<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class retur_products_model extends CI_Model {


	public $table = 'retur_products';
    public $id = 'retur_product_id';
    public $order = 'DESC';

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
	}

	public function json($outlet,$startDate,$endDate)
	{
		$this->datatables->select("
            retur_products.keterangan_retur,
            retur_products.data_created,
            retur_products.qty_retur,
            purchase_products.qty_nota,
            purchase_products.price_nota,
            purchase_products.qty_stok,
            purchase_products.price_stok,
            purchase_products.products_fkid,
            products.name,

            (CASE WHEN (retur_products.employee_fkid is null)
            then (SELECT name FROM admin WHERE admin_id=retur_products.admin_fkid)
            else(SELECT name FROM employee WHERE employee_id=retur_products.employee_fkid)
            END) AS admin_name,


            purchase.invoice AS purchase_invoice,
            outlets.name AS outlet_name,
            supplier.name AS supplier_name,
            ifnull(pdv.variant_name,'') as variant,
        ");

		$this->datatables->from('retur_products');
        $this->datatables->join('purchase_products', 'purchase_products.purchase_products_id =retur_products.purchase_product_fkid','left');
        $this->datatables->join('products_detail', 'products_detail.product_detail_id =purchase_products.products_fkid','left');
        $this->datatables->join('products_detail_variant pdv', 'pdv.variant_id = products_detail.variant_fkid', 'left');
        $this->datatables->join('products', 'products.product_id =products_detail.product_fkid','left');
        $this->datatables->join('purchase', 'purchase.purchase_id = purchase_products.purchase_fkid', 'left');
        // $this->datatables->join('admin', 'admin.admin_id = retur_products.admin_fkid', 'left');
        $this->datatables->join('outlets', 'outlets.outlet_id = purchase.outlet_fkid', 'left');
        $this->datatables->join('supplier', 'supplier.supplier_id = purchase.supplier_fkid', 'left');
        $this->datatables->where('retur_products.data_status', 'on'); //cari hanya data aktif
        // $this->datatables->where('retur_products.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan yang login
        // $this->datatables->where('purchase_products.retur','yes');
        $this->db->where('retur_products.admin_fkid', $this->session->userdata('admin_id'));
        
        if (!empty($outlet)) {
            $this->datatables->where_in('outlets.outlet_id', $outlet);
        }
        if (!empty($startDate)) {
            $this->datatables->where('retur_products.data_created >=', $startDate);
        }
        if (!empty($endDate)) {
            $this->datatables->where('retur_products.data_created <=', $endDate);
        }
        return $this->datatables->generate();
	}

    //to get stock opname
    function get_for_opname($ingridient_id, $beforeDate, $date){
        $this->db->select('ifnull(SUM(retur_products.qty_retur),0) AS qty');
        $this->datatables->join('purchase_products', 'purchase_products.purchase_products_id =retur_products.purchase_product_fkid','left');
        if($beforeDate != 0){

            $this->db->where("retur_products.data_created > ".$beforeDate."");

        }
        $this->db->where("retur_products.data_created < ".$date."");


        $this->db->where('purchase_products.products_fkid = '.$ingridient_id);
        $query=$this->db->where('retur_products.admin_fkid', $this->session->userdata('admin_id'))->get($this->table);

        $result = array();

        foreach ($query->result() as $row)
        {
            $temp_data = array(
                'qty' => $row->qty
                );
            array_push($result,$temp_data);
        }


        return $result;
    }

		public function fetch_last_insert_id(){
			return $this->db->insert_id();
		}

    function insert($data)
    {
        $data['admin_fkid'] = $this->session->userdata('admin_id');
        $data['employee_fkid'] = ($this->session->userdata('user_type')=='employee') ? $this->session->userdata('user_id') : null;
        $data['data_created'] = current_millis();
        return $this->db->insert($this->table, $data);

    }

    function json_retur($purchase_id) {
        $this->db->select("
            purchase_products.purchase_products_id,
            purchase_products.purchase_fkid,
            purchase_products.unit_fkid_nota,
            purchase_products.qty_nota,
            purchase_products.price_nota,
            purchase_products.unit_fkid_stok,
            purchase_products.qty_stok,
            purchase_products.price_stok,
            purchase_products.data_status,
            purchase_products.products_fkid,
            purchase_products.total,
            purchase_products.discount,
            purchase_products.tot_dis,
            purchase_products.keterangan,
            ifnull(purchase_products.tax,0) as tax,
            purchase_products.tax_type,
            g.name as tax_name,
            purchase_products.depreciation,
            retur_products.qty_retur,
            purchase_products.retur,
            purchase.invoice,
            purchase.hutang,
            retur_products.qty_nota as qtynota_retur,
            retur_products.qty_stok as qtyStok_retur,
            retur_products.tot_dis as totdis_retur,
            retur_products.total as total_retur,
            retur_products.harga_stok as harga_retur,
            retur_products.data_created,
            ifnull(pdv.variant_name,'') as variant,
            unit.name AS unit_name,
            ut.name AS unit_description,
            products.name AS products_name,
            sum(pc.qty) as qty_confrim,
        ");


        $this->db->from('retur_products');
        $this->db->join('purchase_products','retur_products.purchase_product_fkid=purchase_products.purchase_products_id','left');
        $this->db->join('products_detail','products_detail.product_detail_id=purchase_products.products_fkid','left');
        $this->db->join('products_detail_variant pdv', 'pdv.variant_id = products_detail.variant_fkid', 'left');
        $this->db->join('products','products.product_id=products_detail.product_fkid','left');
        $this->db->join('unit', 'unit.unit_id =purchase_products.unit_fkid_nota','left');
        $this->db->join('unit ut', 'ut.unit_id =products.unit_fkid','left');
        $this->db->join('purchase', 'purchase.purchase_id = purchase_products.purchase_fkid', 'left');
        $this->db->join('gratuity g', 'g.gratuity_id = purchase_products.gratuity_fkid', 'left');
        $this->db->join('purchase_confrim pc','pc.purchase_product_fkid=purchase_products.purchase_products_id');
        $this->db->where('purchase_products.data_status', 'on'); //cari hanya data aktif
        $this->db->where('purchase.purchase_id', $purchase_id); //cari berdasarkan purchase_id
        $this->db->group_by('retur_products.retur_product_id');
        $this->db->order_by('products.name', 'asc');
        return $this->db->get()->result_array();
    }

}

/* End of file Retur_model.php */
/* Location: ./application/models/purchase/retur/Retur_model.php */
