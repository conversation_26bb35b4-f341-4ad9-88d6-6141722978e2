<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class M_purchasing_product extends CI_Model {

    
    public function __construct()
    {
        parent::__construct();
        //Do your magic here
    }
    
    public function data_purchase($param)
    {
        $this->datatables->select("
            purchase.purchase_id,
            purchase.invoice,
            purchase.keterangan,
            purchase.bayar,
            purchase.grand_total,
            purchase.hutang,
            purchase.status_lunas,
            purchase.outlet_fkid,
            purchase.supplier_fkid, 
            purchase.employee_fkid, 
            purchase.admin_fkid,
            purchase.jatuh_tempo,
            purchase.date_purchase as data_created, 
            purchase.data_status,
            purchase.sub_total,
            purchase.discount_total,
            purchase.type_discount,
            ifnull((select sum(nominal) from debt_payment WHERE purchase_fkid=purchase.purchase_id), 0) as debt_payment,
            ifnull((select sum(nominal) from purchase_refund WHERE purchase_fkid=purchase.purchase_id), 0) as refund,

            ifnull(sum((select sum(tot_dis) from retur_products where purchase_product_fkid = pp.purchase_products_id)),0) as total_retur,

            (CASE WHEN (purchase.employee_fkid is not null)
            THEN (SELECT name FROM employee WHERE employee_id = purchase.employee_fkid)
            ELSE (SELECT name FROM admin WHERE admin_id=purchase.admin_fkid )
            END) AS operator,

            outlets.name AS outlet_name,
            supplier.name AS supplier_name,
            purchase.user_input AS admin_name,
            (select name from shift where shift_id=purchase.shift_fkid) as shift_name,

            ifnull(sum((select sum(qty_arive) from purchase_confrim where purchase_product_fkid = pp.purchase_products_id)),0) as arive,

            sum(pp.qty_stok) as qty_stok

        ")
        ->from('purchase')
        ->join('purchase_products pp ', 'pp.purchase_fkid = purchase.purchase_id', 'left')
        ->join('outlets', 'outlets.outlet_id = purchase.outlet_fkid', 'left')
        ->join('supplier', 'supplier.supplier_id = purchase.supplier_fkid', 'left')
        ->where('purchase.data_status', 'on') //cari hanya data aktif
        ->where('purchase.admin_fkid', $this->session->userdata('admin_id')) //ambil berdasarkan yang login
        ->where('purchase.purchase_type','product');


        if (!empty($param['outlet'])) {
            $this->datatables->where("outlets.outlet_id in (".$param['outlet'].")");
        }

        if (!empty($param['start_date'])) {
            $this->datatables->where("purchase.date_purchase >=", $param['start_date']);
        }

        if (!empty($param['end_date'])) {
            $this->datatables->where("purchase.date_purchase <=", $param['end_date']);
        } 

        $this->datatables->group_by('purchase.purchase_id');

        // Calculate totals for all filtered records
        $result = $this->datatables->generate();
        $data = json_decode($result, true);

        // Get raw query without limit/offset for totals
        $total_query = $this->db->select("
            SUM(grand_total) as total_grand_total,
            SUM(bayar + IFNULL((SELECT SUM(nominal) FROM debt_payment WHERE purchase_fkid=purchase.purchase_id), 0)) as total_bayar,
            SUM(IFNULL((SELECT SUM(tot_dis) FROM retur_products WHERE purchase_product_fkid IN (SELECT purchase_products_id FROM purchase_products WHERE purchase_fkid=purchase.purchase_id)), 0)) as total_retur,
            SUM(hutang) as total_hutang,
            SUM(
                CASE 
                    WHEN (hutang - IFNULL((SELECT SUM(tot_dis) FROM retur_products WHERE purchase_product_fkid IN (SELECT purchase_products_id FROM purchase_products WHERE purchase_fkid=purchase.purchase_id)), 0)) > 0 
                    THEN 0
                    ELSE ABS(hutang - IFNULL((SELECT SUM(tot_dis) FROM retur_products WHERE purchase_product_fkid IN (SELECT purchase_products_id FROM purchase_products WHERE purchase_fkid=purchase.purchase_id)), 0))
                END
            ) as total_piutang
        ")
        ->from('purchase')
        ->join('outlets', 'outlets.outlet_id = purchase.outlet_fkid', 'left')
        ->where('purchase.data_status', 'on')
        ->where('purchase.admin_fkid', $this->session->userdata('admin_id'))
        ->where('purchase.purchase_type', 'product');

        if (!empty($param['outlet'])) {
            $this->db->where("outlets.outlet_id in (".$param['outlet'].")");
        }

        if (!empty($param['start_date'])) {
            $this->db->where("purchase.date_purchase >=", $param['start_date']);
        }

        if (!empty($param['end_date'])) {
            $this->db->where("purchase.date_purchase <=", $param['end_date']);
        }

        // Apply search filter to totals query if searchValue is provided
        if (!empty($param['searchValue'])) {
            $searchValue = $param['searchValue'];
            
            // Create a subquery to get the IDs of purchases that match the search criteria
            $this->db->where("purchase.purchase_id IN (
                SELECT p.purchase_id FROM purchase p
                LEFT JOIN purchase_products pp ON pp.purchase_fkid = p.purchase_id
                LEFT JOIN outlets o ON o.outlet_id = p.outlet_fkid
                LEFT JOIN supplier s ON s.supplier_id = p.supplier_fkid
                WHERE p.data_status = 'on'
                AND p.admin_fkid = " . $this->session->userdata('admin_id') . "
                AND p.purchase_type = 'product'
                AND (
                    p.invoice LIKE '%$searchValue%'
                    OR o.name LIKE '%$searchValue%'
                    OR s.name LIKE '%$searchValue%'
                    OR DATE_FORMAT(FROM_UNIXTIME(p.date_purchase/1000), '%d/%m/%Y') LIKE '%$searchValue%'
                )
            )");
        }

        $totals = $this->db->get()->row_array();
        
        // Add totals to the response
        $data['totals'] = $totals;
        
        return json_encode($data);
    }

    public function data_product($param)
    {
        $this->db->select("
            products_detail.product_detail_id,
            concat(products.name,' ',ifnull(pdv.variant_name,'')) as product_name,
            products_detail.price_buy as price_buy,
            outlets.name as outlet_name,
            outlets.outlet_id,
            unit.name as unit_name,
            unit.unit_id,
            products.product_id           
        ")
        ->from('products')
        ->join('products_detail', 'products.product_id = products_detail.product_fkid', 'left')  
        ->join('outlets', 'outlets.outlet_id = products_detail.outlet_fkid', 'left')  
        ->join('unit', 'unit.unit_id = products.unit_fkid', 'left')
        ->join('products_detail_variant pdv', 'pdv.variant_id = products_detail.variant_fkid', 'left')
        ->where('products.data_status', 'on') //cari hanya data akti
        ->where('products_detail.outlet_fkid', $param['outlet'])
        // ->where("concat(products.name,ifnull(pdv.variant_name,'')) like '%".$param['key']."%'",) //cari hanya data akti
        ->like("CONCAT(products.name, IFNULL(pdv.variant_name, ''))", $param['key'])        
        ->where('products.admin_fkid', $this->session->userdata('admin_id')) 
        // ->where('products.stock_management',1) 
        ->where('products_detail.data_status','on');
        if ($param['type'] == 'assets') {
            $this->db->where('catalogue_type','asset');
        } 
        $result = $this->db->get()->result_array();
        file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> query: " . preg_replace('/[\n\r\t]/', ' ', $this->db->last_query()) . "\n");
        return $result;
    }

    public function data_purchase_retur($id)
    {
        return $this->db->select('
            purchase.purchase_id,
            purchase.invoice,
            purchase.keterangan,
            (select purchase.bayar+ifnull((select sum(nominal) from debt_payment where purchase_fkid = purchase.purchase_id),0)) as bayar,
            purchase.grand_total,
            purchase.hutang,
            purchase.status_lunas,
            purchase.outlet_fkid,
            purchase.supplier_fkid, 
            purchase.employee_fkid, 
            purchase.admin_fkid,
            purchase.jatuh_tempo,
            purchase.data_created, 
            purchase.data_status,
            purchase.sub_total,
            purchase.date_purchase,

            ifnull(sum((select sum(tot_dis) from retur_products where purchase_product_fkid = pp.purchase_products_id)),0) as total_retur,
            ifnull((select sum(nominal) from purchase_refund WHERE purchase_fkid=purchase.purchase_id), 0) as refund,

            (CASE WHEN (purchase.employee_fkid is not null)
            THEN (SELECT name FROM employee WHERE employee_id = purchase.employee_fkid)
            ELSE (SELECT name FROM admin WHERE admin_id=purchase.admin_fkid )
            END) AS operator,

            outlets.name AS outlet_name,
            supplier.name AS supplier_name,
            purchase.user_input AS admin_name,
            (select name from shift where shift_id=purchase.shift_fkid) as shift_name,
            ifnull((select sum(nominal) from debt_payment where purchase_fkid = purchase.purchase_id),0) as debt_payment
        ')
        ->from('purchase')
        ->join('purchase_products pp ', 'pp.purchase_fkid = purchase.purchase_id', 'left')
        ->join('outlets', 'outlets.outlet_id = purchase.outlet_fkid', 'left')
        ->join('supplier', 'supplier.supplier_id = purchase.supplier_fkid', 'left')
        ->join('debt_payment dp','dp.purchase_fkid=purchase.purchase_id','left')
        ->where('purchase.data_status', 'on') //cari hanya data aktif
        ->where('purchase.purchase_id',$id) //cari hanya data aktif
        ->where('purchase.admin_fkid', $this->session->userdata('admin_id')) //ambil berdasarkan yang login
        ->get()->row();
        
        // return $this->db->get_where('purchase',['purchase_id'=>$id,'admin_fkid'=>$this->session->userdata('admin_id')])->row();
    }

    public function data_detail_purchase($id)
    {
        return $this->db->select("
            pp.purchase_products_id,
            pp.purchase_fkid,
            pp.unit_fkid_stok,
            round(pp.qty_stok,2) as qty_stok,
            pp.price_stok,
            pp.price_nota,
            pp.data_created,
            pp.products_fkid,
            round(pp.qty_nota,2) as qty_nota,
            pp.discount,
            pp.total,
            pp.tot_dis,
            pp.tax,
            pp.disc_type,
            pp.tax_type,
            pp.keterangan,
            pp.unit_fkid_nota,
            concat(p.name,' ',ifnull(pdv.variant_name,'')) as product_name,
            unit.name as unit,
            ifnull(sum((select round(sum(qty_arive),2) from purchase_confrim where purchase_product_fkid=pp.purchase_products_id)),0) as qty_arive,
            ifnull(sum((select round(sum(qty_retur),2) from retur_products where purchase_product_fkid=pp.purchase_products_id)),0) as qty_retur,
            (select name from unit where unit_id = pp.unit_fkid_nota) as unit_nota,
            (select name from unit where unit_id = pp.unit_fkid_stok) as unit_stock,
            (select name from gratuity where gratuity_id = pp.gratuity_fkid) as tax_name
        ")
        ->from("purchase_products pp")
        ->join("products_detail pd","pd.product_detail_id=pp.products_fkid","left")
        ->join('products p', 'p.product_id = pd.product_fkid', 'left')  
        ->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left')
        ->join('unit', 'unit.unit_id = p.unit_fkid', 'left')
        ->where("pp.purchase_fkid",$id)
        ->group_by('pp.purchase_products_id')
        ->get()->result_array();
    }



    public function data_retur($id)
    {
        return $this->db->get_where('retur_products',["purchase_product_fkid"=>$id])->result_array();   
    }

    public function data_confirm($id)
    {
        return $this->db->order_by('date_created','asc')
        ->get_where('purchase_confrim',["purchase_product_fkid"=>$id])->result_array();
    }

    public function data_purchase_by_id($purchase_id)
    {
        return $this->db->select("
            purchase.*,
            ifnull((select sum(nominal) from debt_payment WHERE purchase_fkid=purchase.purchase_id), 0) as debt_payment,
            ifnull((select sum(nominal) from purchase_refund WHERE purchase_fkid=purchase.purchase_id), 0) as refund,

            ifnull(sum((select sum(tot_dis) from retur_products where purchase_product_fkid = pp.purchase_products_id)),0) as total_retur,

            (CASE WHEN (purchase.employee_fkid is not null)
            THEN (SELECT name FROM employee WHERE employee_id = purchase.employee_fkid)
            ELSE (SELECT name FROM admin WHERE admin_id=purchase.admin_fkid )
            END) AS operator,

            outlets.name AS outlet_name,
            supplier.name AS supplier_name,
            purchase.user_input AS admin_name,
            (select name from shift where shift_id=purchase.shift_fkid) as shift_name,

            ifnull(sum((select sum(qty_arive) from purchase_confrim where purchase_product_fkid = pp.purchase_products_id)),0) as arive,

            sum(pp.qty_stok) as qty_stok

        ")
        ->from('purchase')
        ->join('purchase_products pp ', 'pp.purchase_fkid = purchase.purchase_id', 'left')
        ->join('outlets', 'outlets.outlet_id = purchase.outlet_fkid', 'left')
        ->join('supplier', 'supplier.supplier_id = purchase.supplier_fkid', 'left')
        ->where('purchase.data_status', 'on') //cari hanya data aktif
        ->where('purchase.admin_fkid', $this->session->userdata('admin_id')) //ambil berdasarkan yang login
        ->where('purchase.purchase_type','product')
        ->where('purchase.purchase_id',$purchase_id)
        ->group_by('purchase.purchase_id')
        ->get()->row();
    }

    
    public function data_detail_product($outlet_id)
    {
        $this->db->select("
            products_detail.product_detail_id,
            concat(products.name,' ',ifnull(pdv.variant_name,'')) as product_name,
            products_detail.price_buy as price_buy,
            outlets.name as outlet_name,
            outlets.outlet_id,
            unit.name as unit_name,
            unit.unit_id,
            products.product_id           
        ")
        ->from('products')
        ->join('products_detail', 'products.product_id = products_detail.product_fkid', 'left')  
        ->join('outlets', 'outlets.outlet_id = products_detail.outlet_fkid', 'left')  
        ->join('unit', 'unit.unit_id = products.unit_fkid', 'left')
        ->join('products_detail_variant pdv', 'pdv.variant_id = products_detail.variant_fkid', 'left')
        ->where('products.data_status', 'on') //cari hanya data akti
        ->where('products_detail.outlet_fkid', $outlet_id)
        ->where('products.admin_fkid', $this->session->userdata('admin_id')) 
        // ->where('products.stock_management',1) 
        ->where('products_detail.data_status','on');
        return $this->db->get()->result_array();
    }
    
}

/* End of file M_purchasing.php */
