<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Retur extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman 
        $this->load->model('outlet/Outlets_model'); //form
        $this->load->model('retur/Retur_products_model');
    }

	public function index()
	{
		$link = 'purchasing/retur/'; //URL dengan slash
        $data = array(
            'kolomID' => 'retur_ingridient_id', //nama kolom primary key pada tabel
            'ajaxActionDelete' => $link.'delete/',
            'ajaxActionCreate' => $link.'action/create',
            'ajaxActionUpdate' => $link.'action/update',
            'ajaxActiongetDataRetur' => $link.'retur/' //ambil data yang akan diedit
            );
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();

        // $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('themes/plugins/hot/moment/moment.js');
        $this->template->js('themes/plugins/axios/axios.min.js');
        $this->template->js('themes/js/custom.js');

        $data['page_title'] = 'Retur';
        $this->template->view('retur/retur_v2', $data);
	}

	public function json()
    {
        $timeZone = $this->input->post('timeZone');
        $outlet = $this->input->post('outlet');
        $startDate = $this->input->post('startDate');
        $endDate = $this->input->post('endDate');
        
        /* custom json output  start */
        $jsondata = $this->Retur_products_model->json($outlet,$startDate,$endDate); //ambil data json
        
        echo $jsondata;
    }

    public function json_retur($type)
    {
        header('Content-Type: application/json');
        $jsondata = $this->Retur_products_model->json_retur($type); //ambil data json
        // echo $this->db->last_query();;die();

        $dataArray = array();
        foreach ($jsondata as $a) { 
            
            $id = $a['purchase_products_id'];
            $purchase_fkid = htmlentities($a['purchase_fkid']); //encoding string
            $unit_fkid_nota = htmlentities($a['unit_fkid_nota']); //encoding string
            $qty_nota =htmlentities($a['qty_nota']); //encoding string
            $price_nota = htmlentities($a['price_nota']); //encoding string
            $unit_fkid_stok = htmlentities($a['unit_fkid_stok']); //encoding string
            $qty_stok = "<span id='qty_stokretur'>".($a['qty_stok'])."</span>";
            $price_stok ="<span id='price_stokretur'>".formatUang(formatDesimal($a['price_stok'],2))."</span>";
            $data_status = htmlentities($a['data_status']); //encoding string
            $products_fkid = htmlentities($a['products_fkid']); //encoding string
            $total = "<span id='totalretur'>".$a['total']."</span>";//encoding string
            $discount = htmlentities($a['discount']); //encoding string
            $tot_dis = "<span id='tot_disretur'>".$a['tot_dis']."</span>";//encoding string
            $retur = htmlentities($a['retur']); //encoding string
            $invoice = htmlentities($a['invoice']); //encoding string
            $hutang = htmlentities($a['hutang']); //encoding string
            $unit_name = htmlentities($a['unit_name']); //encoding string
            $unit_description = htmlentities($a['unit_description']); //encoding string
            $products_name = htmlentities($a['products_name']); //encoding string 
            $qty_retur = htmlentities($a['qty_retur']); //encoding string 
            $qtynota_retur = htmlentities($a['qtynota_retur']); //encoding string 
            $qtyStok_retur = htmlentities($a['qtyStok_retur']); //encoding string 
            $totdis_retur = htmlentities($a['totdis_retur']); //encoding string 
            $total_retur = htmlentities($a['total_retur']); //encoding string 
            $harga_retur = htmlentities($a['harga_retur']); //encoding string 
            $tax = $a['tax_type'] != "percentage" ? $a['tax'] : $a['tax']."%";
            $total_pajak =" <span id='pajak_retur' data-type='".$a['tax_type']."' data-val='".$a['tax']."'>".$a['tax_name']." ".$tax."</span>";
            
            $temp_data = array(
                'purchase_products_id' => $id,
                'date_created' => millis_to_localtime('Y-m-d H:i:s',$a['data_created']),
                'purchase_fkid' => $purchase_fkid,
                'unit_fkid_nota' => $unit_fkid_nota,
                'qty_nota' => $qty_nota,
                'tot_dis' => $tot_dis,
                'price_nota' => $price_nota,
                'unit_fkid_stok' => $unit_fkid_stok, 
                'qty_stok' => $qty_stok,
                'price_stok' => $price_stok,
                'data_status' => $data_status,
                'products_fkid' => $products_fkid,
                'total' => $total,
                'discount' => $discount,
                'retur' => $retur,
                'invoice' => $invoice,
                'hutang' => $hutang,
                'unit_name' => $unit_name,
                'unit_description' => $unit_description,
                'products_name' => $products_name." ".$a['variant'],
                'qty_retur' => $qty_retur,
                'qtynota_retur' => $qtynota_retur,
                'qtyStok_retur' => $qtyStok_retur,
                'totdis_retur' => $totdis_retur,
                'total_retur' => $total_retur,
                'harga_retur' => $harga_retur,
                'qty_confrimRetur' => $a['qty_confrim'],
                'keterangan' => $a['keterangan'],
                'pajak' => $total_pajak,
                'depreciation' => $a['depreciation'].'/Month',
                );
            array_push($dataArray, $temp_data);
        }

        //remake json data
        $draw_json = array(
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json); 
        /* custom json output  end */
    }

}

/* End of file Retur.php */
/* Location: ./application/controllers/purchase/purchase/Retur.php */