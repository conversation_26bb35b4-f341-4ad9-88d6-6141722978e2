<?php
defined('BASEPATH') OR exit('No direct script access allowed');

# PURCHASE
$route['purchase']					= 'navigation/lv1';
$route['purchase/purchase']			= 'navigation/lv2';
$route['purchase/purchase_detail']	= 'navigation/lv2';
$route['purchase/debt']				= 'navigation/lv2';
$route['purchase/purchase_order']	= 'purchase_order';
$route['purchase/purchase_import']	= 'purchase_export';
$route['purchase/retur']	= 'purchasing/retur';
$route['purchase/purchasing_product']	= 'purchasing/purchasing_product';
$route['purchase/purchasing_product/form_purchase']	= 'purchasing/purchasing_product/form_purchase';
$route['purchase/confirm_detail']	= 'purchasing/Purchase_confrim';
$route['purchase/purchasing_product/retur/(:any)'] = 'purchasing/purchasing_product/retur/$1';
$route['purchase/purchasing_product/confirm/(:any)'] = 'purchasing/purchasing_product/confirm/$1';
$route['purchase/purchasing_product/edit/(:any)']	= 'purchasing/purchasing_product/edit/$1';
$route['purchase/purchasing_product/delete']	= 'purchasing/purchasing_product/delete';

#adding this somehow fix error 
$route['purchase/transfer']	= 'transfer/purchase_transfer';


# MAINTENANCE
if (ENVIRONMENT!='production') {
	$route['purchase/maintenance(.*)']			= 'maintenance$1';
}else{
	$route['maintenance(.*)']					= 'error404';
}
