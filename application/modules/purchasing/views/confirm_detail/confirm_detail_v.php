<style type="text/css">
.tiptext {
    border: 1px #333 solid;
    padding:5px;
    width:100px;
}
.description {
    display:none;
    position:absolute;
    border:1px solid #000;
    padding: 5px;
    margin: 10px 0 0 -10px;
    background: #000;
    color: #fff;
    z-index: 9999;

}
</style>
<div class="container-fluid" id="app">
	<!--table-->	
	<div class="content-uniq">
		<div class="row">
			<div class="col-sm-6">
				<h4><b>History Penerimaan Barang</b></h4>
			</div>
			<div class="col-sm-6">
				<div class="btn-group pull-right">
					<button class="btn btn-primary btn-block btn-apply" type="button" @click="onApply">Apply</button>                               
				</div>
				<div class="btn-group pull-right">
					<select class="form-control btn btn-info outletSelect" v-model="filter.outlet" v-select="filter.outlet" id="outlet-id" multiple>
						<?php foreach ($form_select_outlet as $a): ?>                   
							<option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
						<?php endforeach ?>                
					</select>                                   
				</div>
				<div class="btn-group pull-right">
					<button type="button" class="btn btn-info daterange" id="date-purchase" >
						<span> 
							<i class="glyphicon glyphicon-calendar"></i> Date
						</span>
						<i class="caret"></i>
					</button>
				</div>
				
			</div>
		</div>
		<div class="row">
			<div class="col-sm-12">
				<table id="tableInConfrim" class="table table-responsive table-report">
					<thead>
						<tr>
							<th>Date Purchase</th>
							<th>Date Confirm</th>
							<th>Invoice</th>
							<th>Supplier</th>
							<th>Outlet</th>
							<th>Product Name</th>
							<th>Qty Nota</th>
							<th>Price Nota</th>
							<th>Sub Total</th>
							<th>QTY</th>
							<th>Harga</th>
							<th>Total</th>
							<th>QTY Arrive</th>
							<th>Retur</th>
							<th>QTY Confirm</th>
							<th>QTY Not Confirm</th>
							<th>User Confirm</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>		
	</div>
</div>


<script>
	var vm = new Vue({
		el : "#app",
		data:{
			filter:{
				startDate:'',
				endDate:'',
				outlet:[]
			}
		},
		methods: {
			datatable(){
				$(function () {
					$("#tableInConfrim").DataTable({
						// dom: 'l<"toolbar">frtip', //buat custom toolbar
						
						// processing: true,
						// serverSide: true,
						// responsive: false,
						destroy : true,
                        serverSide: true,
						ajax: {
							url: "<?=$ajaxActionJsonInConfrim?>",
							type: "POST",
							data:vm.filter
						},
						columns: [
							{"data": "date_purchase"},
							{"data": "date"},
							{"data": "invoice"},
							{"data": "suplier"},
							{"data": "outlet"},
							{
								"data": "product_name",
								"render": function(data, type, row) {
									return row.product_name+' '+ row.variant
								}
							},
							{
								"orderable": false,								
								"searchable": false,
								"data": "qty_nota"
							},
							{
								"orderable": false,								
								"searchable": false,
								"data": "price_nota"
							},
							{
								"orderable": false,								
								"searchable": false,
								"data": "tot_dis"
							},
							{
								"orderable": false,								
								"searchable": false,
								"data": "qty_stock"
							},
							{
								"orderable": false,								
								"searchable": false,
								"data": "price_stock"
							},
							{"data": "total"},
							{"data": "qty_arive"},
							{"data": "retur"},
							{
								"data": "qty_confrim",
								"orderable": false,								
								"searchable": false,
							},
							{"data": "qty_notConfrim"},
							{
								"orderable": false,	
								"searchable": false,
								"data": "admin_name"
							},
	
						],order: [[ 1, "asc" ]],
					});
				});
			},
			onApply(){
				this.datatable()
			}
		},
		mounted() {
            setTimeout(() => {
                vm.filter.startDate = $('#date-purchase').data('daterangepicker').startDate._d.valueOf()
                vm.filter.endDate = $('#date-purchase').data('daterangepicker').endDate._d.valueOf()
                vm.datatable()
            }, 300);
        },
	})
</script>

<script type="text/javascript">
	$(document).ready(function() {
		$('#date-purchase').on('apply.daterangepicker', function(ev, val) {
			$(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
			vm.filter.startDate = val.startDate.valueOf()
			vm.filter.endDate = val.endDate.valueOf()
		});

		
	}); //document end
</script>


