<style>
    
</style>
<div id="app">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-4">
                   
                </div>
                <div class="col-sm-8">
                    <div class="pull-right">
                        <div class="btn-group pull-right">
                            <button class="btn btn-primary btn-block btn-apply" type="button"
                                @click="onApply">Apply</button>
                        </div>
                        <div class="btn-group pull-right">
                            <select class="form-control btn btn-info outletSelect" v-model="param.outlet"
                                v-select="param.outlet" id="outlet-id" multiple>
                                <?php foreach ($form_select_outlet as $a): ?>
                                    <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                        <div class="btn-group pull-right">
                            <button type="button" class="btn btn-info daterange" id="date">
                                <span>
                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                </span>
                                <i class="caret"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-sm-8">
                    <div class="btn-group pull-right">

                    </div>
                </div>
            </div>
            <hr>
        </div>
    </div>
    <div class="container-fluid">
        <div class="content-uniq">
            <table class="table table-responsive table-report" id="mytable" width="100%">
                <thead>
                    <tr>
                        <th width="10px">No</th>
                        <th>Invoice Purchase</th>
                        <th>Tangal Retur</th>
                        <th>Outlet</th>
                        <th>Nama Produk</th>
                        <th>Qty Stock</th>
                        <th>Price stok</th>
                        <th>Qty Retur</th>
                        <th>Supplier</th>
                        <th>Keterangan</th>
                        <th>User input</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div><!-- /.container-fluid -->
</div>

<script>
    $(function () {
        $('#date').on('apply.daterangepicker', function (ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY") + ' - ' + val.endDate.format("D MMMM YYYY"));
            vm.param.startDate = val.startDate.valueOf()
            vm.param.endDate = val.endDate.valueOf()
        });
    });

    var vm = new Vue({
        el: "#app",
        data: {
            param: {
                outlet: [],
                startDate: '',
                endDate: ''
            },
        },
        methods: {
            datatable() {
                $(function () {
                    $("#mytable").DataTable({
                        buttons: [
                            {
                                extend: 'collection',
                                text: 'EXPORT',
                                buttons: [
                                    {
                                        extend: 'pdfHtml5',
                                        orientation: 'landscape',
                                        pageSize: 'A2',
                                        exportOptions: {
                                            columns: [1, ':visible']
                                        }

                                    },
                                    {
                                        extend: 'excelHtml5',
                                        exportOptions: {
                                            format: {
                                                body: function (data, row, column, node) {
                                                    data = $('<p>' + data + '</p>').text();
                                                    return $.isNumeric(data.replace(/[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '')) ? data.replace(/[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '') : data;
                                                }

                                            }
                                        }

                                    },
                                ]
                            }
                        ],
                        serverSide: true,
                        destroy: true,
                        ajax: {
                            type: "POST",
                            url: "<?= base_url('purchasing/retur/json') ?>",
                            data: {
                                outlet: vm.param.outlet,
                                startDate: vm.param.startDate,
                                endDate: vm.param.endDate,
                                timeZone: timezone()
                            }
                        },
                        columns: [
                            {
                                data: null,
                                orderable: false,
                                searchable: false,
                            },
                            {
                                data: "purchase_invoice",
                            },
                            {
                                data: "data_created",
                                render(data, type, row) {
                                    return moment.unix(data / 1000).local().format("DD/MM/YYYY hh:ss")
                                }

                            },
                            {
                                data: "outlet_name",
                            },
                            {
                                data: "name",
                            },
                            {
                                data: "qty_nota",
                            },
                            {
                                data: "price_stok",
                                render(data, type, row) {
                                    return currency(data)
                                }
                            },
                            { data: "qty_retur" },
                            { data: "supplier_name" },
                            { data: "keterangan_retur" },
                            {
                                data: "admin_name",
                                orderable: false,
                                searchable: false,
                            },
                        ],
                        order: [[1, 'asc']],
                    });
                });
            },
            onApply(){
                this.datatable()
            },
        },
        mounted() {
            setTimeout(() => {
                vm.param.startDate = $('#date').data('daterangepicker').startDate._d.valueOf()
                vm.param.endDate = $('#date').data('daterangepicker').endDate._d.valueOf()
                vm.datatable()
            }, 300);
        },
    })
</script>