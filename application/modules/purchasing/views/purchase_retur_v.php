<style>
    .form-control[disabled],
    .form-control[readonly],
    fieldset[disabled] .form-control {
        background-color: #484a4b;
        opacity: 1;
    }
    .select2-search { background-color: #2d2d2d; }
    .select2-search input { background-color: #2d2d2d; }
    .select2-results { background-color: #2d2d2d }
    /* .select2-choice { background-color: #2d2d2d !important; } */
    .select2-container--default .select2-selection--single .select2-selection__rendered { color: #fff; }
    .select2-container--default .select2-selection--single{
        background-color: #2d2d2d;
        color: #fff;
        border-color: #6c757d;
    }
    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #424242;
    }

    .select2-container--default.select2-container--disabled .select2-selection--single {
        background-color: #464545;
    }
</style>
<div id="form-pengembalian">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-4">
                    <H4><i class="glyphicon glyphicon-log-out"></i> Form Pengembalian Barang</h4>
                </div>
            </div>
            <div class="row">
                <form id="form-menu" novalidate>
                    <div class="col-sm-6">
                        <div class="form-group row mb-1">
                            <label for="invoice" class="col-sm-3 col-form-label col-form-label-sm text-right">Invoice*</label>
                            <div class="col-sm-8">
                                <span class="form-control form-dark input-line input-sm"> <?= $purchase->invoice ?></span>
                            </div>
                        </div>
                        <div class="form-group row mb-1">
                            <label for="outlet" class="col-sm-3 col-form-label col-form-label-sm text-right">
                                Outlet*
                            </label>
                            <div class="col-sm-8">
                                <span class="form-control form-dark input-line input-sm"> <?= $purchase->outlet_name ?></span>
                            </div>
                        </div>
                        <div class="form-group row mb-1">
                            <label for="shift" class="col-sm-3 col-form-label col-form-label-sm text-right">
                                Shift*
                            </label>
                            <div class="col-sm-8">
                                <span class="form-control form-dark input-line input-sm"> <?= $purchase->shift_name ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group row mb-1">
                            <label for="supplier" class="col-sm-2 col-form-label col-form-label-sm text-right">
                                Supplier*
                            </label>
                            <div class="col-sm-8">
                                <span class="form-control form-dark input-line input-sm"> <?= $purchase->supplier_name ?></span>
                            </div>
                        </div>
                        <div class="form-group row mb-1">
                            <label for="tgl" class="col-sm-2 col-form-label col-form-label-sm text-right">Tanggal*</label>
                            <div class="col-sm-8">
                                <span class="form-control form-dark input-line input-sm">{{date(<?=$purchase->data_created?>)}}</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <hr style="margin-top:5px">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-report table-striped table-reprot" id="table-retur" style="font-size: 14px;">
                        <thead>
                            <tr>
                                <th width="20px" class="text-center">No</th>
                                <th>Nama Produk</th>
                                <th>Satuan</th>
                                <th>Qty Stok</th>
                                <th>Harga Stok</th>
                                <th>Ppn</th>
                                <th>Qty Diterima</th>
                                <th>Qty Retur</th>
                                <th>Sisa</th>
                                <th>Retur</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="master.product.length ==0">
                                <td colspan="11" class="text-center">Tidak Ada Data Pada Tabel Ini</td>
                            </tr>
                            
                            <tr v-for="(i,idx) in master.product">
                                <td>{{idx+1}}</td>
                                <td>{{i.product_name}}</td>
                                <td>{{i.unit}}</td>
                                <td>{{number(parseFloat(i.qty_stok))}}</td>
                                <td>{{number(i.price_stok)}}</td>
                                <td>{{i.tax_name}}({{i.tax}}{{i.tax_type=='percentage'?'%':''}})</td>
                                <td>{{number(parseFloat(i.qty_arive))}}</td>
                                <td>{{number(i.qty_retur==null?0:parseFloat(i.qty_retur))}}</td>
                                <td>{{number(i.qty_arive-i.qty_retur)}}</td>
                                <td>
                                    <input :disabled="(i.qty_arive-i.qty_retur)==0" type="text" class="form-control form-dark input-sm text-right" :disabled="i.qty_stok==0" v-model="master.product[idx].form.qty" v-number="master.product[idx].form.qty" placeholder="Qty Retur" @change="subTotal()" />
                                    <small>{{master.product[idx].form.error}}</small>
                                </td>
                                <td>
                                    <textarea :disabled="(i.qty_arive-i.qty_retur)==0" type="text" class="form-control form-dark input-sm" style="height: 30px;" :disabled="i.qty_stok==0" v-model="master.product[idx].form.keterangan" placeholder="Keterangan"> </textarea>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row container-fluid">
                <div class="col-sm-5 col-sm-offset-7">
                    <div class="form-group row mb-1">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Grand Total </label>
                        <div class="col-sm-9">
                            <span class="form-control form-dark input-line input-sm text-right">{{number(<?= $purchase->grand_total ?>)}}</span>
                        </div>
                    </div>

                    <div class="form-group row mb-1">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Grand Total Retur</label>
                        <div class="col-sm-9">
                            <span class="form-control form-dark input-line input-sm text-right">{{number(master.subTotal)}}</span>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Pembayaran</label>
                        <div class="col-sm-9">
                            <span class="form-control form-dark input-line input-sm text-right">{{number(<?= $purchase->bayar ?>)}}</span>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Sisa Tagihan</label>
                        <div class="col-sm-9">
                            <span class="form-control form-dark input-line input-sm text-right">{{number(master.sisaTagihan)}}</span>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="master.kreditMemo>0">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Debit Memo</label>
                        <div class="col-sm-9">
                            <span class="form-control form-dark input-line input-sm text-right">{{number(master.kreditMemo)}}</span>
                        </div>
                    </div>
                    <!-- <div class="form-group row mb-1" v-if="master.kreditMemo>0">
                        <label class="col-sm-11 col-form-label col-form-label-sm text-right">Terima Pengembalian Uang</label>
                        <div class="col-sm-1">
                            <input type="checkbox" name="kembali" id="kembali" v-model='master.pengembalian.status'>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="master.pengembalian.status">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Penerimaan Pengambalian
                        </label>
                        <div class="col-sm-9">
                            <select id="bank" :class="['form-control form-dark select2select']" name="payment_media_bank" v-model="master.pengembalian.payment" v-select="master.pengembalian.payment" style="width:100%" data-placeholder="-- Pilih Bank --">
                                <option value="cash" selected>Cash</option>
                                <?php foreach ($form_select_bank as $a) : ?>
                                    <option value="<?= $a->bank_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div> -->


                </div>
            </div>
            <div class="row">
                <div class="modal-footer">
                    <a href="<?= base_url() ?>purchase/purchasing_product">
                        <button type="button" class="btn btn-default">Kembali</button>
                    </a>
                    <button type="button" class="btn btn-primary" @click="onSubmit()">Simpan</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var sisa = 0
    var saldo = "<?= $purchase->grand_total - $purchase->bayar - $purchase->total_retur ?>"
    saldo > 0 ? sisa = saldo : sisa = saldo * -1

    var refund = "<?=$purchase->refund?>"

    // console.log(sisa);

    var vm = new Vue({
        el: "#form-pengembalian",
        data: {
            master: {
                product: [],
                subTotal: 0,
                sisaTagihan: sisa > 0 ? sisa : 0,
                kreditMemo: sisa < 0 ? sisa : 0,
                purchase_id: "<?= $purchase->purchase_id ?>",
                invoice: "<?= $purchase->invoice ?>",
                outlet_id: "<?= $purchase->outlet_fkid ?>",
                pengembalian: {
                    status: false,
                    payment: ''
                },
            },
            detail: ""
        },
        methods: {
            appendForm(res, idx) {
                // console.log(res);
                var form = {
                    qty: '',
                    keterangan: '',
                    id_detail: res.purchase_products_id,
                    sisa: res.sisa_qty,
                    hpp: res.price_stok,
                    tax: res.tax,
                    taxType: res.tax_type,

                    error: ''
                }
                this.master.product[idx]['form'] = form
            },
            getProduct() {
                $.ajax({
                    type: "get",
                    url: "<?= base_url() ?>purchasing/purchasing_product/detail_purchase",
                    data: {
                        id: <?= $purchase->purchase_id ?>
                    },
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success: function(response) {
                        loading.hide()
                        vm.master.product = response
                        // vm.appendForm(response)

                        for (const i in response) {
                            // vm.appendForm(response[i].purchase_products_id,i,response[i].sisa_qty,response[i].price_stok)
                            vm.appendForm(response[i], i)
                        }

                    },
                    error(err) {
                        loading.hide()
                        Alert.error("error", "Gagal Memuat Produk")
                        console.log(err);
                    }

                });
            },
            number(param) {
                return number(param)
            },
            date(param) {
                return moment.unix(param / 1000).local().format("DD/MM/YYYY HH:mm:ss")
            },
            onSubmit() {
                // maping data
                var data = []
                var dataSend = {}
                for (var key in this.master.product) {
                    if (this.master.product[key].hasOwnProperty('form')) {
                        this.master.product[key].form.qty = clearFormat(this.master.product[key].form.qty)
                        data.push(this.master.product[key].form)
                    }

                    var sisa = this.master.product[key].sisa_qty
                    // return console.log(sisa);
                    if (this.master.product[key].hasOwnProperty('form')) {
                        if (this.master.product[key].form.qty != "" && this.master.product[key].form.qty > sisa) {
                            this.master.product[key].form.error = "Melebihi Total Qty yang Ada"
                            return Alert.warning("Opps", "Melebihi jumalah yang dapat diretur")
                        } else {
                            this.master.product[key].form.error = ""
                        }
                    }
                }
                dataSend.detail = data
                dataSend.subRetur = this.master.subTotal
                dataSend.sisaTagihan = this.master.sisaTagihan
                dataSend.kreditMemo = this.master.kreditMemo
                dataSend.purchase_id = this.master.purchase_id
                dataSend.invoice = this.master.invoice
                dataSend.outlet_id = this.master.outlet_id
                dataSend.pengembalian = this.master.pengembalian.payment

                // return console.log(dataSend);
                //ajax insert
                $.ajax({
                    type: "post",
                    url: "<?= base_url() ?>purchasing/purchasing_product/retur_save",
                    data: {
                        dataSend
                    },
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success: function(res) {
                        loading.hide()
                        Alert.success("Success", "Data Berhasil Disimpan");
                        location.href = "<?= base_url() ?>purchase/purchasing_product"
                    },
                    error(err) {
                        loading.hide();
                        Alert.error("Error", "Gagal Menyimpan Data");
                        console.log(err);
                    }
                });
            },
            validateQty(idx) {
                var result = true
                if (this.master.product[idx].form) {
                    if (this.master.product[idx].form.qty > this.master.product[idx].sisa_qty) {
                        this.master.product[idx].form.error = "Melebihi Total Qty yang Diterima"
                        return result = false
                    } else {
                        this.mastter.product[idx].form.error = ""
                    }
                }

                // return result

            },
            subTotal() {
                var subTotal = 0
                var totalRetur = "<?= $purchase->total_retur ?>"
                // console.log("tes");
                var total = 0;
                for (let i in this.master.product) {

                    if (this.master.product[i].form.qty != "") {
                        console.log(clearFormat(this.master.product[i].form.qty))
                        subTotal += this.master.product[i].price_stok * clearFormat(this.master.product[i].form.qty)
                        total = this.master.product[i].price_stok * clearFormat(this.master.product[i].form.qty)
                        this.master.product[i].form.tax = total * this.master.product[i].tax / 100
                         var tax = 0;
                        if (this.master.product[i].tax > 0) {
                           
                            if (this.master.product[i].tax_type == 'percentage') {
                                // console.log(this.master.product[i].form.tax/subTotal);
                                // console.log(subTotal);
                                tax += subTotal * this.master.product[i].tax / 100
                            } else {
                                tax += this.master.product[i].tax
                            }
                        }
                    }

                    // console.log(this.master.product[i].form); 


                }

                // console.log(subTotal+'+'+tax);

                // this.master.subTotal = parseInt(totalRetur)+parseInt(subTotal) 
                this.master.subTotal = parseInt(subTotal) + parseInt(tax)

                // console.log('saldo'+saldo);
                // console.log("grand_total=<?=$purchase->grand_total?>");
                // console.log("bayar=<?=$purchase->bayar?>");
                // console.log("total_retur=<?=$purchase->total_retur?>");
                // console.log("refund="+refund);
                if ((saldo - this.master.subTotal) >= 0) {
                    this.master.sisaTagihan = (saldo - this.master.subTotal)
                    this.master.kreditMemo = 0
                } else {
                    this.master.sisaTagihan = 0
                    this.master.kreditMemo = (saldo - this.master.subTotal + parseInt(refund)) * -1
                }

            }

        },
        watch: {
            'master.pengembalian.status'(newval){
                setTimeout(() => {
                    $("#bank").select2();
                }, 200);
            },
            'master.kreditMemo'(newVal){
                if (newVal !=0) {
                    this.master.pengembalian.status = false
                    this.master.pengembalian.payment = ''
                }
            }
        },
        mounted() {
            $(function() {
                vm.getProduct()
                vm.subTotal()
            });

        },
    })
</script>