<?php

defined('BASEPATH') or exit('No direct script access allowed');

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use \PhpOffice\PhpSpreadsheet\Style;

class Stock_card extends Auth_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('report/M_stock_card');
        $this->load->model('outlet/Outlets_model');
    }
    public function index()
    {
        $data['page_title'] = 'Kartu Stok';
        $this->template->js("themes/plugins/numeral/numeral.min.js", 'top');
        $data = [
            'form_select_outlet' => $this->Outlets_model->outlet_employe(),
            'page_title' => 'Stock Card'
        ];
        $this->template->view('report/stock_card_v', $data);
    }

    public function get_data($param_export = null)
    {
        if ($param_export != null) {
            $param = $param_export;
        } else {
            $param = $this->input->post();
        }
        $data = $this->M_stock_card->data_total_v2($param);  
        $data_saldo = $this->get_saldo_v2($param);
        $result = [];
        foreach ($data as $key => $val) {       
            $stock_awal = 0;
            $price_awal = 0;
            $total_price_awal = 0;
            foreach ($data_saldo as $sal) {
                if ($val['product_detail_id'] == $sal['product_detail_id']) {
                    $stock_awal = $sal['stock_in']-$sal['stock_out'];
                    $price_awal = ($sal['stock_in']-$sal['stock_out']) != 0 ? $sal['price'] / ($sal['stock_in']-$sal['stock_out']) : 0;
                    $total_price_awal = $sal['price'];
                }
            } 
            $total_prc_in = $val['stock_in']*$val['price'];
            $total_prc_out = $val['stock_out']*$val['price'];
            $sisa_stock = (float)$stock_awal+(float)$val['stock_in'] - (float)$val['stock_out'];
            $total_price_sisa = $total_price_awal+$total_prc_in-$total_prc_out;
            $tmp = [
                'product_name' => $val['pd_name'],
                'product_detail_fkid' => $val['product_detail_id'],
                'stock_awal' =>$stock_awal,
                'price_awal'=>$price_awal,
                'total_price_awal' =>$total_price_awal,
                'stock_in' => (float)$val['stock_in'],
                'stock_out' => (float)$val['stock_out'],
                'total_price_in' => $total_prc_in,
                'total_price_out' => $total_prc_out,
                'price_in' => $val['stock_in'] != 0 ? $total_prc_in / $val['stock_in'] : 0,
                'price_out' => $val['stock_out'] != 0 ? $total_prc_out / $val['stock_out'] : 0,
                'sisa_stock' => $sisa_stock,
                'total_price_sisa' => $total_price_sisa,
                'total_price' => ($sisa_stock != 0) ? $total_price_sisa / $sisa_stock : 0,
                'pembelian' => $val['acc_pembelian'],
                'penjualan' => $val['acc_penjualan'],
                'persediaan' => $val['acc_persediaan'],
                'outlet_name' => $val['outlet_name']
            ];
            
            array_push($result, $tmp);
        }
        $datatable_result = [
            'draw' => $this->input->post('draw'),
            'data' => $result,
            'recordsFiltered' => count($result),
            'recordsTotal' => count($result)

        ];
        if ($param_export != null) {
            return $result;
        } else {
            return $this->response_json($datatable_result);
        }
    }

    function get_saldo_v2($param) {
        $last_close_book = $this->db->select("*")
            ->from("finance_close_book")
            ->where('admin_fkid', $this->session->userdata('admin_id'))
            ->order_by('end_date', 'desc')
            ->limit(1)
            ->get()->row();

        $param = [
            'startDate' => $last_close_book ? $last_close_book->end_date : 0,
            'endDate' => $param['startDate'], //- (1000 * 60 * 60 * 24),
            'outlet' => $param['outlet']
        ];
        $data = $this->M_stock_card->saldo_v2($param);
        return $data;
    }

    function get_data_detail()
    {
        $param = $this->input->post();
        $data_table = $this->M_stock_card->data_detail($param);
        $total_stock = 0;
        foreach ($data_table as $key => $value) {
            $data_table[$key]['date'] = millis_to_localtime('d/m/y H:i:s', $value['date']);

            // $data_table[$key]['qty_sisa'] = $value['stock_out'];
            // $data_table[$key]['price_sisa'] = $value['price'];
            // $data_table[$key]['total_sisa'] = $value['stock_out']*$value['price'];

            if ($value['origin'] == 'in') {
                $data_table[$key]['price_out'] = 0;
                $data_table[$key]['stock_out'] = 0;
                $data_table[$key]['total_out'] = 0;
                $data_table[$key]['total_in'] = $value['stock_in'] * $value['price'];
                $data_table[$key]['price_in'] = $value['price'];

                $data_table[$key]['qty_sisa'] = $value['stock_in'];
                $data_table[$key]['price_sisa'] = $value['price'];
                $data_table[$key]['total_sisa'] = $value['stock_in'] * $value['price'];
            } else {
                $data_table[$key]['price_in'] = 0;
                $data_table[$key]['stock_in'] = 0;
                $data_table[$key]['total_in'] = 0;
                $data_table[$key]['total_out'] = $value['stock_out'] * $value['price'];
                $data_table[$key]['price_out'] = $value['price'];

                $total_stock += $value['stock_out'];
                $sisa = $value['stock_in'] - $total_stock;
                if ($sisa != 0) {
                    $qty = $value['stock_in'] - $total_stock;
                    $data_table[$key]['qty_sisa'] = $qty;
                    $data_table[$key]['price_sisa'] = $value['price'];
                    $data_table[$key]['total_sisa'] = $qty * $value['price'];
                } else {
                    // $qty = $value['stock_in'] - $total_stock;
                    $data_table[$key]['qty_sisa'] = $data_table[$key + 1]['stock_in'];
                    $data_table[$key]['price_sisa'] = $data_table[$key + 1]['price'];
                    $data_table[$key]['total_sisa'] = $data_table[$key + 1]['stock_in'] * $data_table[$key + 1]['price'];
                    $total_stock = 0;
                }

                // $total_stock -= $value['stock_out'];
            }
        }
        $datatable_result = [
            'draw' => $this->input->post('draw'),
            'data' => $data_table,
            'recordsFiltered' => count($data_table),
            'recordsTotal' => count($data_table)

        ];
        return $this->response_json($datatable_result);
    }

    function data_detail_v2()
    {
        $param = $this->input->post();
        $data = $this->M_stock_card->data_detil_v2($param);

        $result = array();
        $saldo = $this->get_saldo_awal($param);
        $total_out = 0;
        $total_in = $this->M_stock_card->total_stock($param)->total_in;
        $total_out = $this->M_stock_card->total_stock($param)->total_out;
        foreach ($data as $key => $value) {
            // $total_qty = 0;
            $tmp = [
                'tgl_in' => millis_to_localtime('d/m/Y H:i:s', $value['date_in']),
                'tgl_out' => millis_to_localtime('d/m/Y H:i:s', $value['date_out']),
                'transaksi_in' => $value['stock_in_source'] . ' #' . $value['stock_in_id'],
                'transaksi_out' => $value['stock_out_source'] . ' #' . $value['stock_out_id'],
            ];

            $total_in -= $value['stock_out'];

            switch (true) {
                case($total_in >= 0 && $value['stock_out'] == 0):
                    $tmp['stock_in'] = $value['stock_in'];
                    $tmp['stock_out'] = "";
                    $tmp['price_in'] = $value['price'];
                    $tmp['price_out'] = "";
                    $tmp['transaksi_out'] = "";
                    $tmp['tgl_out'] = "";
                    $tmp['sisa'] = $total_in;
                    break;
                case($total_in >= 0 && ($total_out - $value['stock_out']) == 0 && ($value['stock_in'] - $value['total_out'] != 0)):
                    $tmp['stock_in'] = $value['stock_in'] - $value['total_out'];
                    $tmp['stock_out'] = '';
                    $tmp['price_in'] = $value['price'];
                    $tmp['price_out'] = '';
                    $tmp['transaksi_out'] = "";
                    $tmp['tgl_out'] = "";
                    $tmp['sisa'] = $total_in;
                    $tmp['sisa_out'] = $total_out;
                    break;
                case($total_in < 0 && $total_out != 0):
                    $tmp['stock_in'] = "";
                    $tmp['stock_out'] = $value['stock_out'];
                    $tmp['price_in'] = "";
                    $tmp['price_out'] = $value['price'];
                    $tmp['transaksi_in'] = "";
                    $tmp['tgl_in'] = "";
                    $tmp['sisa'] = $total_in;
                    break;
                case($total_in >= 0):
                    $tmp['stock_in'] = $value['stock_out'];
                    $tmp['stock_out'] = $value['stock_out'];
                    $tmp['price_in'] = $value['price'];
                    $tmp['price_out'] = $value['price'];
                    $tmp['sisa'] = $total_in;
                    break;
            }
            $total_out -= $value['stock_out'];



            array_push($result, $tmp);
        }
        $datatable_result = [
            'draw' => $this->input->post('draw'),
            'data' => $result,
            'saldo_awal' => $this->get_saldo_awal($param),
            'recordsFiltered' => count($result),
            'recordsTotal' => count($result)

        ];
        return $this->response_json($datatable_result);
    }

    function data_detail_v3()
    {
        $param = $this->input->post();
        $data = $this->M_stock_card->data_detail_v3($param);
        // echo $this->db->last_query();die;
        // Anggap array input disimpan dalam variabel bernama $data

        $output = array();

        // Loop setiap elemen dari array input
        foreach ($data as $element) {
            // Ekstrak nilai price_id dan status
            $price_id = $element["price_id"];
            $status = $element["status"];
            $element['date'] = millis_to_localtime('d/m/Y H:i:s', $element['created_at']);

            // Cek apakah status adalah "in"
            if ($status == "in") {
                // Jika ya, tambahkan index "detail" ke elemen dengan array kosong sebagai nilai
                $element["detail"] = array();
                // Loop lagi setiap elemen dari array input
                foreach ($data as $other) {
                    // Cek apakah elemen lain memiliki status "out" dan price_id sama dengan elemen saat ini
                    if ($other["status"] == "out" && $other["price_id"] == $price_id) {
                        // Jika ya, tambahkan elemen lain ke array "detail"
                        $other['date'] = millis_to_localtime('d/m/Y H:i:s', $other['created_at']);
                        $element["detail"][] = $other;
                    }
                }
            }

            // Cek apakah elemen sudah ada di dalam detail
            $found = false;
            foreach ($output as $item) {
                // Cek apakah item memiliki index "detail" dan elemen ada di dalamnya
                if (isset($item["detail"]) && in_array($element, $item["detail"])) {
                    // Jika ya, setel $found menjadi true
                    $found = true;
                    break;
                }
            }

            // Jika elemen tidak ditemukan di dalam detail, tambahkan elemen ke array output
            if (!$found) {
                $output[] = $element;
            }
        }

        usort($output, function ($a, $b) {
            return $a['created_at'] - $b['created_at'];
        });

        $newArr = [];
        foreach ($output as $key => $value) {
            if ($value['status'] == 'in') {
                foreach ($value['detail'] as $a) {
                    if ($value['created_at'] <= $a['created_at']) {
                        // array_push($newArr,$value);
                        $newArr[] = $value;
                    }
                }
                // $newArr[]=$value;
            }
        }

        return $this->response_json($newArr);
    }

    function data_detail_v4()
    {
        $param = $this->input->post();
        $saldo = $this->get_saldo_awal($param);
        $dataMaster = $this->M_stock_card->data_detail_v4($param);
        
        $data = [];
        if ($saldo) {
            $total_saldo = $saldo['stock_in'] - $saldo['stock_out'];
            if ($total_saldo >0) {
                array_push($dataMaster['dataIn'],$saldo);
            }else if($total_saldo <0){
                array_push($dataMaster['dataOut'],$saldo);
            }
        }
        //merge
        $mergeData = array_merge($dataMaster['dataIn'], $dataMaster['dataOut']);
        // print_r($mergeData);die;

        // Melakukan iterasi pada $mergeData
        foreach ($mergeData as $md) {
            // Mencari price_id yang sama di $data
            $md['date'] = millis_to_localtime('d/m/Y H:i:s', $md['created_at']).' #'.$md['price_id'];
            $found = false;
            foreach ($data as $key => $value) {
                if ($value['price_id'] == $md['price_id']) {
                    // Jika type = in, menambahkan index detail dan memasukkan data dari $mergeData
                    if ($value['type'] == 'in') {
                        $data[$key]['detail'][] = $md;
                        $found = true;
                    }
                    // Jika type = out, tidak perlu melakukan apa-apa
                }
            }
            // Jika tidak ditemukan price_id yang sama, menambahkan data dari $mergeData ke $data
            if (!$found) {
                $data[] = $md;
            }
        }

        // mengurutkan array
        // $result = [];
        foreach ($data as $key => $value) {
            if ($value['type'] == 'in') {
                if (array_key_exists('detail',$value)) {
                    $data[$key]['date_out'] = $value['detail'][0]['created_at'];
                }else{
                    $data[$key]['date_out'] = $value['created_at'];
                    $data[$key]['detail'] = [];
                }
            }else{
                $data[$key]['date_out'] = $value['created_at'];
            }
        }

        usort($data, function ($a, $b) {
            return $a['date_out'] - $b['date_out'];
          });

        // $data ['saldo'] = $saldo;

        // regenerate $data array to make saldo awal
        $array_out = [];
        foreach ($data as $key => $value) {
            if ($value['type'] == 'out' ) {
                array_push($array_out,$value);
                unset($data[$key]);
            }else if($value['type'] == 'in' && $value['ket']=='Saldo Awal'){
                continue;
            }else{
                break;
            }
        }
        
        $found = false;  
        foreach($data as $key => $value) {
            if ($value['ket']=='Saldo Awal') {
                foreach ($array_out as $a) {
                    array_push($data[$key]['detail'],$a);
                }

                $data[$key]['stock_in'] = $value['stock_in']-$value['stock_out'];
            }
        }

        foreach($data as $key => $value) {
            if (!empty($value['detail'])) {
                $total_out = 0;
                foreach ($value['detail'] as $i) {
                    $total_out += $i['stock_out'];
                }
            }
            $data[$key]['stock_out'] = $total_out;
        }

        return $this->response_json($data);
    }

    function get_saldo_awal($arr)
    {
        // last close book
        $last_close_book = $this->db->select("*")
            ->from("finance_close_book")
            ->where('admin_fkid', $this->session->userdata('admin_id'))
            ->order_by('end_date', 'desc')
            ->limit(1)
            ->get()->row();

        $param = [
            'startDate' => $last_close_book ? $last_close_book->end_date : 0,
            'endDate' => $arr['startDate'], //- (1000 * 60 * 60 * 24),
            'pd_id' => $arr['pd_id']
        ];
        $saldo = $this->M_stock_card->get_saldo($param);
        // echo $this->db->last_query();die;
        return $saldo;
    }

    function export_data()
    {
        $param = $this->input->get();
        // print_r($param);die;
        $data = $this->get_data($param);
        // print_r(json_encode($data));die;
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $start_row = 4;
        $start_col = 'A';
        $sheet = $spreadsheet->getActiveSheet();
        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('UNIQ')
            ->setLastModifiedBy('www.uniq.id')
            ->setTitle('UNIQ Finance Stock Card Document')
            ->setSubject('UNIQ Finance Stock Card Document')
            ->setDescription('UNIQ Finance Stock Card Document')
            ->setKeywords('office 2007, office 2013, office 2016')
            ->setCategory('UNIQ');


        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN //fine border
                ]
            ]
        ];
        // set judul
        $sheet->mergeCells('A1:O1');
        $sheet->setCellValue("A1", "REPORT STOCK CARD");
        $sheet->mergeCells('A2:O2');
        $sheet->mergeCells('A3:O3');
        $sheet->setCellValue("A2", "Outlet :" . $data[0]['outlet_name']);
        $sheet->setCellValue("A3", "Tanggal :" . millis_to_localtime("d/m/Y", $param['startDate']) . " s/d " . millis_to_localtime("d/m/Y", $param['endDate']));
        $sheet->getStyle('A1:B3')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A1:B3')->getFont()->setBold(true);

        // Set Header
        $excel_header = array(
            "No",
            "Nama Produk",
            "Outlet",
            "Akun Pembelian",
            "Akun Penjualan",
            "Akun Persediaan",
            "Qty In",
            "Harga In",
            "Total In",
            "Qty Out",
            "Harga Out",
            "Total Out",
            "Qty Sisa",
            "Harga Sisa",
            "Total Sisa"
        );
        foreach ($excel_header as $value) {
            $spreadsheet->setActiveSheetIndex(0)->setCellValue($start_col++ . $start_row, $value);
        }

        $sheet->getStyle('A4:O' . (count($data) + 4))->applyFromArray($styleArray);

        // set width colom
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getStyle('A4:O4')->getFont()->setBold(true);
        $sheet->getPageSetup()->setPaperSize(9);

        // Add Data to Excel
        $no = 0;
        foreach ($data as $a) {
            //init
            $start_col = 'A'; //reset excel column
            $start_row++; //set to next row

            $spreadsheet->setActiveSheetIndex(0)

                ->setCellValue($start_col++ . $start_row, $no += 1)
                ->setCellValue($start_col++ . $start_row, $a['product_name'])
                ->setCellValue($start_col++ . $start_row, $a['outlet_name'])
                ->setCellValue($start_col++ . $start_row, $a['pembelian'])
                ->setCellValue($start_col++ . $start_row, $a['penjualan'])
                ->setCellValue($start_col++ . $start_row, $a['persediaan'])
                ->setCellValue($start_col++ . $start_row, $a['stock_in'])
                ->setCellValue($start_col++ . $start_row, $a['price_in'])
                ->setCellValue($start_col++ . $start_row, $a['total_price_in'])
                ->setCellValue($start_col++ . $start_row, $a['stock_out'])
                ->setCellValue($start_col++ . $start_row, $a['price_out'])
                ->setCellValue($start_col++ . $start_row, $a['total_price_out'])
                ->setCellValue($start_col++ . $start_row, $a['sisa_stock'])
                ->setCellValue($start_col++ . $start_row, $a['total_price'])
                ->setCellValue($start_col++ . $start_row, $a['total_price_sisa'])
            ;
        }

        // Rename worksheet
        $sheet->setTitle('REPORT FINANCE STOCK CARD');
        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $spreadsheet->setActiveSheetIndex(0);

        /* DOWNLOAD FILE */
        // Redirect output to a client’s web browser (Xlsx)
        $filename = 'UNIQ-FINANCE-STOCK-CARD-' . $data[0]['outlet_name'] . "- (" . date('d-m-Y h:i:s') . ').xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        // header('Content-Disposition: attachment;filename="UNIQ-Opname-Import.xlsx"');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');

        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0

        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
    }

}

/* End of file Stock_card.php */
