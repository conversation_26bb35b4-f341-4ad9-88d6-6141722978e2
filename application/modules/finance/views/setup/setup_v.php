<style>

/*----------step-wizard------------*/
.d-flex{
	display: flex;
}
.justify-content-center{
	justify-content: center;
}

/*---------signup-step-------------*/
.wizard .nav-tabs {
    position: relative;
    margin-bottom: 0;
    border-bottom-color: transparent;
}

.wizard > div.wizard-inner {
    position: relative;
}

.connecting-line {
    height: 2px;
    background: #e0e0e0;
    position: absolute;
    width: 75%;
    margin: 0 auto;
    left: 0;
    right: 0;
    top: 50%;
    z-index: 1;
}

.wizard .nav-tabs > li.active > a, .wizard .nav-tabs > li.active > a:hover, .wizard .nav-tabs > li.active > a:focus {
    color: #555555;
    cursor: default;
    border: 0;
    border-bottom-color: transparent;
}

span.round-tab {
    width: 30px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    border-radius: 50%;
    background: #fff;
    z-index: 2;
    position: absolute;
    left: 0;
    text-align: center;
    font-size: 16px;
    color: #0e214b;
    font-weight: 500;
    border: 1px solid #ddd;
}
span.round-tab i{
    color:#555555;
}
.wizard li.active span.round-tab {
        background: #0db02b;
    color: #fff;
    border-color: #0db02b;
}
.wizard li.active span.round-tab i{
    color: #5bc0de;
}
.wizard .nav-tabs > li.active > a i{
	color: #0db02b;
}

.wizard .nav-tabs > li {
    width: 49%;
}

.wizard li:after {
    content: " ";
    position: absolute;
    left: 46%;
    opacity: 0;
    margin: 0 auto;
    bottom: 0px;
    border: 5px solid transparent;
    border-bottom-color: red;
    transition: 0.1s ease-in-out;
}

.wizard .nav-tabs > li a {
    width: 30px;
    height: 30px;
    margin: 20px auto;
    border-radius: 100%;
    padding: 0;
    background-color: transparent;
    position: relative;
    top: 0;
}
.wizard .nav-tabs > li a i{
	position: absolute;
    top: -15px;
    font-style: normal;
    font-weight: 400;
    white-space: nowrap;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 700;
    color: #b59d9d;
}

.wizard .nav-tabs > li a:hover {
    background: transparent;
}

.wizard .tab-pane {
    position: relative;
    padding-top: 20px;
}


.wizard h3 {
    margin-top: 0;
}
.prev-step,
.next-step{
    font-size: 13px;
    padding: 8px 24px;
    border: none;
    border-radius: 4px;
}
@media (max-width: 767px){
	.wizard .nav-tabs > li a i{
		display: none;
	}
}

.tableFixHead {
  overflow-y: auto;
  height: 80vh;
}

.tableFixHead table {
  border-collapse: collapse;
  width: 100%;
}

.tableFixHead th,
.tableFixHead td {
  padding: 8px 16px;
}

.tableFixHead th {
  position: sticky;
  top: 0;
}

</style>

<div id="app">
     <!-- Modal saldo -->
	<div class="modal fade" id="modal-setup" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-lg">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"></button>
					<h4 class="modal-title text-center">Selamat Datang di UNIQ Finance</h4>
				</div>
                <div class="modal-body">
                    <div class="row d-flex justify-content-center">
                        <div class="col-sm-5">
                            <div class="wizard">
                                <div class="wizard-inner">
                                    <div class="connecting-line"></div>
                                    <ul class="nav nav-tabs" role="tablist">
                                        <li role="presentation" class="active">
                                            <a href="#step1" data-toggle="tab" aria-controls="step1" role="tab" aria-expanded="true"><span class="round-tab">1 </span> <i>Step 1</i></a>
                                        </li>
                                        <li role="presentation" class="disabled">
                                            <a href="#step2" data-toggle="tab" aria-controls="step2" role="tab" aria-expanded="false"><span class="round-tab">2</span> <i>Step 2</i></a>
                                        </li>
                                        <!-- <li role="presentation" class="disabled">
                                            <a href="#step3" data-toggle="tab" aria-controls="step3" role="tab"><span class="round-tab">3</span> <i>Step 3</i></a>
                                        </li>
                                        <li role="presentation" class="disabled">
                                            <a href="#step4" data-toggle="tab" aria-controls="step4" role="tab"><span class="round-tab">4</span> <i>Step 4</i></a>
                                        </li> -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row d-flex justify-content-center">
                        <div class="col-sm-12">
                            <div class="wizard">
                                <div class="wizard-inner">
                                    <div class="tab-content" id="main_form">
                                        <div class="tab-pane active" role="tabpanel" id="step1">
                                            <!-- <h4 class="text-center">Step 1</h4> -->
                                            <div class="row text-center text-white" style="color:#fff">
                                                <h3>
                                                    Selamat datang di UNIQ FINANCE
                                                </h3>
                                                <h4>UNIQ Finance membantu mengoptimalkan laporan keuangan anda secara cepat dan efisien.</h4>
                                                <h4>Dengan UNIQ semua serba otomatis</h4>
                                                
                                                
                                            </div>
                                            <br>
                                            <hr>
                                            <div class="row">
                                                <ul class="list-inline pull-right">
                                                    <li class="pull-right">
                                                        <a href="<?=base_url()?>dashboard"> 
                                                            <button type="button" class="btn btn-default">Batal</button>
                                                        </a>
                                                    </li>
                                                    <li class="pull-right"> 
                                                        <span class="active ">*Coba GRATIS semua fitur UNIQ Finance tanpa batasan </span> <button type="button" class=" btn btn-primary next-step">Mulai Sekarang</button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="tab-pane" role="tabpanel" id="step2">
                                            <h4 class="text-center" style="color:#fff">Persiapkan daftar akun keuangan anda </h4>
                                            <p class="text-center" style="color:#fff">*Akun dibawah ini merupakan akun default dari UNIQ, anda dapat merubahnya nanti jika diperlukan</p>
                                            <div class="tableFixHead">

                                                <table class="table table-responsive table-striped tableFixHead" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th>Code</th>
                                                            <th>Nama</th>
                                                            <th>Category</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr v-for="i,idx in master.account">
                                                            <td>{{i.kode_akun}}</td>
                                                            <td>{{i.nama_akun}}</td>
                                                            <td>{{i.category}}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <br>
                                            <hr>
                                            <ul class="list-inline pull-right">
                                                <li><button type="button" class="btn-default prev-step">Back</button></li>
                                                <!-- <li><button type="button" class="btn-warning next-step skip-btn">Skip</button></li> -->
                                                <li><button type="button" class="btn-primary next-step" @click="onSubmit()">Continue</button></li>
                                            </ul>
                                        </div>
                                        <!-- <div class="tab-pane" role="tabpanel" id="step3">
                                            <h4 class="text-center">Step 3</h4>
                                            <ul class="list-inline pull-right">
                                                <li><button type="button" class="btn-default prev-step">Back</button></li>
                                                <li><button type="button" class="btn-primary next-step">Continue</button></li>
                                            </ul>
                                        </div> -->
                                        <!-- <div class="tab-pane" role="tabpanel" id="step4">
                                            <h4 class="text-center">Step 4</h4>
                                            
                                            <ul class="list-inline pull-right">
                                                <li><button type="button" class="btn-primary prev-step">Back</button></li>
                                                <li><button type="button" class="btn-primary next-step">Finish</button></li>
                                            </ul>
                                        </div> -->
                                        <div class="clearfix"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="modal-footer">
                    <button type="button" @click="validateSaldo" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading" id="btn-simpan">Simpan</button>
                </div> -->
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->
</div>


<script>
    var vm = new Vue({
        el:"#app",
        data:{
            master:{
                account:<?=json_encode($account) ?>
            }
        },
        methods: {
            onSubmit(){
                $.ajax({
                    type: "post",
                    url: "<?=base_url()?>finance/setup/setup_finance/setup_proccess",
                    beforeSend:function(){
                        loading.show();
                    },
                    success: function (response) {
                        loading.hide();
                        Alert.success("Success","data berhasil disimpan")
                        window.location.replace("<?=base_url()?>finance/report/Home_report_finance");
                    },
                    error(err){
                        loading.hide()
                        Alert.error("Data gagal disimpan");
                        console.log(err);
                    }
                });
            }
        },
        mounted(){
            // setTimeout(() => {
            //     $('#modal-setup').modal('show')
            // }, 2000);
            
        }
    })
        // ------------step-wizard-------------
    $(document).ready(function () {
        $('.nav-tabs > li a[title]').tooltip();
        $('#modal-setup').modal('show')
        //Wizard
        $('a[data-toggle="tab"]').on('show.bs.tab', function (e) {

            var $target = $(e.target);
        
            if ($target.parent().hasClass('disabled')) {
                return false;
            }
        });

        $(".next-step").click(function (e) {

            var $active = $('.wizard .nav-tabs li.active');
            $active.next().removeClass('disabled');
            nextTab($active);

        });
        $(".prev-step").click(function (e) {

            var $active = $('.wizard .nav-tabs li.active');
            prevTab($active);

        });
    });

    function nextTab(elem) {
        $(elem).next().find('a[data-toggle="tab"]').click();
    }

    function prevTab(elem) {
        $(elem).prev().find('a[data-toggle="tab"]').click();
    }
</script>