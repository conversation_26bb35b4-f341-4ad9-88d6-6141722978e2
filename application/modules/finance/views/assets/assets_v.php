<div id="app">
	<div class="container-fluid">
        <div class="content-uniq">
	    	<div class="row">
                <div class="col-sm-6">
                    <span><i class="fa fa-book"></i> Assets  </span>
                    <button class="btn btn-primary btn-sm" @click="onAdd()"><i class="fa fa-plus"></i> Add New</button>
                </div>
                <div class="col-sm-6">
	            	<div class="pull-right">
		                <div class="btn-group">
		                    <select id="selectOutlet" class="form-control btn btn-info" v-model="filter.outlet">
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-default btn-block btn-sm" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
            </div>
            <hr style="margin:10px">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-responsive table-report datatable" id="mytable" width="100%">
                        <thead>
                            <tr>
                                <th width="5px">No</th>
                                <th>Nama Aset</th>
                                <th>Outlet</th>
                                <th>Kategori</th>
                                <th>Sub Kategori</th>
                                <th>Status</th>
                                <th>Action </th>
                            </tr>
                        </thead>
                    </table>			
                </div>			
            </div>
        </div>
	</div><!-- /.container-fluid -->

	<!-- Modal -->
	<div class="modal fade" id="myModal" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">{{attr.formTitle}}</h4>
				</div>

					<div class="modal-body">
						
						<div class="form-group" style="margin-top:15px">
							<small for="email">Nama Aset*</small>
							<input type="text" :class="['form-control form-control-sm form-dark',inputs.name.error ? 'is-invalid' : '']" id="name" name="name" v-model="inputs.name.value">
							<small class="text-warning" id="err-name">{{inputs.name.error}}</small>
						</div>		

						<div class="form-group" style="margin-top:15px">
							<small class="control-label" for="unit">Satuan*</small>
							<select id="unit" :class="['form-control form-control-sm selectpicker2', inputs.unit_id.error ? 'is-invalid' : '']" data-live-search="true" name="unit_id" v-model="inputs.unit_id.value" v-select="inputs.unit_id.value" style="width:100%" require>
								<option value="" selected disabled>-- Pilih Unit --</option>	
								<?php foreach ($form_select_unit as $a): ?>
									<option value="<?=$a->unit_id ?>"><?=htmlentities($a->name) ?></option>
								<?php endforeach ?>
							</select>
							<small class="text-warning" id="err-category">{{inputs.outlet.error}}</small>
						</div>	

						<div class="form-group" style="margin-top:15px">
							<small class="control-label" for="selectOutlet2">Outet*</small>
							<select id="selectOutlet2" :class="['form-control form-control-sm', inputs.outlet.error ? 'is-invalid' : '']" data-live-search="true" name="outlet" multiple v-model="inputs.outlet.value" v-select="inputs.outlet.value" style="width:100%" require>
								<option value="" selected disabled>-- Pilih Outlet --</option>	
								<?php foreach ($form_select_outlet as $a): ?>
									<option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
								<?php endforeach ?>
							</select>
							<small class="text-warning" id="err-category">{{inputs.outlet.error}}</small>
						</div>
						<div class="form-group" style="margin-top:15px">
							<small class="control-label">Kategori*</small>
							<select id="kategori" :class="['form-control selectpicker2', inputs.category_id.error ? 'is-invalid' : '']" data-live-search="true" name="kategori" v-model="inputs.category_id.value" v-select="inputs.category_id.value" style="width:100%" require>
								<option value="" selected disabled>-- Pilih Kategori --</option>	
								<?php foreach ($form_select_products_category as $a): ?>
									<option value="<?=$a->product_category_id ?>"><?=htmlentities($a->name) ?></option>
								<?php endforeach ?>
							</select>
							<small class="text-warning" id="err-category">{{inputs.category_id.error}}</small>
						</div>
						<div class="form-group" style="margin-top:15px">
							<small class="control-label">Sub Kategori*</small>
							<select id="subCategory" :class="['form-control selectpicker2', inputs.subcategory_id.error ? 'is-invalid' : '']" data-live-search="true" name="subCategory" v-model="inputs.subcategory_id.value" v-select="inputs.subcategory_id.value" style="width:100%" require>
								<option value="" selected disabled>-- Pilih Sub Kategori --</option>	
								<?php foreach ($form_select_products_subcategory as $a): ?>
									<option value="<?=$a->product_subcategory_id ?>"><?=htmlentities($a->name) ?></option>
								<?php endforeach ?>
							</select>
							<small class="text-warning" id="err-category">{{inputs.subcategory_id.error}}</small>
						</div>
						<div class="form-group" style="margin-top:15px">
							<small class="control-label">Pantau Stok*</small>
							<select id="stockManagement" :class="['form-control form-control-sm form-darak', inputs.stockManagement.error ? 'is-invalid' : '']" data-live-search="false" name="stockManagement" v-model="inputs.stockManagement.value" v-select="inputs.stockManagement.value" style="width:100%" require>
								<option value="1">Yes</option>
								<option value="0">No</option>
							</select>
							<small class="text-warning" id="err-category">{{inputs.stockManagement.error}}</small>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" @click="onSubmit" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading" id="btn-simpan">Simpan</button>
					</div>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->

	<!-- Modal susut -->
	<div class="modal fade" id="modal-susut" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-lg">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">Depresiasi</h4>
				</div>

					<div class="modal-body">
						<div class="row">
							<div class="col-sm-6">	
								<div class="form-group" style="margin-top:15px">
									<small for="email">Nama Aset*</small>
									<input type="text" class="form-dark form-control" id="aset_id" name="aset_id" disabled>
								</div>		
								<div class="form-group" style="margin-top:15px">
									<small class="control-label" for="unit">Akun Aset Tetap*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_aset.value" v-select="inputSusut.account_aset.value" id="account_aset" width="100%">
										<option value="" selected disabled>-- Pilih Akun --</option>	
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.account_aset.error}}</small>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group" style="margin-top:15px">
									<small for="email">Tanggal Akuisisi*</small>
									<input type="date" :class="['form-control form-dark',inputSusut.tgl_akui.error ? 'is-invalid' : '']" id="tgl_akui" name="tgl_akui" v-model="inputSusut.tgl_akui.value" v-datepicker="inputSusut.tgl_akui">
									<small class="text-warning">{{inputSusut.tgl_akui.error}}</small>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Biaya Akuisisi*</small>
									<input type="text" :class="['form-control form-dark',inputSusut.biaya_akui.error ? 'is-invalid' : '']" id="biaya_akui" name="biaya_akui" v-model="inputSusut.biaya_akui.value" v-money="inputSusut.biaya_akui.value">
									<small class="text-warning">{{inputSusut.biaya_akui.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akun Dikreditkan*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_kredit.value" v-select="inputSusut.account_kredit.value" id="account_kredit" name="account_kredit" width="100%">
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.account_kredit.error}}</small>
								</div>
							</div>
						</div>	
						<br>
						<hr>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group" style="margin-top:15px">
									<small for="email">Aset non-depresiasi*</small>
									<select name="metode" class="form-control form-dark" id="non_depresiasi" name="non_depresiasi" v-model="inputSusut.non_depresiasi.value">
										<option value="0" selected>No</option>
										<option value="1">Yes</option>
									</select>
									<small class="text-warning">{{inputSusut.non_depresiasi.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Metode*</small>
									<select name="metode" class="form-control form-dark" id="metode" v-model="inputSusut.metode.value" :disabled="inputSusut.non_depresiasi.value==0">
										<option value="straight line">Straight Line</option>
										<option value="reducing balance">Reducing Balance</option>
									</select>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Masa Manfaat*</small>
									<div class="row">
										<div class="col-sm-8">
											<input type="text" :class="['form-control form-dark',inputSusut.masa_manfaat.error ? 'is-invalid' : '']" id="masa_manfaat" name="masa_manfaat" v-model="inputSusut.masa_manfaat.value" v-number="inputSusut.masa_manfaat.value" :disabled="inputSusut.non_depresiasi.value==0">
										</div>
										<div class="col-sm-3">
											<span>Tahun</span>
										</div>
									</div>
									<small class="text-warning">{{inputSusut.masa_manfaat.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Nilai/Tahun*</small>
									<div class="row">
										<div class="col-sm-8">
											<input type="text" :class="['form-control form-dark',inputSusut.nilai_tahun.error ? 'is-invalid' : '']" id="nilai_tahun" name="nilai_tahun" v-model="inputSusut.nilai_tahun.value" v-money="inputSusut.nilai_tahun.value"  :disabled="inputSusut.non_depresiasi.value==0">
										</div>
										<div class="col-sm-3">
											<span>Persen</span>
										</div>
									</div>
									<small class="text-warning">{{inputSusut.nilai_tahun.error}}</small>	
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akun Penyusutan*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.accout_susut.value" v-select="inputSusut.accout_susut.value" id="accout_susut" name="accout_susut" width="100%" :disabled="inputSusut.non_depresiasi.value==0">
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.accout_susut.error}}</small>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akumulasi Akun Penyusutan*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_akumulasi.value" v-select="inputSusut.account_akumulasi.value" id="account_akumulasi" name="account_akumulasi" width="100%" :disabled="inputSusut.non_depresiasi.value==0">
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.account_akumulasi.error}}</small>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akumulasi Penyusutan*</small>
									<input type="text" :class="['form-control form-dark',inputSusut.total_akumulasi.error ? 'is-invalid' : '']" id="total_akumulasi" name="total_akumulasi" v-model="inputSusut.total_akumulasi.value" v-money="inputSusut.total_akumulasi.value" :disabled="inputSusut.non_depresiasi.value==0">
									<small class="text-warning">{{inputSusut.total_akumulasi.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Pada Tanggal*</small>
									<input type="date" :class="['form-control form-dark',inputSusut.tgl_susut.error ? 'is-invalid' : '']" id="tgl_susut" name="tgl_susut" v-model="inputSusut.tgl_susut.value" v-datepicker="inputSusut.tgl_susut.value" :disabled="inputSusut.non_depresiasi.value==0">
									<small class="text-warning">{{inputSusut.tgl_susut.error}}</small>	
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" id="btn-btl">Batal</button>
						<button type="button" @click="onSubmitSusut" class="btn btn-primary" id="btn-simpan">Simpan</button>
						<button type="button" @click="onSubmitSusut" class="btn btn-uniq-primary" id="btn-post">Simpan & Posting</button>
					</div>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->
</div>


<!-- DATATABLE LOAD START -->
<script type="text/javascript">

	var vm = new Vue({	
		el: "#app",
		data:{
			attr:{
				formTitle:'Form Input Asets',
				methods:'created',
			},
            filter:{
                outlet:'',
            },
			inputs:{
				name:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
                unit_id:{
                    value:"",
                    error:"",
                    allowClear:true,
                    required: true,
                },
                category_id:{
                    value:"",
                    error:"",
                    allowClear:true,
                    required: true,
                },
                subcategory_id:{
                    value:"",
                    error:"",
                    allowClear:true,
                    required: true,
                },
				stockManagement:{
					value:"",
					error:"",
					allowClear: true,
				},
                outlet:{
                    value:[],
                    error:"",
                },
				product_id:{
					value:"",
					error:"",
					allowClear: true,
				},
				detail_id:{
					value:"",
					error:"",
					allowClear: true,
				}
			},	
			inputSusut:{
				aset_id:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
				account_aset:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				tgl_akui:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				biaya_akui:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				account_kredit:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				non_depresiasi:{
					value:"0",
					error:"",
					allowClear: true,
                    required: true,
				},
				metode:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				masa_manfaat:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				nilai_tahun:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				accout_susut:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				account_akumulasi:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				total_akumulasi:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				tgl_susut:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
			}
		},
		methods:{
			dataTable(){
				$(document).ready(function() {
					var t = $("#mytable").DataTable({
						scrollX:true,
						fixedHeader: true,
                        pageLength: 50,
                        destroy : true,
						ajax: {
				            url: "<?=current_url() ?>/datatable_assets",
				            type: "POST",
                            data : {outlet:vm.filter.outlet}
				        },
						columns: [
							{
								"data": null,
								"class":"text-center",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "asset_name",
							},							
							{
								"data": "outlet_name",
							},
							{"data": "category"},
							{"data": "sub_category"},
							{
								"className": "text-center",
								"data":"status",
								"render": function(data, type, row) {
									var btn_on = '<span class="label label-success">Active</span>';
									var btn_off= '<span class="label label-default">Off</span>';
									var html = ''
									if(row.status == "on"){
										html = btn_on;
									}else{
										html = btn_off
									}								
									return html
								}
							},
							{
								"orderable": false,
								"searchable": false,
								"className": "text-center",
								"render": function(data, type, row) {
									var btn_edit = '<button class="btn btn-xs btn-warning btn-edit" title="edit"><i class="fa fa-edit"></i></button>';
									var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus" title="hapus"><i class="fa fa-trash"></i></button>';
									var btn_susut = '<button class="btn btn-xs btn-primary btn-susut" title="penyusutan"><i class="fa fa-cog"></i></button>';	
									if(row.status == "on"){
										return btn_edit+' '+btn_hapus
									}else{
										return '<button class="btn btn-xs btn-primary" title="penyusutan"><i class="fa fa-lock"></i></button'
									}								
								}
							}
						],
						order: [[1, 'asc']]
					});//end datatable init					
				});	// end jequery
			},
			onSubmit(){
				if (!this.validate(this.inputs)) {
					return;
				}
				// console.log(this.inputs.outlet.value.length);
				if (this.inputs.outlet.value.length == 0) {
					this.inputs.outlet.value.error ="Outlet harus diisi"
					return;
				}
				var data = {};
				for (var key in this.inputs) {
					if (this.inputs.hasOwnProperty(key)) {
						data[key] = this.inputs[key].value;
					}
				}
				data.methods = this.attr.methods
				$.ajax({
					type: "post",
					url: "<?=current_url() ?>/save",
					data: data,
					dataType: "json",
					beforeSend: function() {
						loading.show();
					},
					success: function (response) {
						loading.hide();
						Alert.success('Success',"Data Berhasil di Simpan");
						$('#myModal').modal('hide');
						$('#mytable').DataTable().ajax.reload()
					},
					error: function(err) {
						loading.hide();
						Alert.error('Something Error!');
					}
				});
			},
			onAdd(){
				this.resetForm()
				$("#myModal").modal('show');
				this.attr.methods = 'created'				
				this.inputs.name.value = ''
				this.inputs.stockManagement.value = '0'
			},
            onApply(){
                if (this.filter.outlet == "") {
                    return Alert.warning("Warning","Pilih Outlet");
                }
                this.dataTable()
            },
			onSubmitSusut(){
				this.inputSusut.tgl_susut.value
				if (!this.validate(this.inputSusut)) {
					return;
				}

				var data = {};
				for (var key in this.inputSusut) {
					if (this.inputSusut.hasOwnProperty(key)) {
						data[key] = this.inputSusut[key].value;
					}
				}
				var date_susut = reverseDate(data.tgl_susut)
				var date_akui = reverseDate(data.tgl_akui)
				data.biaya_akui = clearFormat(data.biaya_akui),
				data.total_akumulasi = clearFormat(data.total_akumulasi),
				data.nilai_tahun = clearFormat(data.nilai_tahun),
				data.tgl_akui = dateToMilis(data.tgl_akui)
				data.tgl_susut = dateToMilis(data.tgl_susut)


				$.ajax({
					type: "post",
					url: "<?=current_url() ?>/save_susut",
					data: data,
					dataType: "json",
					beforeSend: function() {
						loading.show();
					},
					success: function (response) {
						loading.hide();
						Alert.success('Success',"Data Berhasil di Simpan");
						$('#modal-susut').modal('hide');
						$('#mytable').DataTable().ajax.reload()
					},
					error: function(err) {
						loading.hide();
						Alert.error('Something Error!',err);
					}
				});


			},
			getDataSusut(id){
				$.ajax({
					type: "get",
					url: "<?=current_url() ?>/data_susut",
					data:{aset_id:id},
					beforeSend(){
						loading.show();
					},
					success: function (data) {
						loading.hide();
						$("#modal-susut").modal('show');
						$('.selectAkun').selectpicker({
							title:"Pilih Akun",
							style: 'btn btn-info',
							selectedTextFormat:"count >3",
							width:"100%",
							liveSearch:true,
							actionsBox:true,
							allowClear: true,
						});
						if (data) {
							console.log(data);
						}else{
							console.log("data kosong");
						}

					},
					error(err){
						loading.hide();
						Alert.error('Error','Gagal memuat data')
						console.log(err);
					}
				});
			},
			onDelete(id){
				Alert.confirm("Anda yakin akan menonaktifkan aset","Proses menghapus aset akan menonaktifkan aset selamanya dan tidak bisa diaktifkan lagi").then(function(result){
					if (!result.value) {
						return;
					}
					$.ajax({
						type: "post",
						url: "<?=current_url() ?>/delete_assets",
						data:{pd_id:id},
						beforeSend(){
							loading.show();
						},
						success: function (data) {
							loading.hide();
							Alert.success("success","data Asset berhasil dinonaktifkan");
							$('#mytable').DataTable().ajax.reload();
						},
						error(err){
							loading.hide();
							Alert.error('Error','Gagal menghapus data')
							console.log(err);
						}
					});
				})
			},
			resetForm(){
				vm.inputs.name.value = ""
				vm.inputs.unit_id.value = ""
				vm.inputs.category_id.value = ""
				vm.inputs.subcategory_id.value = ""
				vm.inputs.stockManagement.value = ""
				vm.inputs.product_id.value = ""
				vm.inputs.detail_id.value = ""
				vm.inputs.outlet.value=[]
				vm.attr.methods='created'
				setTimeout(() => {
					$('.selectpicker2').selectpicker('refresh');
					$('#selectOutlet2').attr('disabled',false);
					$('#selectOutlet2').selectpicker('refresh');
				}, 100);
			}
		},
		watch:{
			'inputSusut.non_depresiasi.value'(newval){
				if (newval == 1) {
					$('#accout_susut').attr('disabled',false)
					$('#account_akumulasi').attr('disabled',false)
				}else{
					$('#account_akumulasi').attr('disabled',true)
					$('#accout_susut').attr('disabled',true)
					$('select[name=account_akumulasi]').val("");
					$('select[name=accout_susut]').val("");
					this.inputSusut.accout_susut.value= "";
					this.inputSusut.account_akumulasi.value= "";

					this.inputSusut.masa_manfaat.value = ""
					this.inputSusut.nilai_tahun.value = ""
					this.inputSusut.total_akumulasi.value = ""
					this.inputSusut.tgl_susut.value = ""
				}
				$('.selectAkun').selectpicker('refresh');
				// console.log(newval);
			}
		},
		mounted(){
			this.dataTable()
			$(document).ready(function () {
				$('#selectOutlet').selectpicker({
					title:"Outlet",
					style: 'form-control btn btn-sm btn-info',
					selectedTextFormat:"count >3",
					dropdownAlignRight:true,
					width:"auto",
					liveSearch:true,
					actionsBox:true
				});
				$('.selectpicker2').selectpicker({
					style: 'btn btn-info',
					selectedTextFormat:"count >3",
					width:"100%",
					liveSearch:true,
					actionsBox:true,
					allowClear: true,
				});

				$('#stockManagement').selectpicker({
					style: 'btn btn-info',
					selectedTextFormat:"count >3",
					width:"100%",
					liveSearch:false,
					actionsBox:false,
					allowClear: true,
				});

				$('#selectOutlet2').selectpicker({
					style: 'btn btn-info',
					selectedTextFormat:"count >3",
					width:"100%",
					liveSearch:true,
					actionsBox:true,
					allowClear: true,
				});
			});
        }
	})

    function onSaldo(){
        vm.setSaldo()
    }
</script>

<script type="text/javascript">

$(document).ready(function() {
	//ACTION EDIT
	$('#mytable').on('click', '.btn-edit', function () {
		$("#myModal").modal('show');
		var data = $("#mytable").DataTable().row($(this).closest('tr')).data();
		vm.inputs.name.value = data.asset_name
		vm.inputs.unit_id.value = data.unit_id
		vm.inputs.category_id.value = data.category_id
		vm.inputs.subcategory_id.value = data.sub_id
		vm.inputs.stockManagement.value = data.stock_management
		vm.inputs.product_id.value = data.p_id
		vm.inputs.detail_id.value = data.pd_id
		vm.inputs.outlet.value.push(data.outlet_id)
		vm.attr.methods='updated'
		// vm.inputs.method.value = 'updated'
		// $('select[name=kategori]').attr('disabled',true);
		setTimeout(() => {
			$('.selectpicker2').selectpicker('refresh');
			$('#selectOutlet2').attr('disabled',true);
			$('#selectOutlet2').selectpicker('refresh');

		}, 100);
	});

	//ACTION susut
	$('#mytable').on('click', '.btn-hapus', function () {
		var data = $("#mytable").DataTable().row($(this).closest('tr')).data();
		vm.onDelete(data.pd_id)
	});

	//ACTION susut
	$('#mytable').on('click', '.btn-susut', function () {
		var data = $("#mytable").DataTable().row($(this).closest('tr')).data();
		// console.log(data);
		vm.inputSusut.aset_id.value = data.pd_id
		$("#aset_id").val(data.asset_name)
		vm.getDataSusut(data.asset_id)
		//ambil semua data di row
	});
})
</script>