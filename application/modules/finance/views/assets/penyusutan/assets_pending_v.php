<div id="app">
    <div class="row">
        <div class="col-sm-6 col-sm-offset-6">
            <div class="pull-right">
                <div class="btn-group">
                    <select id="selectOutlet" class="form-control btn btn-info" v-model="filter.outlet">
                        <?php foreach ($form_select_outlet as $a): ?>                   
                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                        <?php endforeach ?>
                    </select>                                   
                </div>
                <div class="btn-group">
                    <button class="btn btn-default btn-block btn-sm" @click="dataTable()" type="button">Apply</button>                  
                </div>
            </div>	                
        </div>
    </div>
    <hr style="margin:10px">
    <div class="row">
        <div class="col-sm-12">
            <table class="table table-responsive table-report datatable" id="table-pending" width="100%">
                <thead>
                    <tr>
                        <th width="5px">No</th>
                        <th>Kode aset</th>
                        <th>Nama Aset</th>
                        <th>Outlet</th>
                        <th>Tgl Akuisisi</th>
                        <th>Biaya Akuisisi</th>
                        <th>Status</th>
                        <th><i class="fa fa-cog"></i></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="8" class="text-center">Pilih Outlet Untuk menampikan data</td>
                    </tr>
                </tbody>
            </table>			
        </div>			
    </div>

    	<!-- Modal susut -->
	<div class="modal fade" id="modal-setting" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-lg">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">Depresiasi</h4>
				</div>

					<div class="modal-body">
						<div class="row">
							<div class="col-sm-6">	
								<div class="form-group" style="margin-top:15px">
                                    <small for="email">Outlet*</small>
                                    <select id="formOutlet-pending" name="formOutlet-pending" class="form-control btn btn-info" v-model="inputSusut.outlet_id.value" v-select="inputSusut.outlet_id.value">
                                        <option value="" selected disabled>-- Pilih Outlet --</option>
                                        <?php foreach ($form_select_outlet as $a): ?>                   
                                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>      
                                </div>
								<div class="form-group" style="margin-top:15px">
                                    <small for="asset-name-pending">Nama Aset*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.pd_fkid.value" v-select="inputSusut.pd_fkid.value" id="asset-name-pending" name="asset-name-pending" width="100%">
                                        <option value="" selected disabled>-- Pilih Master Asset --</option>
                                        <option v-for="i,idx in master.assets" :value="i.asset_id">{{i.asset_name}}</option>
                                    </select>
                                    <small class="text-warning">{{inputSusut.pd_fkid.error}}</small>
                                </div>		
								<div class="form-group" style="margin-top:15px">
									<small class="control-label" for="unit">Akun Aset Tetap*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_aset.value" v-select="inputSusut.account_aset.value" id="account_aset" width="100%">
										<option value="" selected disabled>-- Pilih Akun --</option>	
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.account_aset.error}}</small>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group" style="margin-top:15px">
									<small for="email">Tanggal Akuisisi*</small>
									<input type="text" :class="['form-control form-dark',inputSusut.tgl_akui.error ? 'is-invalid' : '']" id="tgl_akui" name="tgl_akui" v-model="inputSusut.tgl_akui.value" disabled>
									<small class="text-warning">{{inputSusut.tgl_akui.error}}</small>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Biaya Akuisisi*</small>
									<input type="text" :class="['form-control form-dark',inputSusut.biaya_akui.error ? 'is-invalid' : '']" id="biaya_akui" name="biaya_akui" v-model="inputSusut.biaya_akui.value" v-money="inputSusut.biaya_akui.value" disabled>
									<small class="text-warning">{{inputSusut.biaya_akui.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Qty*</small>
									<input type="text" :class="['form-control form-dark',inputSusut.qty.error ? 'is-invalid' : '']" id="qty" name="qty" v-model="inputSusut.qty.value" v-number="inputSusut.qty.value" disabled>
									<small class="text-warning">{{inputSusut.qty.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px" v-if="inputSusut.purchase_fkid.value ==null">
                                    <small for="email">Akun Dikreditkan*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_kredit.value" v-select="inputSusut.account_kredit.value" id="account_kredit" name="account_kredit" width="100%">
                                    <option value="" selected disabled>-- Pilih Akun --</option>
                                        <?php foreach ($account as $a): ?>                   
                                            <option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="text-warning">{{inputSusut.account_kredit.error}}</small>
                                </div>
							</div>
						</div>	
						<br>
						<hr>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group" style="margin-top:15px">
									<small for="email">Aset depresiasi*</small>
									<select name="metode" class="form-control form-dark" id="non_depresiasi" name="non_depresiasi" v-model="inputSusut.non_depresiasi.value">
										<option value="0" selected>No</option>
										<option value="1">Yes</option>
									</select>
									<small class="text-warning">{{inputSusut.non_depresiasi.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Metode*</small>
									<select name="metode" class="form-control form-dark" id="metode" v-model="inputSusut.metode.value" disabled>
										<option selected value="straight_line">Straight Line</option>
										<option value="reducing_balance">Reducing Balance</option>
									</select>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Masa Manfaat*</small>
									<div class="row">
										<div class="col-sm-8">
											<input type="text" :class="['form-control form-dark',inputSusut.masa_manfaat.error ? 'is-invalid' : '']" id="masa_manfaat" name="masa_manfaat" v-model="inputSusut.masa_manfaat.value" v-number="inputSusut.masa_manfaat.value" :disabled="inputSusut.non_depresiasi.value==0">
										</div>
										<div class="col-sm-3">
											<span>Tahun</span>
										</div>
									</div>
									<small class="text-warning">{{inputSusut.masa_manfaat.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Nilai/Tahun*</small>
									<div class="row">
										<div class="col-sm-8">
											<input type="text" :class="['form-control form-dark',inputSusut.nilai_tahun.error ? 'is-invalid' : '']" id="nilai_tahun" name="nilai_tahun" v-model="inputSusut.nilai_tahun.value" v-money="inputSusut.nilai_tahun.value" disabled>
										</div>
										<div class="col-sm-3">
											<span>Persen</span>
										</div>
									</div>
									<small class="text-warning">{{inputSusut.nilai_tahun.error}}</small>	
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akun Penyusutan*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.accout_susut.value" v-select="inputSusut.accout_susut.value" id="accout_susut" name="accout_susut" width="100%" :disabled="inputSusut.non_depresiasi.value==0">
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.accout_susut.error}}</small>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akumulasi Akun Penyusutan*</small>
									<select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_akumulasi.value" v-select="inputSusut.account_akumulasi.value" id="account_akumulasi" name="account_akumulasi" width="100%" :disabled="inputSusut.non_depresiasi.value==0">
										<?php foreach ($account as $a): ?>                   
											<option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<small class="text-warning">{{inputSusut.account_akumulasi.error}}</small>
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Akumulasi Penyusutan*</small>
									<input type="text" :class="['form-control form-dark',inputSusut.total_akumulasi.error ? 'is-invalid' : '']" id="total_akumulasi" name="total_akumulasi" v-model="inputSusut.total_akumulasi.value" v-money="inputSusut.total_akumulasi.value" :disabled="inputSusut.non_depresiasi.value==0">
									<small class="text-warning">{{inputSusut.total_akumulasi.error}}</small>	
								</div>
								<div class="form-group" style="margin-top:15px">
									<small for="email">Pada Tanggal*</small>
									<input type="date" :class="['form-control form-dark',inputSusut.tgl_susut.error ? 'is-invalid' : '']" id="tgl_susut" name="tgl_susut" v-model="inputSusut.tgl_susut.value" v-datepicker="inputSusut.tgl_susut.value" :disabled="inputSusut.non_depresiasi.value==0">
									<small class="text-warning">{{inputSusut.tgl_susut.error}}</small>	
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" id="btn-btl" data-dismiss="modal">Batal</button>
						<button type="button" @click="onSubmitSusut('pending')" class="btn btn-primary" id="btn-simpan">Simpan</button>
						<button type="button" @click="onSubmitSusut('active')" class="btn btn-uniq-primary" id="btn-post">Simpan & Posting</button>
					</div>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->
</div>

<script>
    var vm = new Vue({
        el:"#app",
        data:{
            filter:{
                outlet:""
            },
			master:{
                assets:[]
            },
            inputs:{         	
            },
            inputSusut:{
				outlet_id:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
                aset_id:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: true,
                },
                pd_fkid:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: true,
                },
                account_aset:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: true,
                    disabled:true,
                },
                tgl_akui:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: true,
                    disabled:true,
                },
                biaya_akui:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: true,
                    disabled:true,
                },
				account_kredit:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
                non_depresiasi:{
                    value:"0",
                    error:"",
                    allowClear: true,
                    required: false,
                },
                metode:{
                    value:"straight_line",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                masa_manfaat:{
                    value:"4",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                nilai_tahun:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                accout_susut:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                account_akumulasi:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                total_akumulasi:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                tgl_susut:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                curent_account_susut:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                purchase_fkid:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
                qty:{
                    value:"",
                    error:"",
                    allowClear: true,
                    required: false,
                    disabled:true,
                },
            }
        },
        methods: {
            dataTable(){
                $(document).ready(function() {
					var t = $("#table-pending").DataTable({
						scrollX:true,
						fixedHeader: true,
                        pageLength: 50,
                        destroy : true,
						ajax: {
				            url: "<?=current_url() ?>/datatable/pending",
				            type: "POST",
                            data : {outlet:vm.filter.outlet}
				        },
						columns: [
							{
								"data": null,
								"class":"text-center",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "asset_code",
							},							
							{
								"data": "asset_name",
							},							
							{
								"data": "outlet_name",
							},						
							{
								"data": "tgl_akuisisi",
                                "className": "text-center",
                                "render": function(data, type, row) {
                                    return milisToLocal(data);
                                }
							},						
							{
								"data": "biaya_akuisisi",
                                "className": "text-right",
                                "render": function(data, type, row) {
                                    return currency(data);
                                }
							},
							{
								"className": "text-center",
								"data":"status",
								"render": function(data, type, row) {
									var btn_on = '<span class="label label-success">Active</span>';
									var btn_off= '<span class="label label-default">Pending</span>';
									var html = ''
									if(row.status == "active"){
										html = btn_on;
									}else{
										html = btn_off
									}								
									return html
								}
							},
							{
								"data": "asset_id",
								"orderable": false,
								"searchable": false,
								"className": "text-center",
								"render": function(data, type, row) {
									var btn_edit = '<button class="btn btn-xs btn-warning btn-edit" title="Catat"><i class="fa fa-edit"> Catat</i></button>';
									var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus" title="hapus"><i class="fa fa-trash"></i></button>';
                                    return btn_edit
								}
							}
						],
						order: [[1, 'asc']]
					});//end datatable init					
				});	// end jequery
            },
            onSetting(data){
                $.ajax({
                    type: "POST",
                    url: "<?=current_url()?>/data_aset",
                    data: {id:data},
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success: function (res) {
                        loading.hide();
						// console.log(res);
                        $("#modal-setting").modal("show");   
						$("#aset_id").val(res.asset_name)
						// var form = vm.inputSusut
						vm.inputSusut.outlet_id.value = res.outlet_fkid
						vm.inputSusut.aset_id.value = res.asset_id
						vm.inputSusut.pd_fkid.value = res.pd_fkid
						vm.inputSusut.account_aset.value = res.account_aset_tetap_fkid
						vm.inputSusut.tgl_akui.value = milisToLocal(res.tgl_akuisisi,"YYYY-MM-DD")
						vm.inputSusut.biaya_akui.value = currency(res.biaya_akuisisi)
						vm.inputSusut.account_kredit.value = res.account_credit_fkid
						vm.inputSusut.non_depresiasi.value = res.depreciation
						vm.inputSusut.masa_manfaat.value = res.masa_manfaat
						vm.inputSusut.nilai_tahun.value = res.nilai_tahun
						vm.inputSusut.accout_susut.value = res.account_susut_fkid
						vm.inputSusut.account_akumulasi.value = res.account_total_susut_fkid
						vm.inputSusut.total_akumulasi.value = res.akumulasi_susut
						vm.inputSusut.purchase_fkid.value = res.purchase_fkid
						vm.inputSusut.qty.value = res.qty
						if (res.tgl_susut) {
							vm.inputSusut.tgl_susut.value = milisToLocal(res.tgl_susut,"YYYY-MM-DD")
						}else{
							vm.inputSusut.tgl_susut.value = ""
						}
						
						if (res.purchase_fkid!=null) {
							vm.inputSusut.account_kredit.required =false
						}else{
							vm.inputSusut.account_kredit.required =true
						}
						vm.inputSusut.curent_account_susut.value = res.account_aset_tetap_fkid

						setTimeout(() => {
							$('.selectAkun').selectpicker('refresh');
							$('#formOutlet-pending').selectpicker('refresh');
						}, 200);
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error('error','Gagal menampilkan data')
                    }
                });
            },
            onSubmitSusut(status){
				if (!this.validate(this.inputSusut)) {
					return;
				}

				if (dateToMilis(this.inputSusut.tgl_akui.value) > dateToMilis(this.inputSusut.tgl_susut.value)) {
					return Alert.warning("oops..","tanggal penyusutan tidak bileh lebih kecil dari tanggal akui")
				}
				var data = {};
				for (var key in this.inputSusut) {
					if (this.inputSusut.hasOwnProperty(key)) {
						data[key] = this.inputSusut[key].value;
					}
				}
				data.total_akumulasi = clearFormat(data.total_akumulasi)
				// data.nilai_tahun = clearFormat(data.nilai_tahun)
				data.biaya_akui = clearFormat(data.biaya_akui)
				data.tgl_susut = dateToMilis(data.tgl_susut)
				data.status = status
				$.ajax({
					type: "POST",
					url: "<?=current_url()?>/catat",
					data: data,
					dataType: "json",
					beforeSend(){
						loading.show()
					},
					success(res) {
						loading.hide();
						Alert.success("Success","data berhasil disimpan");
						$("#modal-setting").modal("hide");
						$("#table-pending").DataTable().ajax.reload();
					},
					error(err){
						console.log(err);
						loading.hide();
						Alert.error("error","data gagal disimpan")
					}
				});
            }
        },
		watch:{
			'inputSusut.non_depresiasi.value'(newval){
				if (newval == 1) {
					$('#accout_susut').attr('disabled',false)
					$('#account_akumulasi').attr('disabled',false)

					this.inputSusut.masa_manfaat.required = true
					this.inputSusut.nilai_tahun.required = true
					this.inputSusut.accout_susut.required = true
					this.inputSusut.account_akumulasi.required = true
					this.inputSusut.total_akumulasi.required = false
					this.inputSusut.tgl_susut.required = true
				}else{
					$('#account_akumulasi').attr('disabled',true)
					$('#accout_susut').attr('disabled',true)
					$('select[name=account_akumulasi]').val("");
					$('select[name=accout_susut]').val("");
					this.inputSusut.accout_susut.value= "";
					this.inputSusut.account_akumulasi.value= "";

					this.inputSusut.masa_manfaat.value = ""
					this.inputSusut.nilai_tahun.value = ""
					this.inputSusut.total_akumulasi.value = ""
					this.inputSusut.tgl_susut.value = ""

					this.inputSusut.masa_manfaat.required = false
					this.inputSusut.nilai_tahun.required = false
					this.inputSusut.accout_susut.required = false
					this.inputSusut.account_akumulasi.required = false
					this.inputSusut.total_akumulasi.required = false
					this.inputSusut.tgl_susut.required = false
				}
				$('.selectAkun').selectpicker('refresh');
				// console.log(newval);
			},
			'inputSusut.outlet_id.value'(newVal){
                $.ajax({
                    type: "post",
                    url: "<?=current_url()?>/get_assets",
                    data: {outlet_id :newVal},
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success(res) {
                        loading.hide()
                        vm.master.assets = res
                        setTimeout(() => {
							$('#asset-name-pending').attr('disabled',true)
                            $('#asset-name-pending').selectpicker('refresh');
							$('#formOutlet-pending').attr('disabled',true)
                            $('#formOutlet-pending').selectpicker('refresh');
                        }, 100);
                    },
                    error(err){
                        loading.hide()
                        Alert.error("error")
                        console.log(err);
                    }
                });
            },
			'inputSusut.masa_manfaat.value'(newVal){
				let nilaiTahun =100/newVal
				this.inputSusut.nilai_tahun.value = parseFloat(nilaiTahun).toFixed(2)
			}
		},
        mounted() {
            // $("#table-pending").dataTable();
            $(function () {
                $('#selectOutlet').selectpicker({
					title:"All Outlet",
					style: 'form-control btn btn-sm btn-info',
					selectedTextFormat:"count >3",
					dropdownAlignRight:true,
					width:"auto",
					liveSearch:true,
					actionsBox:true
				});
				$('.selectAkun').selectpicker({
					style: 'btn btn-info',
					selectedTextFormat:"count >3",
					width:"100%",
					liveSearch:true,
					actionsBox:true,
					allowClear: true,
				});
            });
        },
    })

    $(function () {
        $('#table-pending').on('click', '.btn-edit', function () {
            var data = $("#table-pending").DataTable().row($(this).closest('tr')).data();
            vm.onSetting(data.asset_id)
        })
    });
</script>