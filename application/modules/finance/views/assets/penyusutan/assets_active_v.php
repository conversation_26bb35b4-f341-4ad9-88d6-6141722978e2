<style>
    .select2-search { background-color: #2d2d2d; }
    .select2-search input { background-color: #2d2d2d; }
    .select2-results { background-color: #2d2d2d }
    /* .select2-choice { background-color: #2d2d2d !important; } */
    .select2-container--default .select2-selection--single .select2-selection__rendered { color: #fff; }
    .select2-container--default .select2-selection--single{
        background-color: #2d2d2d;
        color: #fff;
        border-color: #6c757d;
    }
    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #424242;
    }
    
    .select2-container--default.select2-container--disabled .select2-selection--single {
        background-color: #464545;
    }
</style>
<div id="app-active">
    <div class="row">
        <div class="col-sm-6 col-sm-offset-6">
            <div class="pull-right">
                <div class="btn-group">
                    <select id="selectOutletActive" class="form-control btn btn-info" v-model="filter.outlet">
                        <?php foreach ($form_select_outlet as $a): ?>                   
                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                        <?php endforeach ?>
                    </select>                                   
                </div>
                <div class="btn-group">
                    <button class="btn btn-default btn-block btn-sm" @click="dataTable()" type="button">Apply</button>                  
                </div>
            </div>	                
        </div>
    </div>
    <hr style="margin:10px">
    <div class="row">
        <div class="col-sm-12">
            <table class="table table-responsive table-report datatable" id="table-active" width="100%">
                <thead>
                    <tr>
                        <th width="5px">No</th>
                        <th>Kode aset</th>
                        <th>Nama Aset</th>
                        <th>Outlet</th>
                        <th>Tgl Akuisisi</th>
                        <th>Biaya Akuisisi</th>
                        <th>Nilai Buku</th>
                        <th>Status</th>
                        <th><i class="fa fa-cog"></i></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center">Pilih Outlet Untuk menampikan data</td>
                    </tr>
                </tbody>
            </table>			
        </div>			
    </div>

	<!-- Modal susut -->
	<div class="modal fade" id="modal-history" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">History penyusutan </h4>
                </div>

                    <div class="modal-body">
						<div class="row">
							<div class="col-sm-12">
								<table class="table">
									<tr>
										<th width="20%">Nama Asset</th>
										<td>: <b>{{filter.asset_name}}</b></td>
									</tr>
									<tr>
										<th width="20%">Outlet</th>
										<td>: <b>{{filter.outlet_name}}</b></td>
									</tr>
									<tr>
										<th width="20%">Tanggal Akui</th>
										<td>: <b>{{filter.tgl_akui}}</b></td>
									</tr>
									<tr>
										<th width="20%">Nilai Akui</th>
										<td>: <b>{{currency(parseFloat(filter.nilai_akui))}}</b></td>
									</tr>
									<tr>
										<th width="20%">Jumlah Akui</th>
										<td>: <b>{{filter.qty_akui}} {{filter.unit}}</b></td>
									</tr>
								</table>
							</div>
						</div>
                        <div class="row">
							<div class="col-sm-12">
								<table class="table table-responsive table-report" id="table-susut-history" width="100%">
									<thead>
										<tr>
											<th width="5px">No</th>
											<th>Type</th>
											<th>Tgl</th>
											<th>Sales</th>
											<th>Qty</th>
											<th>Perolehan</th>
											<th>Nilai Susut</th>
											<th>Akumulasi</th>
											<th>Nilai Buku</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(a,idx) in historySusut">
											<td>{{idx+1}}</td>
											<td>{{a.type}}</td>
											<td>{{milisToLocal(a.tgl)}}</td>
											<td>{{a.qty_sales}}</td>
											<td>{{a.qty_susut}}</td>
											<td>{{currency(a.perolehan)}}</td>
											<td>{{currency(parseFloat(a.nominal))}}</td>
											<td>{{currency(parseFloat(a.akumulasi))}}</td>
											<td>{{currency(parseFloat(a.nilai))}}</td>
										</tr>
									</tbody>
								</table>
							</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" id="btn-btl" data-dismiss="modal">Keluar</button>
                    </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

	<!-- Modal sales -->
	<div class="modal fade" id="modal-sales" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Penjualan Asset </h4>
                </div>

                    <div class="modal-body" style="color:white">
						<div class="row">
							<div class="col-sm-12">
								<table class="table">
									<tr>
										<th width="20%">Nama Asset</th>
										<td><b>: {{filter.asset_name}}</b></td>
									</tr>
									<tr>
										<th width="20%">Outlet</th>
										<td><b>: {{filter.outlet_name}}</b></td>
									</tr>
									<tr>
										<th width="20%">Tanggal Akui</th>
										<td><b>: {{filter.tgl_akui}}</b></td>
									</tr>
									<tr>
										<th width="20%">Nilai Akui</th>
										<td><b>: {{filter.nilai_akui}}</b></td>
									</tr>
									<tr>
										<th width="20%">Qty Akui</th>
										<td><b>: {{filter.qty_akui}}</b></td>
									</tr>
								</table>
							</div>
						</div>
						<hr>
						<h3>Penjualan asset</h3>
						<div class="row">

							<div class="col-sm-3">
								<div class="form-group" style="margin-top:15px">
									<small for="qty-sales">Qty dijual*</small>
									<input type="text" :class="['form-control form-control-sm form-dark',formSales.qty.error ? 'is-invalid' : '']" id="qty-sales" name="qty-sales" v-model="formSales.qty.value" v-number="formSales.qty.value">
									<small class="invalid-feedback" id="err-qty-sales">{{formSales.qty.error}}</small>
								</div>	
							</div>
							<div class="col-sm-9">
								<div class="form-group" style="margin-top:15px">
									<small for="qty-saharga-jualles">Harga Jual*</small>
									<input type="text" :class="['form-control form-control-sm form-dark',formSales.price.error ? 'is-invalid' : '']" id="harga-jual" name="harga-jual" v-model="formSales.price.value" v-money="formSales.price.value">
									<small class="invalid-feedback" id="err-harga-jual">{{formSales.price.error}}</small>
								</div>	
							</div>
							<div class="col-sm-7">
								<div class="form-group" style="margin-top:15px">
									<small for="qty-saharga-jualles">Sub Total*</small>
									<h3>{{subTotal}}</h3>
								</div>	
							</div>
							<div class="col-sm-4">
								<div class="form-group" style="margin-top:15px">
									<small for="qty-saharga-jualles">Pembayaran*</small>
									<select class="form-control form-dark" name="payment" id="payment" v-model="formSales.payment.value">
										<option value="cash" selected>Cash</option>
										<option value="card">Card</option>
									</select>
									<small class="invalid-feedback" id="err-harga-jual">{{formSales.payment.error}}</small>
								</div>	
							</div>
							<div class="col-sm-4" >
								<div class="form-group" style="margin-top:15px" v-if="formSales.payment.value=='card'">
									<small for="qty-saharga-jualles">Pilih Bank*</small>
									<select id="bank" :class="['form-control form-dark','select2select ', formSales.bank.error ? 'is-invalid' : '']" name="payment_media_bank" v-model="formSales.bank.value" v-select="formSales.bank.value" style="width:100%" data-placeholder="-- Pilih Bank --">
										<?php foreach ($form_select_bank as $a): ?>                   
											<option value="<?=$a->bank_id ?>"><?=htmlentities($a->name) ?></option>
										<?php endforeach ?>
									</select>
									<div class="invalid-feedback">{{ formSales.bank.error }}</div>
								</div>	
							</div>
							
							
						</div>
                    </div>
                    <div class="modal-footer">
						<button type="button" class="btn btn-default" id="btn-btl" data-dismiss="modal">Batal</button>
                        <button type="button" @click="onSubmitSales" class="btn btn-primary" id="btn-save">Simpan</button>
                    </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>

<script>
    var vmActive = new Vue({
        el:"#app-active",
        data:{
            filter:{
                outlet:"",
				asset_name:"",
				outlet_name:"",
				tgl_akui:"",
				nilai_akui:"",
				qty_akui:"",
				akumulasi_susut:"",
				unit:""
            },
			formSales:{
				qty:{
					title:"Qty",
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
				price:{
					title:"Harga",
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
				hpp:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
				payment:{
					value:"cash",
					error:"",
					allowClear: true,
                    required: true,
				},
				bank:{
					title:"Bank",
					value:"",
					error:"",
					allowClear: true,
                    required: false,
				},
				asset_fkid:{
					title:"asset",
					value:"",
					error:"",
					allowClear: true,
                    required: false,
				},
				perolehan:{
					title:"asset",
					value:"",
					error:"",
					allowClear: true,
                    required: false,
				}
			},
			historySusut:[]
        },
        methods: {
            dataTable(){
                $(document).ready(function() {
					var t = $("#table-active").DataTable({
						scrollX:true,
						fixedHeader: true,
                        pageLength: 50,
                        destroy : true,
						ajax: {
				            url: "<?=current_url() ?>/datatable/active",
				            type: "POST",
                            data : {outlet:vmActive.filter.outlet}
				        },
						columns: [
							{
								"data": null,
								"class":"text-center",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "asset_code",
							},							
							{
								"data": "asset_name",
							},							
							{
								"data": "outlet_name",
							},						
							{
								"data": "tgl_akuisisi",
                                "className": "text-center",
                                "render": function(data, type, row) {
                                    return milisToLocal(data);
                                }
							},						
							{
								"data": "biaya_akuisisi",
                                "className": "text-right",
                                "render": function(data, type, row) {
                                    return currency(data);
                                }
							},						
							{
								"data": "nilai_buku",
                                "className": "text-right",
                                "render": function(data, type, row) {
                                    return currency(parseFloat(data));
                                }
							},
							{
								"className": "text-center",
								"data":"status",
								"render": function(data, type, row) {
									var btn_on = '<span class="label label-success">Active</span>';
									var btn_off= '<span class="label label-default">Pending</span>';
									var html = ''
									if(row.status == "active"){
										html = btn_on;
									}else{
										html = btn_off
									}								
									return html
								}
							},
							{
								"data": "asset_id",
								"orderable": false,
								"searchable": false,
								"className": "text-center",
								"render": function(data, type, row) {
									var btn_sales = '<button class="btn btn-xs btn-info btn-sales" title="Jual"><i class="fa fa-shopping-cart" aria-hidden="true"></i></button>';
									var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus" title="hapus"><i class="fa fa-trash"></i></button>';
									var btn_detail = '<button class="btn btn-xs btn-primary btn-detail" title="history penyusutan"><i class="fa fa-list"></i></button>';
									var qty = row.qty_akui-row.qty_sales;
									if (qty <= 0) {
										return btn_detail+" "+btn_hapus
									}else{
										return btn_sales+" "+btn_detail+" "+btn_hapus
									}
								}
							}
						],
						order: [[1, 'asc']]
					});//end datatable init					
				});	// end jequery
            },
            onDel(id){
                Alert.confirm("Anda yakin akan menonaktifkan aset","Proses menghapus aset akan menonaktifkan aset selamanya dan tidak bisa diaktifkan lagi").then(function(result){
					if (!result.value) {
						return;
					}
					$.ajax({
						type: "post",
						url: "<?=current_url() ?>/assets_off",
						data:{asset_id:id},
						beforeSend(){
							loading.show();
						},
						success: function (data) {
							loading.hide();
							Alert.success("success","data Asset berhasil dinonaktifkan");
							$('#table-active').DataTable().ajax.reload();
						},
						error(err){
							loading.hide();
							Alert.error('Error','Gagal menghapus data')
							console.log(err);
						}
					});
				})
            },
			onDetail(id){
				
				$.ajax({
					type: "get",
					url: "<?=current_url()?>/susut_history",
					data: {asset_fkid:id},
					dataType: "json",
					beforeSend(){
						loading.show()
					},
					success(res) {
						loading.hide()
						$("#modal-history").modal('show')
						vmActive.historySusut = res
					},
					error(err){
						loading.hide();
						Alert.error("error")
						console.log(err);
					}
				});	
			},
			onSubmitSales(){
				if (!this.validate(this.formSales)) {
					return;
				}
				var data = {};
				for (var key in this.formSales) {
					if (this.formSales.hasOwnProperty(key)) {
						data[key] = this.formSales[key].value;
					}
				}
				data.price =clearFormat(data.price)
				data.hpp = data.hpp*data.qty
				// cek qty
				if (parseFloat(data.qty)> parseFloat(this.filter.qty_akui)) {
					return Alert.warning("warning","jumlah penjualan melebihi jumlah pembelian awal")
				}
				$.ajax({
					type: "POST",
					url: "<?=current_url()?>/save_sales",
					data: data,
					dataType: "json",
					beforeSend(){
						loading.show()
					},
					success(res) {
						loading.hide();
						Alert.success("Success","Data berrhasil disimpan")
						$("#modal-sales").modal("hide");
						$("#table-active").DataTable().ajax.reload()
					},
					error(err){
						loading.hide();
						Alert.error("error")
						console.log(err);
					}
				});

			},
			milisToLocal(data){
				return milisToLocal(data)
			},
			currency(data){
				return currency(data)
			},
			clear(){
				this.clearForm(this.formSales);
			}
        },
		watch:{
			'formSales.payment.value'(newVal){
				if (newVal=="card") {
					this.formSales.bank.value = ""
					this.formSales.bank.required = true
					setTimeout(() => {
						$('#bank').select2({
							allowClear: true,
							placeholder: '-- Pilih Unit -- ',
						});
					}, 200);
				}else{
					this.formSales.bank.value = ""
					this.formSales.bank.required = false
				}
			},
			'formSales.qty.value'(newVal){
				this.formSales.perolehan.value = (clearFormat(this.filter.nilai_akui)-this.filter.akumulasi_susut)/this.filter.qty_akui*newVal
			}
		},
		computed:{
			subTotal(){
				let subTotal = this.formSales.qty.value*clearFormat(this.formSales.price.value)
				return currency(subTotal);
			}
		},
        mounted() {
            // $("#table-active").dataTable();
            $(function () {
                $('#selectOutletActive').selectpicker({
					title:"All Outlet",
					style: 'form-control btn btn-sm btn-info',
					selectedTextFormat:"count >3",
					dropdownAlignRight:true,
					width:"auto",
					liveSearch:true,
					actionsBox:true
				});
				
            });
        },
    })
    $(function () {
        $('#table-active').on('click', '.btn-hapus', function () {
            var data = $("#table-active").DataTable().row($(this).closest('tr')).data();
            vmActive.onDel(data.asset_id)
        });

        $('#table-active').on('click', '.btn-detail', function () {
            var data = $("#table-active").DataTable().row($(this).closest('tr')).data();
			vmActive.filter.asset_name = data.asset_name
			vmActive.filter.outlet_name = data.outlet_name
			vmActive.filter.tgl_akui = milisToLocal(data.tgl_akuisisi)
			vmActive.filter.nilai_akui = data.biaya_akuisisi
			vmActive.filter.qty_akui = data.qty_akui
			vmActive.filter.unit = data.unit
            vmActive.onDetail(data.asset_id)
        });
		
        $('#table-active').on('click', '.btn-sales', function () {
            var data = $("#table-active").DataTable().row($(this).closest('tr')).data();
			vmActive.clear()
			vmActive.filter.asset_name = data.asset_name
			vmActive.filter.outlet_name = data.outlet_name
			vmActive.filter.tgl_akui = milisToLocal(data.tgl_akuisisi)
			vmActive.filter.nilai_akui = currency(data.biaya_akuisisi)
			vmActive.filter.qty_akui = data.qty_akui
			vmActive.filter.unit = data.unit
			vmActive.filter.akumulasi_susut = data.akumulasi_susut
			var nilaiBuku  = data.total_susut-(data.total_susut/data.qty_akui)
			vmActive.formSales.hpp.value = data.total_susut/data.qty_akui
			vmActive.formSales.asset_fkid.value = data.asset_id
			$("#modal-sales").modal("show");
        });
    });
</script>