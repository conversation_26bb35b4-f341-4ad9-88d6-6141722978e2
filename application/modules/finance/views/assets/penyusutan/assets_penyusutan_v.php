<div id="app-susut">
    <div class="row">
        <div class="col-sm-6 col-sm-offset-6">
            <div class="pull-right">
                <div class="btn-group">
                    <select id="selectOutletSusut" class="form-control btn btn-info" v-model="filter.outlet">
                        <?php foreach ($form_select_outlet as $a): ?>                   
                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                        <?php endforeach ?>
                    </select>                                   
                </div>
                <div class="btn-group">
                    <button class="btn btn-default btn-block btn-sm" @click="dataTable()" type="button">Apply</button>                  
                </div>
            </div>	                
        </div>
    </div>
    <hr style="margin:10px">
    <div class="row">
        <div class="col-sm-12">
            <table class="table table-report datatable" id="table-susut" width="100%">
                <thead>
                    <tr>
                        <th width="5px">No</th>
                        <th>Tgl Susut</th>
                        <th>Nama Aset</th>
                        <th>Outlet</th>
                        <th>Tgl Akuisisi</th>
                        <th>Biaya Akuisisi</th>
                        <th>Penyusutan</th>
                        <th>Nilai Buku</th>
                        <th><i class="fa fa-cog"></i></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center">Pilih Outlet Untuk menampikan data</td>
                    </tr>
                </tbody>
            </table>			
        </div>			
    </div>
</div>

<script>
    var vmSusut = new Vue({
        el:"#app-susut",
        data:{
            filter:{
                outlet:""
            },
        },
        methods: {
            dataTable(){
                $(document).ready(function() {
					var t = $("#table-susut").DataTable({
                        destroy : true,
						ajax: {
				            url: "<?=current_url() ?>/data_susut",
				            type: "POST",
                            data : {outlet:vmSusut.filter.outlet}
				        },
						columns: [
							{
								"data": null,
								"class":"text-center",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "tgl_susut",
                                "className": "text-center",
                                "render": function(data, type, row) {
                                    return milisToLocal(data);
                                }
							},							
							{
								"data": "asset_name",
							},							
							{
								"data": "outlet_name",
							},						
							{
								"data": "tgl_akuisisi",
                                "className": "text-center",
                                "render": function(data, type, row) {
                                    return milisToLocal(data);
                                }
							},						
							{
								"data": "biaya_akuisisi",
                                "className": "text-right",
                                "render": function(data, type, row) {
                                    return currency(parseFloat(data));
                                }
							},					
							{
								"data": "susut",
                                "className": "text-right",
                                "render": function(data, type, row) {
                                    return currency(parseFloat(data));
                                }
							},					
							{
								"data": "nilai_buku",
                                "className": "text-right",
                                "render": function(data, type, row) {
                                    return currency(parseFloat(data));
                                }
							},
							{
								"data": "asset_id",
								"orderable": false,
								"searchable": false,
								"className": "text-center",
								"render": function(data, type, row) {
									var btn_posting = '<button class="btn btn-sm btn-warning btn-posting" title="Posting"><i class="fa fa-edit"></i> Post Jurnal</button>';
                                    return btn_posting
								}
							}
						],
						order: [[1, 'asc']]
					});//end datatable init					
				});	// end jequery
            },
            onPost(penyusutan_id){
                Alert.confirm("Anda yakin akan memposting aset ke jurnal","Data yang sudah diposting tidak bisa dirubah lagi").then(function(result){
					if (!result.value) {
						return;
					}
					$.ajax({
						type: "post",
						url: "<?=current_url() ?>/asset_posting",
						data:{penyusutan_id:penyusutan_id},
						beforeSend(){
							loading.show();
						},
						success: function (data) {
							loading.hide();
							Alert.success("success","data Asset berhasil disimpan");
							$('#table-susut').DataTable().ajax.reload();
						},
						error(err){
							loading.hide();
							Alert.error('Error','Gagal disimpan')
							console.log(err);
						}
					});
				})
            }
        },
        mounted() {
            // $("#table-susut").dataTable();
            $(function () {
                $('#selectOutletSusut').selectpicker({
					title:"All Outlet",
					style: 'form-control btn btn-sm btn-info',
					selectedTextFormat:"count >3",
					dropdownAlignRight:true,
					width:"auto",
					liveSearch:true,
					actionsBox:true
				});
				
            });
        },
    })
    $(function () {
        $('#table-susut').on('click', '.btn-posting', function () {
            var data = $("#table-susut").DataTable().row($(this).closest('tr')).data();
            vmSusut.onPost(data.penyusutan_id)
        });
    });
</script>