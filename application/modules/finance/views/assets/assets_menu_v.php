    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <ul class="nav nav-tabs tab-uniq" id="myTab">
                    <li class="active"><a data-toggle="tab" href="#menu1" style="color: #fff;">Aset Pending</a></li>
                    <li ><a data-toggle="tab" href="#menu2" style="color: #fff;">Aset Aktif</a></li>
                    <!-- <li ><a data-toggle="tab" href="#menu3" style="color: #fff;">Penyusutan</a></li> -->
                    <li class="pull-right">
                        <button class="btn btn-primary btn-sm" id="btn-add"><i class="fa fa-plus"></i> Add New</button>
                    </li>
                </ul>
            
            </div>
            <div class="tab-content">
                <div id="menu1" class="tab-pane fade in active">
                    <div class="col-md-12 col-sm-12">
                        <div class="content-uniq">
                            <?php $this->load->view('finance/assets/penyusutan/assets_pending_v');?>
                        </div>
                    </div>
                </div>

                <div id="menu2" class="tab-pane fade in">
                    <div class="col-md-12 col-sm-12">
                        <div class="content-uniq">
                            <?php $this->load->view('finance/assets/penyusutan/assets_active_v');?>
                        </div>
                    </div>
                </div>

                <!-- <div id="menu3" class="tab-pane fade in">
                    <div class="col-md-12 col-sm-12">
                        <div class="content-uniq">
                            <?php $this->load->view('finance/assets/penyusutan/assets_penyusutan_v');?>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
        
    </div>

    

    <!-- Modal susut -->
    <div class="modal fade" id="modal-asset-susut" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Depresiasi</h4>
                </div>

                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-6">	
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Outlet*</small>
                                    <select id="formOutlet" name="formOutlet" class="form-control btn btn-info" v-model="inputSusut.outlet_id.value" v-select="inputSusut.outlet_id.value">
                                        <option value="" selected disabled>-- Pilih Outlet --</option>
                                        <?php foreach ($form_select_outlet as $a): ?>                   
                                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>      
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="asset-name">Nama Aset*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.pd_fkid.value" v-select="inputSusut.pd_fkid.value" id="asset-name" name="asset-name" width="100%">
                                        <option value="" selected disabled>-- Pilih Master Asset --</option>
                                        <option v-for="i,idx in master.assets" :value="i.asset_id">{{i.asset_name}}</option>
                                    </select>
                                    <small class="text-warning">{{inputSusut.aset_id.error}}</small>
                                </div>		
                                <div class="form-group" style="margin-top:15px">
                                    <small class="control-label" for="unit">Akun Aset Tetap*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_aset.value" v-select="inputSusut.account_aset.value" id="account_aset" width="100%">
                                        <option value="" selected disabled>-- Pilih Akun --</option>	
                                        <?php foreach ($account as $a): ?>                   
                                            <option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="text-warning">{{inputSusut.account_aset.error}}</small>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Tanggal Akuisisi*</small>
                                    <input type="date" :class="['form-control form-dark',inputSusut.tgl_akui.error ? 'is-invalid' : '']" id="tgl_akui" name="tgl_akui" v-model="inputSusut.tgl_akui.value" v-datepicker="inputSusut.tgl_akui">
                                    <small class="text-warning">{{inputSusut.tgl_akui.error}}</small>
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Biaya Akuisisi*</small>
                                    <input type="text" :class="['form-control form-dark',inputSusut.biaya_akui.error ? 'is-invalid' : '']" id="biaya_akui" name="biaya_akui" v-model="inputSusut.biaya_akui.value" v-money="inputSusut.biaya_akui.value">
                                    <small class="text-warning">{{inputSusut.biaya_akui.error}}</small>	
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Qty*</small>
                                    <input type="text" :class="['form-control form-dark',inputSusut.qty.error ? 'is-invalid' : '']" id="qty" name="qty" v-model="inputSusut.qty.value" v-number="inputSusut.qty.value">
                                    <small class="text-warning">{{inputSusut.qty.error}}</small>	
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Akun Dikreditkan*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_kredit.value" v-select="inputSusut.account_kredit.value" id="account_kredit" name="account_kredit" width="100%">
                                    <option value="" selected disabled>-- Pilih Akun --</option>
                                        <?php foreach ($account as $a): ?>                   
                                            <option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="text-warning">{{inputSusut.account_kredit.error}}</small>
                                </div>
                            </div>
                        </div>	
                        <br>
                        <hr>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Aset non-depresiasi*</small>
                                    <select name="metode" class="form-control form-dark" id="non_depresiasi" name="non_depresiasi" v-model="inputSusut.non_depresiasi.value">
                                        <option value="0" selected>No</option>
                                        <option value="1">Yes</option>
                                    </select>
                                    <small class="text-warning">{{inputSusut.non_depresiasi.error}}</small>	
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Metode*</small>
                                    <select name="metode" class="form-control form-dark" id="metode" v-model="inputSusut.metode.value" disabled="">
                                        <option value="straight_line" selected>Straight Line</option>
                                        <option value="reducing_balance">Reducing Balance</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Masa Manfaat*</small>
                                    <div class="row">
                                        <div class="col-sm-8">
                                            <input type="text" :class="['form-control form-dark',inputSusut.masa_manfaat.error ? 'is-invalid' : '']" id="masa_manfaat" name="masa_manfaat" v-model="inputSusut.masa_manfaat.value" v-number="inputSusut.masa_manfaat.value" :disabled="inputSusut.non_depresiasi.value==0">
                                        </div>
                                        <div class="col-sm-3">
                                            <span>Tahun</span>
                                        </div>
                                    </div>
                                    <small class="text-warning">{{inputSusut.masa_manfaat.error}}</small>	
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Nilai/Tahun*</small>
                                    <div class="row">
                                        <div class="col-sm-8">
                                            <input type="text" :class="['form-control form-dark',inputSusut.nilai_tahun.error ? 'is-invalid' : '']" id="nilai_tahun" name="nilai_tahun" v-model="inputSusut.nilai_tahun.value" v-money="inputSusut.nilai_tahun.value" disabled>
                                        </div>
                                        <div class="col-sm-3">
                                            <span>Persen</span>
                                        </div>
                                    </div>
                                    <small class="text-warning">{{inputSusut.nilai_tahun.error}}</small>	
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Akun Penyusutan*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.accout_susut.value" v-select="inputSusut.accout_susut.value" id="accout_susut_add" name="accout_susut_add" width="100%" :disabled="inputSusut.non_depresiasi.value==0">
                                    <option value="" selected disabled>-- Pilih Akun --</option>    
                                    <?php foreach ($account as $a): ?>                   
                                            <option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="text-warning">{{inputSusut.accout_susut.error}}</small>
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Akumulasi Akun Penyusutan*</small>
                                    <select :class="['form-control btn btn-info selectAkun']" v-model="inputSusut.account_akumulasi.value" v-select="inputSusut.account_akumulasi.value" id="account_akumulasi_add" name="account_akumulasi_add" width="100%" :disabled="inputSusut.non_depresiasi.value==0">
                                    <option value="" selected disabled>-- Pilih Akun --</option>    
                                    <?php foreach ($account as $a): ?>                   
                                            <option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="text-warning">{{inputSusut.account_akumulasi.error}}</small>
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Akumulasi Penyusutan*</small>
                                    <input type="text" :class="['form-control form-dark',inputSusut.total_akumulasi.error ? 'is-invalid' : '']" id="total_akumulasi" name="total_akumulasi" v-model="inputSusut.total_akumulasi.value" v-money="inputSusut.total_akumulasi.value" :disabled="inputSusut.non_depresiasi.value==0">
                                    <small class="text-warning">{{inputSusut.total_akumulasi.error}}</small>	
                                </div>
                                <div class="form-group" style="margin-top:15px">
                                    <small for="email">Pada Tanggal*</small>
                                    <input type="date" :class="['form-control form-dark',inputSusut.tgl_susut.error ? 'is-invalid' : '']" id="tgl_susut" name="tgl_susut" v-model="inputSusut.tgl_susut.value" v-datepicker="inputSusut.tgl_susut.value" :disabled="inputSusut.non_depresiasi.value==0">
                                    <small class="text-warning">{{inputSusut.tgl_susut.error}}</small>	
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" id="btn-btl" data-dismiss="modal">Batal</button>
                        <button type="button" @click="checkSubmit('pending')" class="btn btn-primary" id="btn-simpan">Simpan</button>
                        <button type="button" @click="checkSubmit('active')" class="btn btn-uniq-primary" id="btn-post">Simpan & Posting</button>
                    </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

<script>
    var appModalAsset = new Vue({
        el:"#modal-asset-susut",
        data:{
            master:{
                assets:[]
            },
            inputSusut:{
				outlet_id:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
				aset_id:{
					value:"",
					error:"",
					allowClear: true,
                    required: false,
				},
				pd_fkid:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},

				account_aset:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				tgl_akui:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				biaya_akui:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				account_kredit:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				non_depresiasi:{
					value:"0",
					error:"",
					allowClear: true,
                    required: true,
				},
				metode:{
					value:"straight_line",
					error:"",
                    required: true,
					disabled:true,
				},
				masa_manfaat:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				nilai_tahun:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				accout_susut:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				account_akumulasi:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				total_akumulasi:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				tgl_susut:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:true,
				},
				qty:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
					disabled:false,
				},
			}
        },
        methods: {
            onAdd(){
                $("#modal-asset-susut").modal("show")
                this.resetForm()
                setTimeout(() => {
                    $('.selectAkun').selectpicker('refresh');
                    $('#formOutlet').selectpicker('refresh');
                    $('#asset-name').selectpicker('refresh');
                }, 300);
            },
            onSubmit(type){
                var data = {}
                if (!this.validate(this.inputSusut)) {
                    return;
                }

                if (dateToMilis(this.inputSusut.tgl_akui.value) > dateToMilis(this.inputSusut.tgl_susut.value)) {
                    return Alert.warning("oops..","tanggal penyusutan tidak bileh lebih kecil dari tanggal akui")
                }
                var data = {};
                for (var key in this.inputSusut) {
                    if (this.inputSusut.hasOwnProperty(key)) {
                        data[key] = this.inputSusut[key].value;
                    }
                }
                data.total_akumulasi = clearFormat(data.total_akumulasi)
                // data.nilai_tahun = clearFormat(data.nilai_tahun)
                data.biaya_akui = clearFormat(data.biaya_akui)
                data.tgl_susut = dateToMilis(data.tgl_susut)
                data.tgl_akui = dateToMilis(data.tgl_akui)
                $.ajax({
                    type: "POST",
                    url: "<?=current_url()?>/save_assets/"+type,
                    data: data,
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success(res) {
                        loading.hide();
                        Alert.success("Success","data berhasil disimpan");
                        $("#modal-asset-susut").modal("hide");
                    },
                    error(err){
                        console.log(err);
                        loading.hide();
                        Alert.error("error","data gagal disimpan")
                    }
                }); 
            },
            checkSubmit(type){
                if (type == 'active') {
                    Alert.confirm("Anda yakin akan memposting aset","Data yang diposting tidak akan bisa dirubah lagi").then(function(result){
                        if (!result.value) {
                            return;
                        }
                        appModalAsset.onSubmit(type)
                    })
                }else{
                    this.onSubmit(type)
                }
            },
            resetForm(){
                this.clearForm(this.inputSusut)
                $('#account_akumulasi_add').attr('disabled',true)
                $('#accout_susut_add').attr('disabled',true)

                $('select[name=account_akumulasi_add]').val("");
                $('select[name=accout_susut_add]').val("");

                $('select[name=formOutlet]').val("");
                $('select[name=asset-name]').val("");
                this.inputSusut.non_depresiasi.value="0"
                this.inputSusut.masa_manfaat.required = false
                this.inputSusut.nilai_tahun.required = false
                this.inputSusut.accout_susut.required = false
                this.inputSusut.account_akumulasi.required = false
                this.inputSusut.total_akumulasi.required = false
                this.inputSusut.tgl_susut.required = false
                $('.selectAkun').selectpicker('refresh');
                $('#formOutlet').selectpicker('refresh');
                $('#asset-name').selectpicker('refresh');

            }
        },
        watch:{
            'inputSusut.non_depresiasi.value'(newval){
				if (newval == 1) {
					$('#accout_susut_add').attr('disabled',false)
					$('#account_akumulasi_add').attr('disabled',false)

					this.inputSusut.masa_manfaat.required = true
					this.inputSusut.nilai_tahun.required = true
					this.inputSusut.accout_susut.required = true
					this.inputSusut.account_akumulasi.required = true
					this.inputSusut.total_akumulasi.required = true
					this.inputSusut.tgl_susut.required = true
				}else{
					$('#account_akumulasi_add').attr('disabled',true)
					$('#accout_susut_add').attr('disabled',true)
					$('select[name=account_akumulasi_add]').val("");
					$('select[name=accout_susut_add]').val("");
					this.inputSusut.accout_susut.value= "";
					this.inputSusut.account_akumulasi.value= "";

					this.inputSusut.masa_manfaat.value = ""
					this.inputSusut.nilai_tahun.value = ""
					this.inputSusut.total_akumulasi.value = ""
					this.inputSusut.tgl_susut.value = ""

					this.inputSusut.masa_manfaat.required = false
					this.inputSusut.nilai_tahun.required = false
					this.inputSusut.accout_susut.required = false
					this.inputSusut.account_akumulasi.required = false
					this.inputSusut.total_akumulasi.required = false
					this.inputSusut.tgl_susut.required = false
				}
				$('.selectAkun').selectpicker('refresh');
				// console.log(newval);
			},
            'inputSusut.outlet_id.value'(newVal){
                $.ajax({
                    type: "post",
                    url: "<?=current_url()?>/get_assets",
                    data: {outlet_id :newVal},
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success(res) {
                        loading.hide()
                        appModalAsset.master.assets = res
                        setTimeout(() => {
                            $('#asset-name').selectpicker('refresh');
                        }, 100);
                    },
                    error(err){
                        loading.hide()
                        Alert.error("error")
                        console.log(err);
                    }
                });
            },
            'inputSusut.masa_manfaat.value'(newVal){
				let nilaiTahun =100/newVal
				this.inputSusut.nilai_tahun.value = parseFloat(nilaiTahun).toFixed(2)
			}
        }
    })

    $("#btn-add").on("click",function () { 
        appModalAsset.onAdd()
    })

    $('#asset-name').selectpicker({
        style: 'btn btn-info',
        selectedTextFormat:"count >3",
        width:"100%",
        liveSearch:true,
        actionsBox:true,
        allowClear: true,
    });

    $('#formOutlet').selectpicker({
        style: 'btn btn-info',
        selectedTextFormat:"count >3",
        width:"100%",
        liveSearch:true,
        actionsBox:true,
        allowClear: true,
    });

    $('a[data-toggle="tab"]').on('show.bs.tab', function(e) {
			localStorage.setItem('activeTabAset', $(e.target).attr('href'));
	});
	var activeTabAset = localStorage.getItem('activeTabAset');
	if(activeTabAset){
		$('#myTab a[href="' + activeTabAset + '"]').tab('show');
	}
</script>