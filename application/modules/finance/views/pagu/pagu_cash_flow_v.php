<style lang="">
    .table-hover tbody tr:hover td,
    .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }
</style>

<div id="app">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-gears"></i> Pagu Cash Flow </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
                    <div class="btn-group pull-right">
                        <button class="btn btn-primary btn-block" @click="onAdd" type="button"> Tambah <i
                                class="fa fa-plus"></i></button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-responsive table-report datatable" id="table-pagu" width="100%">
                        <thead>
                            <tr>
                                <th width="1%">No</th>
                                <th>Outlet</th>
                                <th>#</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-form" data-backdrop="static" data-keyboard="false">
        <form action="javascript:void(0)" id="form-laba">
            <div class="modal-dialog">
                <div class="modal-content" style="background: #27292a;">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Pagu Cash Flow</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-10">
                                <div class="form-group">
                                    <label for="outlet">Outlet*</label><br>
                                    <select class="form-control" id="input-outlet" name="outlet[]" multiple required>
                                        <?php foreach ($form_select_outlet as $a): ?>
                                            <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 table-responsive">
                                <table class="table table-report table-hover" id="labaRugiTable" width="100%">
                                    <tbody>
                                        <tr>
                                            <td class="text-bold">Pembayaran ke suplier</td>
                                            <td width="20%" class="text-center text-bold">
                                                <input type="text" class="form-control number" value="0"
                                                    id="pembayaran_suplier" name="pembayaran_suplier">
                                            </td>
                                            <td>%</td>
                                        </tr>
                                        <tr>
                                            <td class="text-bold">Persediaan</td>
                                            <td class="text-center text-bold">
                                                <input type="text" class="form-control number" value="0" id="persediaan"
                                                    name="persediaan">
                                            </td>
                                            <td>%</td>
                                        </tr>
                                        <tr>
                                            <td class="text-bold">Operasional</td>
                                            <td class="text-center text-bold">
                                                <input type="text" class="form-control number" value="0"
                                                    id="operasional" name="operasional">
                                            <td>%</td>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="text-bold">Sisa Hutang Usaha periode berjalan</td>
                                            <td class="text-center text-bold">
                                                <input type="text" class="form-control number" value="0"
                                                    id="sisa_hutang" name="sisa_hutang">
                                            </td>
                                            <td>%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary"
                            data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading">Simpan</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    $(function () {
        var t = $("#table-pagu").DataTable({
            processing: true,
            serverSide: true,
            fixedHeader: true,
            ajax: {
                url: "<?= base_url() ?>finance/accounts/account_pagu/data_table",
                type: "POST",
            },
            dom: "<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>",
            columns: [
                {
                    "data": null,
                    "orderable": true,
                    "searchable": false,
                },
                { "data": "name" },
                {
                    "orderable": false,
                    "searchable": false,
                    "className": "text-center",
                    "render": function (data, type, row) {
                        var btn_edit = '<button class="btn btn-xs btn-warning btn-edit"><i class="fa fa-edit"></i></button>';
                        return btn_edit
                    }
                }
            ],
            order: [[1, 'asc']],
        });//end datatable init
    });
</script>


<script>
    var vm = new Vue({
        el: "#app",
        data: {

        },
        methods: {
            onAdd() {
                $("#modal-form").modal('show')
                $("#pembayaran_suplier").val(0)
                $("#persedaan").val(0)
                $("#operasional").val(0)
                $("#sisa_hutang").val(0)
                setTimeout(() => {
                    $('#input-outlet').selectpicker('val', '');
                    $('#input-outlet').selectpicker('refresh').trigger('change');
                }, 300);
            },
            onEdit(outlet_id) {
                $.ajax({
                    type: "POST",
                    url: "<?= base_url('finance/pagu/pagu_cash_flow/get_by_outlet') ?>",
                    data: { outlet_id: outlet_id },
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success(res) {
                        loading.hide()
                        if (res) {
                            for (const i in res) {
                                $("#" + res[i].key).val(res[i].pagu.replace('.', ','))
                            }
                            setTimeout(() => {
                                $('#input-outlet').selectpicker('val', outlet_id);
                                $('#input-outlet').selectpicker('refresh').trigger('change');
                            }, 300);
                        } else {
                            $("#modal-form").modal('show')
                            $("#pembayaran_suplier").val(0)
                            $("#persediaan").val(0)
                            $("#operasional").val(0)
                            $("#sisa_hutang").val(0)
                            setTimeout(() => {
                                $('#input-outlet').selectpicker('val', '');
                                $('#input-outlet').selectpicker('refresh').trigger('change');
                            }, 300);
                        }

                        $("#modal-form").modal('show');
                    },
                    error(err) {
                        loading.hide();
                        Alert.error('error', 'gagal menampilkan data')
                        console.log(err);

                    }
                });
            }
        },
    })

    $(function () {
        $("#form-laba").on('submit', function (e) {
            e.preventDefault();
            let formData = $(this).serialize()
            $.ajax({
                type: "post",
                url: "<?= base_url('finance/pagu/pagu_cash_flow/save') ?>",
                data: formData,
                dataType: "json",
                beforeSend() {
                    loading.show()
                },
                success(res) {
                    loading.hide()
                    Alert.success('success', 'data berhasil disimpan')
                    $("#modal-form").modal('hide')
                },
                error(err) {
                    Alert.error('error', 'data gagal disimpan')
                    loading.hide()
                    console.log(err);

                }
            });
        })

        $('#table-pagu').on('click', '.btn-edit', async function () {
            var data = $("#table-pagu").DataTable().row($(this).closest('tr')).data();
            vm.onEdit(data.outlet_id);
        });

        $('#input-outlet').selectpicker({
            title: "Pilih Outlet",
            style: 'btn btn-default btn-block',
            selectedTextFormat: "count >3",
            dropdownAlignRight: true,
            width: '100%',
            liveSearch: true,
            actionsBox: true,
            color: 'white'
        });

        $('.number').on('input', function () {
            this.value = this.value.replace(/[^0-9,]/g, ''); // Allow only digits and commas
        });
    });
</script>