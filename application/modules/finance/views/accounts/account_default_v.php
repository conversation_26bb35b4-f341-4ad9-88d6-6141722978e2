<style>
    .select2-container--default .select2-selection--single {
        background-color: #27292a;
        border: 1px solid #27292a;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #c8bebe;
    }
    .select2-dropdown {
        background-color: #2b2d2e;
        border: 1px solid #555050;
    }
    .select2-search__field {
        background-color: #323738;
    }
</style>
<div class="container-fluid form-horizontal" id="app" style="margin-top: 10px;">
    <div class="panel panel-uniq">
        <div class="panel-heading"><b>Sales</b></div>
        <div class="panel-body">
            <div class="col-sm-6" v-for="i,idx in dataAccount.sales">
                <div class="form-group" >
                    <label class="control-label col-sm-4">{{i.title}}</label>
                    <div class="col-sm-8">
                        <select  :class="['form-control form-control-sm select2select']" data-live-search="true" style="width:100%" v-model="i.id" v-select="i.id" require title="- Pilih <PERSON>kun -">
                            <?php foreach ($account as $a): ?>
                                <option data-type="1" data-key="1" value="<?=$a['account_id']?>"><?=$a['code']."-".htmlentities($a['name'])." (".$a['category'].")" ?></option>
                            <?php endforeach ?>
                        </select>	
                    </div>
                </div> 
            </div>
        </div>
    </div>

    <!-- purchase -->
    <div class="panel panel-uniq">
        <div class="panel-heading"><b>Pembelian</b></div>
        <div class="panel-body">
            <div class="col-sm-6" v-for="i,idx in dataAccount.purchase">
                <div class="form-group">
                    <label class="control-label col-sm-4">{{i.title}}</label>
                    <div class="col-sm-8">
                        <select  :class="['form-control form-control-sm btn-info select2select']" data-live-search="true" style="width:100%" v-model="i.id" v-select="i.id" require title="- Pilih Akun -">
                            <?php foreach ($account as $a): ?>
                                <option value="<?=$a['account_id']?>"><?=$a['code']."-".htmlentities($a['name'])." (".$a['category'].")" ?></option>
                            <?php endforeach ?>
                        </select>	
                    </div>
                </div>                 
            </div>
        </div>
    </div>
    <!-- arp -->
    <div class="panel panel-uniq">
        <div class="panel-heading"><b>AR/AP</b></div>
        <div class="panel-body">
            <div class="col-sm-6" v-for="i,idx in dataAccount.arp">
                <div class="form-group">
                    <label class="control-label col-sm-4">{{i.title}}</label>
                    <div class="col-sm-8">
                        <select  :class="['form-control form-control-sm btn-info select2select']" data-live-search="true" style="width:100%" v-model="i.id" v-select="i.id" require title="- Pilih Akun -">
                            <?php foreach ($account as $a): ?>
                                <?php if ($a['category']=='Akun Piutang') :?>
                                <option value="<?=$a['account_id']?>"><?=$a['code']."-".htmlentities($a['name'])." (".$a['category'].")" ?></option>
                                <?php endif?>
                            <?php endforeach ?>
                        </select>	
                    </div>
                </div>                
            </div>
        </div>
    </div>
    <!-- persediaan -->
    <div class="panel panel-uniq">
        <div class="panel-heading"><b>Persediaan</b></div>
        <div class="panel-body">
            <div class="col-sm-6" v-for="i,idx in dataAccount.persediaan">
                <div class="form-group">
                    <label class="control-label col-sm-4">{{i.title}}</label>
                    <div class="col-sm-8">
                        <select  :class="['form-control form-control-sm btn-info select2select']" data-live-search="true" style="width:100%" v-model="i.id" v-select="i.id" require title="- Pilih Akun -">
                            <?php foreach ($account as $a): ?>
                                <option value="<?=$a['account_id']?>"><?=$a['code']."-".htmlentities($a['name'])." (".$a['category'].")" ?></option>
                            <?php endforeach ?>
                        </select>	
                    </div>
                </div>                
            </div>
        </div>
    </div>
    <!-- extra -->
    <div class="panel panel-uniq">
        <div class="panel-heading"><b>Lainya</b></div>
        <div class="panel-body">
            <div class="col-sm-6" v-for="i,idx in dataAccount.extra">
                <div class="form-group">
                    <label class="control-label col-sm-4">{{i.title}}</label>
                    <div class="col-sm-8">
                        <select  :class="['form-control form-control-sm btn-info select2select']" data-live-search="true" style="width:100%" v-model="i.id" v-select="i.id" require title="- Pilih Akun -">
                            <?php foreach ($account as $a): ?>
                                <option value="<?=$a['account_id']?>"><?=$a['code']."-".htmlentities($a['name'])." (".$a['category'].")" ?></option>
                            <?php endforeach ?>
                        </select>	
                    </div>
                </div>                
            </div>
        </div>
    </div>
    <div class="panel panel-uniq">
        <div class="panel-body">
            <div class="col-sm-9">
                <p class="text-right">*Pengaturan akun akan tersimpan jika sudah dilakukan penyimpanan</p>
            </div>
            <div class="col-sm-3">
                <button @click="onSubmit()" class="btn btn-primary btn-block pull-right">Simpan</button>
            </div>
        </div>
        
    </div>
</div>

<script>
    $(function () {
        $('.select2select').on('select2:select', function (e) {
            var data = $(e.params.data.element).data();
            

            //  var data = e.params.data;
            //  console.log(data);
        })

        // $('.select2select option:selected').attr('data-id')
    });
    
    var vm = new Vue({
        el:"#app",
        data:{
            dataAccount :{
                sales:{
                    sales_pendapatan:{
                        title:'Pendapatan Penjualan:',
                        id:'',
                        key:'1',
                        type:'1'
                    },
                    sales_diskon:{
                        title:'Diskon Penjualan:',
                        id:'',
                        key:'2',
                        type:'1'
                    },
                    sales_retur:{
                        title:'Retur Penjualan:',
                        id:'',
                        key:'3',
                        type:'1'
                    },
                    sales_pengiriman:{
                        title:'Pengiriman Penjualan:',
                        id:'',
                        key:'4',
                        type:'1'
                    },
                    sales_pembayaran:{
                        title:'Pembayaran di Muka:',
                        id:'',
                        key:'5',
                        type:'1'
                    },
                    sales_penjualan:{
                        title:'Penjualan Belum Ditagih:',
                        id:'',
                        key:'6',
                        type:'1'
                    },
                    sales_piutang:{
                        title:'Piutang Belum Ditagih:',
                        id:'',
                        key:'7',
                        type:'1'
                    },
                    sales_tax:{
                        title:'Hutang Pajak Penjualan:',
                        id:'',
                        key:'8',
                        type:'1'
                    },
                    sales_cash:{
                        title:'Sales Cash:',
                        id:'',
                        key:'25',
                        type:'1'
                    },
                    sales_bank:{
                        title:'Sales Bank:',
                        id:'',
                        key:'26',
                        type:'1'
                    },
                },
                purchase:{
                    purchase_cogs:{
                        title:'Pembelian (COGS):',
                        id:'',
                        key:'9',
                        type:'2'
                    },
                    purchase_pengiriman:{
                        title:'Pengiriman Pembelian:',
                        id:'',
                        key:'10',
                        type:'2'
                    },
                    purchase_payment:{
                        title:'Uang Muka Pembelian:',
                        id:'',
                        key:'11',
                        type:'2'
                    },
                    purchase_hutang:{
                        title:'Hutang Belum Ditagih:',
                        id:'',
                        key:'12',
                        type:'2'
                    },
                    purchase_tax:{
                        title:'Pajak Pembelian:',
                        id:'',
                        key:'13',
                        type:'2'
                    },
                    purchase_operasional:{
                        title:'Operasional:',
                        id:'',
                        key:'22',
                        type:'2'
                    },
                    purchase_cash:{
                        title:'Purchase Cash:',
                        id:'',
                        key:'23',
                        type:'2'
                    },
                    purchase_bank:{
                        title:'Purchase Bank:',
                        id:'',
                        key:'24',
                        type:'2'
                    },
                    purchase_debitmemo:{
                        title:'Purchase Debit Memo:',
                        id:'',
                        key:'27',
                        type:'2'
                    },
                    purchase_assets:{
                        title:'Purchase Asset:',
                        id:'',
                        key:'30',
                        type:'2'
                    },
                },
                persediaan:{
                    persediaan_persediaan:{
                        title:'Persediaan :',
                        id:'',
                        key:'14',
                        type:'10'
                    },
                    persediaan_umum:{
                        title:'Persediaan Umum :',
                        id:'',
                        key:'15',
                        type:'10'
                    },
                    persediaan_rusak:{
                        title:'Persediaan Rusak :',
                        id:'',
                        key:'16',
                        type:'10'
                    },
                    persediaan_produksi:{
                        title:'Persediaan Produksi :',
                        id:'',
                        key:'17',
                        type:'10'
                    },
                },
                arp:{
                    piutang_usaha:{
                        title:'Piutang Usaha :',
                        id:'',
                        key:'18',
                        type:'11'
                    },
                    hutang_usaha:{
                        title:'Hutang Usaha :',
                        id:'',
                        key:'19',
                        type:'11'
                    },
                    hutang_biaya:{
                        title:'Hutang biaya :',
                        id:'',
                        key:'34',
                        type:'11'
                    }
                },
                extra:{
                    ekuitas_saldo_awal:{
                        title:'Ekuitas Saldo Awal :',
                        id:'',
                        key:'20',
                        type:'12'
                    },
                    aset_tetap:{
                        title:'Aset Tetap :',
                        id:'',
                        key:'21',
                        type:'12'
                    },
                    pendapatan_lain:{
                        title:'Pendapatan lain-lain :',
                        id:'',
                        key:'28',
                        type:'12'
                    },
                    modal_saham:{
                        title:'Modal Saham :',
                        id:'',
                        key:'29',
                        type:'12'
                    },
                    beban:{
                        title:'Beban :',
                        id:'',
                        key:'31',
                        type:'16'
                    },
                    pendapatan:{
                        title:'Pendapatan :',
                        id:'',
                        key:'32',
                        type:'14'
                    },
                    selisih_persediaan:{
                        title:'Selisih Persediaan :',
                        id:'',
                        key:'33',
                        type:'17'
                    },
                    hutang_biaya:{
                        title:'Hutang Biaya :',
                        id:'',
                        key:'34',
                        type:'13'
                    }

                }
            }
        },
        methods: {
            getData(){
                $.ajax({
                    type: "get",
                    url: "<?=base_url('finance/accounts/account_default/data_account')?>",
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide(); 

                        vm.dataAccount.sales.sales_pendapatan.id = res.sales_pendapatan
                        vm.dataAccount.sales.sales_diskon.id = res.sales_diskon
                        vm.dataAccount.sales.sales_retur.id = res.sles_retur
                        vm.dataAccount.sales.sales_pengiriman.id = res.sales_pengiriman
                        vm.dataAccount.sales.sales_pembayaran.id = res.sales_pembayaran
                        vm.dataAccount.sales.sales_penjualan.id = res.sales_penjualan
                        vm.dataAccount.sales.sales_piutang.id = res.sales_piutang
                        vm.dataAccount.sales.sales_tax.id = res.sales_tax
                        vm.dataAccount.sales.sales_cash.id = res.sales_cash
                        vm.dataAccount.sales.sales_bank.id = res.sales_bank

                        vm.dataAccount.purchase.purchase_cogs.id = res.purchase_cogs
                        vm.dataAccount.purchase.purchase_pengiriman.id = res.purchase_pengiriman
                        vm.dataAccount.purchase.purchase_payment.id = res.purchase_payment
                        vm.dataAccount.purchase.purchase_hutang.id = res.purchase_hutang
                        vm.dataAccount.purchase.purchase_tax.id = res.purchase_tax
                        vm.dataAccount.purchase.purchase_operasional.id = res.purchase_operasional
                        vm.dataAccount.purchase.purchase_cash.id = res.purchase_cash
                        vm.dataAccount.purchase.purchase_bank.id = res.purchase_bank
                        vm.dataAccount.purchase.purchase_debitmemo.id = res.purchase_debitmemo
                        vm.dataAccount.purchase.purchase_assets.id = res.purchase_asset

                        vm.dataAccount.persediaan.persediaan_persediaan.id = res.persediaan_persediaan
                        vm.dataAccount.persediaan.persediaan_umum.id = res.persediaan_umum
                        vm.dataAccount.persediaan.persediaan_rusak.id = res.persediaan_rusak
                        vm.dataAccount.persediaan.persediaan_produksi.id = res.persediaan_produksi

                        vm.dataAccount.arp.piutang_usaha.id = res.piutang_usaha
                        vm.dataAccount.arp.hutang_usaha.id = res.hutang_usaha
                        vm.dataAccount.arp.hutang_biaya.id = res.hutang_biaya

                        vm.dataAccount.extra.ekuitas_saldo_awal.id = res.ekuitas_saldo_awal
                        vm.dataAccount.extra.aset_tetap.id = res.aset_tetap
                        vm.dataAccount.extra.modal_saham.id = res.modal_saham
                        vm.dataAccount.extra.pendapatan_lain.id = res.pendapatan_lain
                        vm.dataAccount.extra.beban.id = res.defult_beban 
                        vm.dataAccount.extra.pendapatan.id = res.dafault_pendapatan
                        vm.dataAccount.extra.selisih_persediaan.id = res.selisih_persediaan
                        
    
                        setTimeout(() => {
                            $('.select2select').trigger('change.select2');
                        }, 300);

                    },
                    error(err){
                        console.log(err);
                        Alert.error("Error","Terjadi Kesalahan");
                        loading.hide();
                    }
                });
            },
            onSubmit(){
                var data =[]
                for (const i in this.dataAccount) {
                    for (const j in this.dataAccount[i]) {
                        if (this.dataAccount[i][j].id == '') {
                            return Alert.warning("Oops",this.dataAccount[i][j].title+' Masih kosong')
                        }else{
                            data.push(this.dataAccount[i][j])
                        }
                    }
                     
                }
               
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/accounts/account_default/save_account')?>",
                    data: {data},
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (re) {
                        Alert.success("success","Data berhasil disimpan")
                        loading.hide();
                    },
                    error(err){
                        loading.hide()
                        console.log(err);
                        Alert.error("error","Gagal menyimpan data")
                    }
                });
            }
        },
        watch: {
            data(newVal){
                for (const i in newVal) {
                   console.log(mewVal[i]);
                }
            },
            
        },
        mounted() {
           this.getData()
        },
    })
</script>