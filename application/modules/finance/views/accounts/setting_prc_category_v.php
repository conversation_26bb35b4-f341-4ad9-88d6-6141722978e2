<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-gears"></i> Setting Purchase CAtegory </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
                    <div class="btn-group pull-right">
		                <button class="btn btn-primary btn-block" @click="onAdd" type="button">  Tambah <i class="fa fa-plus"></i></button>
		            </div>
                </div>
            </div>
	    	<div class="row">
                <div class="col-sm-12">
	    			<table class="table table-responsive table-report datatable" id="table-prcCategory" width="100%">
	    				<thead>
	    					<tr>
	    						<th width="10%">No</th>
	    						<th>Prc Category</th>
	    						<th>Akun</th>
	    						<th>#</th>
	    					</tr>
	    				</thead>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modal-form" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">{{form.type}} Data Akun</h4>
				</div>
                <div class="modal-body">
                    <form action="#">
                        <div class="form-group">
                           <label class="control-label">Prc Category</label>
                           <select name="prc_category" id="prc_category"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.prc_category.value" v-select="form.inputs.prc_category.value" style="width:100%" require title="- Pilih prc_category -">
                                <?php foreach ($category as $a): ?>
                                    <option value="<?=$a->purchase_report_category_id ?>"><?=htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>	
                            <small class="text-warning" id="err-category">{{form.inputs.prc_category.error}}</small>
                        </div>	
                        <div class="form-group">
                            <label class="control-label">Akun</label>
                            <select name="akun" id="akun"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.account.value" v-select="form.inputs.account.value" style="width:100%" require title="- Pilih Akun -">
                                <?php foreach ($account as $a): ?>
                                    <option value="<?=$a->account_id ?>"><?=htmlentities($a->output) ?></option>
                                <?php endforeach ?>
                            </select>	
                            <small class="text-warning" id="err-category">{{form.inputs.account.error}}</small>
                        </div>	
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" @click="onSubmit" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading">Simpan</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    
    $(function () {
        var t = $("#table-prcCategory").DataTable({					
            processing: true,
            serverSide: false,
            fixedHeader: true,
            ajax: {
                url: "<?=base_url() ?>finance/accounts/setting_prc_category/get_prc_category",
                type: "POST",
            },
            dom:"<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>",
            columns: [
                {
                    "data": null,
                    "orderable": true,								
                    "searchable": false,
                },							
                {"data": "prc_name"},
                {
                    "data": "account_name",
                    "render":function(data,type,row){
                        return "("+row.account_code+")"+"-"+row.account_name
                    }
                },
                {
                    "orderable": false,
                    "searchable": false,
                    "className": "text-center",
                    "render": function(data, type, row) {
                        var btn_edit = '<button class="btn btn-xs btn-warning btn-edit"><i class="fa fa-edit"></i></button>';
                        var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus"><i class="fa fa-trash"></i></button>';
                     	return btn_edit+' '+btn_hapus  
                    }
                }
            ],
            order: [[1, 'asc']],
        });//end datatable init

        $('#table-prcCategory').on('click', '.btn-hapus', function () {
            var self = this;
            var data = $("#table-prcCategory").DataTable().row($(this).closest('tr')).data();
            var id = data.id;
            Alert.confirm().then(function(result) {
                if (!result.value) {
                    return;
                }
                $.ajax({
                    type: "post",
                    url: "<?=base_url() ?>finance/accounts/setting_prc_category/delete",
                    data : {id:id},
                    dataType: "json",
                    beforeSend: function() {
                        loading_show();
                    },
                    success: function (response) {
                        loading_hide();
                        Alert.success('Success','Data berhasil dihapus');
                        if (response.status == 'success') {
                            $('#table-prcCategory').DataTable().ajax.reload(); //reload datatables
                        }
                    },
                    error: function(err) {
                        loading_hide();
                        Alert.error('Something Error!',err.responseJSON);
                        console.log(err)
                    }
                });
            });
        });

        $('#table-prcCategory').on('click', '.btn-edit',function () {
            $("#modal-form").modal("show")
            var data = $("#table-prcCategory").DataTable().row($(this).closest('tr')).data();
            // return console.log(data);
            vm.form.type = "Update"
            vm.form.inputs.id.value = data.id
            vm.form.inputs.prc_category.value = data.type_fkid
            vm.form.inputs.account.value = data.account_fkid   
            setTimeout(() => {
                $('.selectpicker').selectpicker('refresh')    
            }, 300);
        });
    });

   
    var vm = new Vue({
        el:"#app",
        data:{
            form:{
                type:"insert",
                inputs:{
                    id:{
						value:'',
						error:'',
						required:false,
						allowClear: true,
					},
                    prc_category:{
						value:'',
						error:'',
						required:true,
						allowClear: true,
					},
                    account:{
						value:'',
						error:'',
						required:true,
						allowClear: true,
					},
                }
            }
        },
        methods: {
            onSubmit(){
                if (!this.validate(this.form.inputs)) {
					return;
				}
                let data ={}
                for (var key in this.form.inputs) {
                    if (this.form.inputs.hasOwnProperty(key)) {
                        this.form.inputs[key].error = "";
                        data[key] = this.form.inputs[key].value;
                    }
                }
                data.method = this.form.type
                $.ajax({
					type: "post",
					url: "<?=base_url() ?>finance/accounts/setting_prc_category/save",
					data: data,
					dataType: "json",
					beforeSend: function() {
						loading_show();
					},
					success: function (response) {
						loading_hide();
						Alert.success('Success',"Data Berhasil di Simpan");
						$('#modal-form').modal('hide');
						$('#table-prcCategory').DataTable().ajax.reload()
					},
					error: function() {
						loading_hide();
						Alert.error('Something Error!');
					}
				});
            },
            onAdd(){
                this.form.type = "insert"
                $("#modal-form").modal("show")
                this.clearForm(this.form.inputs);
                setTimeout(() => {
                    $('.selectpicker').selectpicker('refresh')    
                }, 100);
                
            },
            
        },
    }) 
</script>