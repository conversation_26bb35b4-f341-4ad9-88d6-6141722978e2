<div id="app">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-gears"></i> Setting Bank Akun </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
                    <div class="btn-group pull-right">
                        <button class="btn btn-primary btn-block" @click="onAdd" type="button"> Tambah <i
                                class="fa fa-plus"></i></button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-responsive table-report datatable" id="table-pagu" width="100%">
                        <thead>
                            <tr>
                                <th width="1%">No</th>
                                <th>Outlet</th>
                                <th>#</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-form" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Data Akun Pagu</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="outlet">Outlet*</label><br>
                                <select class="form-control" id="input-outlet" v-model="form.outlet"
                                    v-select="form.outlet" multiple>
                                    <?php foreach ($form_select_outlet as $a): ?>
                                        <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-striped table-report" width="100%">
                                <thead>
                                    <tr>
                                        <td>No</td>
                                        <td>Account</td>
                                        <td>Pagu (%)</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="i,idx in dataAccount">
                                        <td>{{idx+1}}</td>
                                        <td>{{i.account_name}}</td>
                                        <td>
                                            <input type="text" class="form-control" v-model="dataAccount[idx].pagu"
                                                v-number="dataAccount[idx].pagu">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" @click="onSubmit" class="btn btn-primary"
                        data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading">Simpan</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    $(function () {
        var t = $("#table-pagu").DataTable({
            processing: true,
            serverSide: true,
            fixedHeader: true,
            ajax: {
                url: "<?= base_url() ?>finance/accounts/account_pagu/data_table",
                type: "POST",
            },
            dom: "<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>",
            columns: [
                {
                    "data": null,
                    "orderable": true,
                    "searchable": false,
                },
                { "data": "name" },
                {
                    "orderable": false,
                    "searchable": false,
                    "className": "text-center",
                    "render": function (data, type, row) {
                        var btn_edit = '<button class="btn btn-xs btn-warning btn-edit"><i class="fa fa-edit"></i></button>';
                        var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus"><i class="fa fa-trash"></i></button>';
                        return btn_edit 
                    }
                }
            ],
            order: [[1, 'asc']],
        });//end datatable init

        $('#table-pagu').on('click', '.btn-hapus', function () {
            var self = this;
            var data = $("#table-pagu").DataTable().row($(this).closest('tr')).data();
            var id = data.id;
            Alert.confirm().then(function (result) {
                if (!result.value) {
                    return;
                }
                $.ajax({
                    type: "post",
                    url: "<?= base_url() ?>finance/accounts/setting_payment/delete",
                    data: { id: id },
                    dataType: "json",
                    beforeSend: function () {
                        loading_show();
                    },
                    success: function (response) {
                        loading_hide();
                        Alert.success(response.msg);
                        if (response.status == 'success') {
                            $('#table-pagu').DataTable().ajax.reload(); //reload datatables
                        }
                    },
                    error: function (err) {
                        loading_hide();
                        Alert.error('Something Error!', err.responseJSON);
                        console.log(err)
                    }
                });
            });
        });
    });


    var vm = new Vue({
        el: "#app",
        data: {
            dataAccount: [],
            form: {
                outlet: []
            }
        },
        methods: {
            onSubmit() {
                if (this.form.outlet.length == 0) {
                    return Alert.info('oops', 'pilih outlet')
                }

                for (const i in this.dataAccount) {
                    let pagu  =this.dataAccount[i].pagu.toString()
                    this.dataAccount[i].pagu = pagu.replace(',','.')
                }    

                 let data= {
                        account: vm.dataAccount,
                        outlet: vm.form.outlet
                    }

                let dataSend = JSON.stringify(data)

                $.ajax({
                    type: "post",
                    url: "<?= base_url() ?>finance/accounts/account_pagu/save",
                    data: {data:dataSend},
                    dataType: "json",
                    beforeSend: function () {
                        loading_show();
                    },
                    success: function (response) {
                        loading_hide();
                        Alert.success('Success', "Data Berhasil di Simpan");
                        $('#modal-form').modal('hide');
                        $('#table-pagu').DataTable().ajax.reload()
                    },
                    error: function () {
                        loading_hide();
                        Alert.error('Something Error!');
                    }
                });
            },
            onEdit(outlet_id) {
                $.ajax({
                    type: "post",
                    url: "<?= base_url('finance/accounts/account_pagu/get_by_outlet') ?>",
                    data: {
                        outlet_id: outlet_id
                    },
                    beforeSend() {
                        loading.show()
                    },
                    success(res) {
                        if (res) {
                            loading.hide()
                            $("#modal-form").modal('show')
                            vm.form.outlet.push(outlet_id)
                            for (const i in res) {
                                res[i].pagu = res[i].pagu.replace('.',',')
                            }
                            vm.dataAccount = res
                            setTimeout(() => {
                                 $('#input-outlet').selectpicker('val', [outlet_id]);
                                $('#input-outlet').selectpicker('refresh').trigger('change');
                            }, 300);
                        } else {
                            vm.onAdd(outlet_id)
                        }
                    },
                    error(err) {
                        loading.hide()
                        Alert.error('error', 'gagal menampilkan data')
                    }
                });
            },
            onAdd(outlet_id = null) {
                $.ajax({
                    type: "get",
                    url: "<?= base_url('finance/accounts/account_pagu/get_account') ?>",
                    beforeSend() {
                        loading.show()
                        app.dataAccount = []
                    },
                    success(res) {
                        loading.hide()
                        $("#modal-form").modal('show')
                        if (outlet_id != null) {
                            vm.form.outlet.push(outlet_id)
                        }
                        for (const i in res) {
                            let tmp = {
                                account_id: res[i].account_id,
                                account_name: res[i].account_name,
                                pagu: 0
                            }
                            vm.dataAccount.push(tmp)
                        }
                        setTimeout(() => {
                           $('#input-outlet').selectpicker('val', [outlet_id]);
                            $('#input-outlet').selectpicker('refresh').trigger('change');
                        }, 300);
                    },
                    error(err) {
                        loading.hide()
                        Alert.error('error', 'gagal menpilkan data');
                    }
                });

            },

        },
    })

    $(document).ready(function () {
        $('#table-pagu').on('click', '.btn-edit', async function () {
            var data = $("#table-pagu").DataTable().row($(this).closest('tr')).data();
            vm.onEdit(data.outlet_id);
        });

        $('#input-outlet').selectpicker({
            title: "Pilih Outlet",
            style: 'btn btn-default btn-block',
            selectedTextFormat: "count >3",
            dropdownAlignRight: true,
            width: '100%',
            liveSearch: true,
            actionsBox: true,
            color: 'white'
        });
    });  
</script>