<style type="text/css">
	div.dropdown-menu.open { width: 100%; } 
	ul.dropdown-menu.inner>li>a { white-space: initial; }
	table.dataTable tbody td.select-checkbox:before, table.dataTable tbody th.select-checkbox:before {
		border: 1px solid #fff;
	}
	input[type=checkbox]
	{
		/* Double-sized Checkboxes */
		-ms-transform: scale(2); /* IE */
		-moz-transform: scale(2); /* FF */
		-webkit-transform: scale(2); /* Safari and Chrome */
		-o-transform: scale(2); /* Opera */
		padding: 10px;
	}
	#table-setting_processing{
		background-color: #27292a;
	}
</style>
<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
	    	<div class="row">
	    		<div class="col-sm-12">	   
		    		<div class="btn-group">
		                <button class="btn btn-primary btn-block" @click="onAdd" type="button"> <i class="fa fa-plus"></i> Tambah</button>
		            </div>
		            <div class="btn-group">
		            	<button type="button" class="btn btn-warning" style="background-color: #08C;border-color: transparent;border-right: 2px solid black">{{attr.totalSelect}}</button>
		            	<button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="background-color: #08C;border-color: transparent;">Data di Pilih
		            		<span class="caret"></span>
		            		<span class="sr-only">Toggle Dropdown</span>
		            	</button>
		            	<ul class="dropdown-menu">
		            		<li><a href="javascript:void(0)" @click="onEdit"><i class="fa fa-edit"></i> Edit</a></li>
		            		<li><a href="javascript:void(0)" @click="onDelete"><i class="fa fa-trash"></i> Hapus</a></li>
		            	</ul>
		            </div>
	    		</div>
	    	</div>
	    	<br>
	    	<div class="row">
	    		<div class="col-sm-12">
	    			<table class="table table-responsive table-report datatable" id="table-setting" width="100%">
	    				<thead>
	    					<tr>
	    						<th>No</th>
	    						<th>Nama Produk</th>
	    						<th>Sub Kategori</th>
	    						<th>Akun Pembelian</th>
	    						<th>Akun Penjualan</th>
	    						<th>Akun Persediaan</th>
	    						<th>Expenses</th>
	    					</tr>
	    				</thead>
	    			</table>			
	    		</div>			
	    	</div>	    	
	    </div>	
	</div><!-- /.container-fluid -->

	<!-- Modal -->
	<div class="modal fade" id="myModal" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-lg" style="width: 90%">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">{{attr.formTitle}}</h4>
				</div>
				<form method="post" id="myForm">
					<div class="modal-body">	
						<div class="row" v-if="form.method=='created'">				
							<div class="form-group col-sm-3">
								<select id="type" :class="['form-control form-control-sm', form.inputs.type.error ? 'is-invalid' : '']" name="type" v-model="form.inputs.type.value" style="width:100%" require :disabled="form.inputs.data.length != '0'">
									<option value="" selected disabled>-- Pilih Tipe Masukan --</option>	
									<option value="1">By Produk</option>
									<option value="2">By Sub Kategori</option>									
								</select>
								<small class="text-warning" id="err-category">{{form.inputs.type.error}}</small>
							</div>	
							<div class="col-sm-1">
								<button type="button" class="btn btn-primary" @click="addTable"><i class="fa fa-plus"></i> Tambah Data</button>
							</div>				
						</div>
						<div class="row">
							<div class="col-sm-12">
								<table class="table table-striped table-report" width="100%">
									<thead>
										<tr>
											<th>No</th>
											<th>{{attr.tableType}}</th>
											<th>Akun Pembelian</th>
											<th>Akun Penjualan</th>
											<th>Akun Persedian</th>
											<th>Expenses</th>
											<th>#</th>
										</tr>
									</thead>
									<tbody>
										<tr v-if="form.inputs.data.length == '0'">
											<td colspan="8" class="text-center">Tidak Ada Data</td>
										</tr>
										<tr v-for="(val,idx) in form.inputs.data">
											<td>{{idx+1}}</td>
											<td>												
												<div class="form-group">	
													<template v-if="form.method =='created'">
														<select v-if="form.inputs.type.value == '1'" :class="['form-control form-control-sm selectpicker']" data-size="5" data-live-search="true" v-model="form.inputs.data[idx].id" v-select="form.inputs.data[idx].id" :name="'product'+idx" :id="'product'+idx" style="width:100%" require title="- Pilih Produk -">
															<?php foreach ($data_product as $a): ?>
																<option value="<?=$a->product_id ?>"><?=htmlentities($a->name) ?></option>
															<?php endforeach ?>
														</select>	
														<select v-else :class="['form-control form-control-sm selectpicker']" data-size="5" data-live-search="true" v-model="form.inputs.data[idx].id" v-select="form.inputs.data[idx].id" :name="'subCategory'+idx" style="width:100%" require title="- Pilih Sub Kategori -">				
															<?php foreach ($data_sub_category as $a): ?>
																<option value="<?=$a->product_subcategory_id ?>"><?=htmlentities($a->name) ?></option>
															<?php endforeach ?>
														</select>
													</template>													
													<template v-else>
														<input type="text" name="text-product" id="text-product" class="form-control form-control-sm" v-model="form.inputs.data[idx].namaProduk" readonly="">
													</template>												
													
												</div>
											</td>									
											<td>												
												<div class="form-group">
													<select :id="'account-debt'+idx" :class="['form-control form-control-sm selectpicker']" data-size="5" data-live-search="true" :name="'account-debt'+idx" v-model="form.inputs.data[idx].debit" v-select="form.inputs.data[idx].debit" style="width:100%" :name="'debit'+idx" require title="- Pilih Akun Penjualn -">
														<?php foreach ($account as $a): ?>
															<option value="<?=$a->account_id?>"><?=$a->code.'-'.$a->name?></option>
														<?php endforeach ?>
													</select>
												</div>
											</td>									
											<td>												
												<div class="form-group">
													<select :id="'account-kredit'+idx" :class="['form-control form-control-sm selectpicker']" data-size="5" data-live-search="true" :name="'account-kredit'+idx" v-model="form.inputs.data[idx].kredit" v-select="form.inputs.data[idx].kredit" :name="'kredit'+idx" style="width:100%" require title="- Pilih Akun Pembelian -">	
														<?php foreach ($account as $a): ?>
															<option value="<?=$a->account_id?>"><?=$a->code.'-'.$a->name?></option>
														<?php endforeach ?>
													</select>
												</div>
											</td>									
											<td>												
												<div class="form-group">
													<select :id="'aset'+idx" :class="['form-control form-control-sm selectpicker']" data-size="5" data-live-search="true" :name="'aset'+idx" v-model="form.inputs.data[idx].persedian" v-select="form.inputs.data[idx].persedian" require title="- Pilih Akun Persedian -" :name="'persediaan'+idx" style="width: 30px">
														<?php foreach ($account as $a): ?>
															<option value="<?=$a->account_id?>"><?=$a->code.'-'.$a->name?></option>
														<?php endforeach ?>
													</select>
												</div>
											</td>	
											<td class="text-center">
												<input type="checkbox" class="expenses" value="1" v-model="form.inputs.data[idx].expenses">
											</td>								
											<td>
												<button type="button" class="btn btn-danger" @click="deleteTable(idx)" title="Hapus"><i class="fa fa-trash"></i></button>
											</td>									
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" @click="onSubmit" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading">Simpan</button>
					</div>
				</form>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->
</div>


<!-- DATATABLE LOAD START -->
<script type="text/javascript">
	var vm = new Vue({	
		el: "#app",
		data:{
			attr:{
				formTitle:'Form Akun Seting',
				tableType:'Nama Produk/Sub Category',
				totalSelect:'0',

			},
            accountDefault :'',
			form:{
				method:'created',
				inputs:{
					type:{
						value:'',
						error:'',
						required:true,
						allowClear: true,
					},
					data:[],
				}
			}
			
		},
		methods:{
			dataTable(){
				$(document).ready(function() {
					var t = $("#table-setting").DataTable({					
						processing: true,
						serverSide: false,
						scrollX:true,
						fixedHeader: true,
						ajax: {
				            url: "<?=base_url() ?>finance/accounts/setting_product/get_account_seting",
				            type: "POST",
				        },
				        dom:"<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                            "<'row'<'col-sm-12'tr>>" +
                            "<'row'<'col-sm-5'i><'col-sm-7'p>>",
				        select: {
				            style:    'multi',
				            selector: 'td:first-child'
				        },
						columns: [
							{
								"data": null,
								"class":"select-checkbox",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "namaProduk",
							},
							{"data": "sub_category"},
							{"data": "acc_debit"},
							{"data": "acc_kredit"},
							{"data": "acc_persedian"},
							{
								data: "expenses",
								class:"text-center",
								render:function(data,row){
									if (data == 'false') {
										return '<i class="fa fa-times text-danger fa-2x"></i>'
									}else{
										return '<i class="fa fa-check text-success fa-2x"></i>'
									}
								}
							}
						],
						order: [[2, 'asc']],
					});//end datatable init
				});	// end jequery
			},
			async onSubmit(){
				// validasi inputan
				if (!this.validate(this.form.inputs)) {
					return;
				}

				// return console.log(this.form.inputs.data.length)
				if (this.form.inputs.data.length == '0') {
					 return Alert.error('Tabel Harus di Isi')
				}else{
					for (var i =0; i<this.form.inputs.data.length;i++) {
						// var jmlOUtlet = this.form.inputs.data[i].outlet.length
						// return console.log(jmlOUtlet);
						if (this.form.inputs.data[i].id == '' || this.form.inputs.data[i].debit == '' || this.form.inputs.data[i].kredit == '') {
							var no = parseInt(i)+1;
							return Alert.error('Data Tidak Boleh Kosong','Periksa Kembali Data Nomor '+ no);
						}
					}

				}

				var data = {
					dataType : this.form.inputs.type.value,
					method : this.form.method,
					data: this.form.inputs.data
				};

				$.ajax({
					type: "post",
					url: "<?=base_url() ?>finance/accounts/setting_product/save",
					data: data,
					dataType: "json",
					beforeSend: function() {
						loading_show();
					},
					success: function (response) {
						loading_hide();
						Alert.success('Success',"Data Berhasil di Simpan");
						$('#myModal').modal('hide');
						$('#table-setting').DataTable().ajax.reload()
						vm.attr.totalSelect = '0'
					},
					error: function() {
						loading_hide();
						Alert.error('Something Error!');
					}
				});
			},
			onAdd(){
                $.ajax({
                    type: "get",
                    url: "<?=base_url('finance/accounts/account_default/data_account')?>",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide();
                        vm.accountDefault = res
                        vm.clearForm(vm.form.inputs);
                        // vm.form.inputs.outlet.value=[]
                        // vm.form.inputs.outlet.error=''
                        vm.form.inputs.data = []
                        vm.attr.formTitle='Form Akun Seting'
                        vm.attr.tableType='Nama Produk/Sub Category'
                        vm.attr.totalSelect='0'
                        vm.form.method = 'created'
                        $("#myModal").modal('show');
                        setTimeout(function() {
                            $('.selectpicker').selectpicker('refresh')
                        }, 300);
				      
                    },error(err){
                        loading.hide();
                        Alert.error('Error','gagal memuat data')
                    }
                });
								
			},
			addTable(){
				// validasi type data
				if (this.form.inputs.type.value == '') {
					return this.form.inputs.type.error = 'Pilih Tipe Data'
				}else{
					this.form.inputs.type.error = ''
				}
				this.form.inputs.data.push(
					{
                        id:'',
                        debit:this.accountDefault.purchase_cogs,
                        kredit:this.accountDefault.sales_pendapatan,
                        persedian:this.accountDefault.persediaan_persediaan,
						expenses:false,
                    }
				)

                var lastIdx = this.form.inputs.data.length-1;
                // console.log('#account-debt'+lastIdx);

                    
				setTimeout(function() {
                    $('#account-debt'+lastIdx).selectpicker('val',vm.accountDefault.purchase_cogs);
                    $('#account-kredit'+lastIdx).selectpicker('val',vm.accountDefault.sales_pendapatan);
                    $('#aset'+lastIdx).selectpicker('val',vm.accountDefault.persediaan_persediaan);
                    $('.selectpicker').selectpicker('render');
                    $('.selectpicker').selectpicker('refresh');
				}, 500);
			},
			deleteTable(id){
				this.form.inputs.data.splice(id,1);
			},
			onEdit(){
				this.form.method = 'updated'
				$("#myModal").modal('show');
				$(function () {
					var dataEdit = $("#table-setting").DataTable().rows('.selected').data().toArray();
					vm.form.inputs.data=''
					for (const i in dataEdit) {
						dataEdit[i].expenses = dataEdit[i].expenses=="false"?false:true
					}
					vm.form.inputs.data = dataEdit
					vm.form.inputs.type.value = '1'
					// this.form.inputs.outlet.required = false

					setTimeout(() => {
						for (var a in vm.form.inputs.data) {
							$('select[name=product'+a+']').val(vm.form.inputs.data[a].product_detail_id);
							$('select[name=product'+a+']').attr('disabled',true);
							// vm.form.inputs.data[a].outlet = [vm.form.inputs.data[a].id_outlet]
							// $('select[name=outlet'+a+']').selectpicker("val",[vm.form.inputs.data[a].id_outlet]);
							// $('select[name=outlet'+a+']').attr('disabled',true);
							$('select[name=account-debt'+a+']').val(vm.form.inputs.data[a].debit);
							$('select[name=account-kredit'+a+']').val(vm.form.inputs.data[a].kredit);
							$('select[name=aset'+a+']').val(vm.form.inputs.data[a].persedian);
							console.log(a);
						}
					}, 300);
					
					setTimeout(function() {
						console.log("render");
						$('.selectpicker').selectpicker('refresh');
						$('.selectpicker').selectpicker('render');
					}, 400);
				});
				
			},
			onDelete(){
				var dataDelete = $("#table-setting").DataTable().rows('.selected').data().toArray();
				var data = [];
				for (var key in dataDelete) {
					if (dataDelete.hasOwnProperty(key,'seting_id')) {
						data.push(dataDelete[key].seting_id)
					}
				} 

				Alert.confirm().then(function(result) {
					if (!result.value) {
						return;
					}				
					$.ajax({
						url: "<?=base_url() ?>finance/accounts/setting_product/delete",
						type: 'POST',
						data: {data:data},
                        beforeSend:function(){
                            loading.show()
                        },
						success:function(){
							Alert.success('Success','Data Berhasil di Hapus');
							$('#table-setting').DataTable().ajax.reload()
							vm.attr.totalSelect='0'
                            loading.hide()
						},
						error:function(){
                            loading.hide()
							Alert.error('Error','Data Gagal di Hapus');
						}
					});				
				})
			}
		},
		watch:{
			'form.inputs.type.value'(newVal){
				(newVal == "1") ? this.attr.tableType = "Nama Produk" : this.attr.tableType = "Nama Sub Kategori" 
			},
			'filter.outlet.value'(newVal){
				$('#table-setting').DataTable().destroy(); //hapus data table 
        		$("#table-setting > tbody > tr").remove(); //hapus content datatable   
				this.dataTable()

			}
		},
		mounted(){
			setTimeout(function() {
				vm.dataTable();
			}, 100);
		}	
	})
</script>

<script type="text/javascript">

$(document).ready(function() {
	$('#myModal').on('hidden.bs.modal', function () {
		// $("#table-setting").DataTable().ajax.reload();
		location.reload(false)
		// vm.attr.totalSelect = $("#table-setting").DataTable().rows('.selected').data().count()
		// console.log($("#table-setting").DataTable().rows('.selected').data().count());
	})

	$('#table-setting').on('click','.select-checkbox', function () {
		setTimeout(function() {
			vm.attr.totalSelect = $("#table-setting").DataTable().rows('.selected').data().count()
		}, 100);		 
	})

	$('#table-setting').on('click', '.btn-edit', function () {
		$("#myModal").modal('show');
		var data = $("#table-setting").DataTable().row($(this).closest('tr')).data();
		vm.inputs.method.value = 'updated'
		vm.inputs.kode.value = data.code
		vm.inputs.name.value = data.account_name
		vm.inputs.kategori.value = data.code.substring(0, 1);
		vm.inputs.saldo.value = data.saldo
		vm.inputs.keterangan.value = data.keterangan
		vm.inputs.active.value = data.active_status
		$('select[name=kategori]').val(data.code.substring(0, 1));
		$('select[name=kategori]').attr('disabled',true);
		$('.selectpicker').selectpicker('refresh')
		$('#code').attr('disabled','true');


	});
	//ACTION HAPUS
	$('#table-setting').on('click', '.btn-hapus', function () {
		var self = this;
		var data = $("#table-setting").DataTable().row($(this).closest('tr')).data();
		var id = data.id;
		Alert.confirm().then(function(result) {
			if (!result.value) {
				return;
			}
			$.ajax({
				type: "get",
				url: "<?=current_url() ?>/delete/"+id,
				dataType: "json",
				beforeSend: function() {
					loading_show();
				},
				success: function (response) {
					loading_hide();
					Alert.success(response.message);

					if (response.status == 'success') {
						$('#table-setting').DataTable().ajax.reload(); //reload datatables
					}
				},
				error: function() {
					loading_hide();
					Alert.error('Something Error!');
				}
			});
		});
		//ambil semua data di row
	});

})
</script>