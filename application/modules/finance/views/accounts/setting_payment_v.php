<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-gears"></i> Setting Bank Akun </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
                    <div class="btn-group pull-right">
		                <button class="btn btn-primary btn-block" @click="onAdd" type="button">  Tambah <i class="fa fa-plus"></i></button>
		            </div>
                </div>
            </div>
	    	<div class="row">
                <div class="col-sm-12">
	    			<table class="table table-responsive table-report datatable" id="table-payment" width="100%">
	    				<thead>
	    					<tr>
	    						<th width="10%">No</th>
	    						<th>Gratuity</th>
	    						<th>Akun</th>
	    						<th>#</th>
	    					</tr>
	    				</thead>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modal-form" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">{{form.type}} Data Akun</h4>
				</div>
                <div class="modal-body">
                    <form action="#">
                        <div class="form-group">
                           <label class="control-label">Metode Pembayaran</label>
                           <select name="bank" id="bank"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.bank.value" v-select="form.inputs.bank.value" style="width:100%" require title="- Pilih Bank -" id="bank" name="bank">
                                <?php foreach ($payment as $a): ?>
                                    <option value="<?=$a->bank_id ?>"><?=htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>	
                            <small class="text-warning" id="err-category">{{form.inputs.bank.error}}</small>
                        </div>	
                        <div class="form-group">
                            <label class="control-label">Akun</label>
                            <select name="akun" id="akun"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.account.value" v-select="form.inputs.account.value" style="width:100%" require title="- Pilih Akun -">
                                <?php foreach ($account as $a): ?>
                                    <option value="<?=$a->account_id ?>"><?=htmlentities($a->output) ?></option>
                                <?php endforeach ?>
                            </select>	
                            <small class="text-warning" id="err-category">{{form.inputs.account.error}}</small>
                        </div>	
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" @click="onSubmit" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading">Simpan</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    
    $(function () {
        var t = $("#table-payment").DataTable({					
            processing: true,
            serverSide: true,
            fixedHeader: true,
            ajax: {
                url: "<?=base_url() ?>finance/accounts/setting_payment/get_payment",
                type: "POST",
            },
            dom:"<'row'<'col-sm-6'l><'col-sm-6'f>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>",
            columns: [
                {
                    "data": null,
                    "orderable": true,								
                    "searchable": false,
                },							
                {"data": "bank"},
                {
                    "data": "account_name",
                    "render":function(data,type,row){
                        return "("+row.account_code+")"+"-"+row.account_name
                    }
                },
                {
                    "orderable": false,
                    "searchable": false,
                    "className": "text-center",
                    "render": function(data, type, row) {
                        var btn_edit = '<button class="btn btn-xs btn-warning btn-edit"><i class="fa fa-edit"></i></button>';
                        var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus"><i class="fa fa-trash"></i></button>';
                     	return btn_edit+' '+btn_hapus  
                    }
                }
            ],
            order: [[1, 'asc']],
        });//end datatable init

        $('#table-payment').on('click', '.btn-hapus', function () {
            var self = this;
            var data = $("#table-payment").DataTable().row($(this).closest('tr')).data();
            var id = data.id;
            Alert.confirm().then(function(result) {
                if (!result.value) {
                    return;
                }
                $.ajax({
                    type: "post",
                    url: "<?=base_url() ?>finance/accounts/setting_payment/delete",
                    data : {id:id},
                    dataType: "json",
                    beforeSend: function() {
                        loading_show();
                    },
                    success: function (response) {
                        loading_hide();
                        Alert.success(response.msg);
                        if (response.status == 'success') {
                            $('#table-payment').DataTable().ajax.reload(); //reload datatables
                        }
                    },
                    error: function(err) {
                        loading_hide();
                        Alert.error('Something Error!',err.responseJSON);
                        console.log(err)
                    }
                });
            });
        });
    });

   
    var vm = new Vue({
        el:"#app",
        data:{
            form:{
                type:"insert",
                inputs:{
                    id:{
						value:'',
						error:'',
						required:false,
						allowClear: true,
					},
                    bank:{
						value:'',
						error:'',
						required:true,
						allowClear: true,
					},
                    account:{
						value:'',
						error:'',
						required:true,
						allowClear: true,
					},
                }
            }
        },
        methods: {
            onSubmit(){
                if (!this.validate(this.form.inputs)) {
					return;
				}
                let data ={}
                for (var key in this.form.inputs) {
                    if (this.form.inputs.hasOwnProperty(key)) {
                        this.form.inputs[key].error = "";
                        data[key] = this.form.inputs[key].value;
                    }
                }
                data.method = this.form.type
                $.ajax({
					type: "post",
					url: "<?=base_url() ?>finance/accounts/setting_payment/save",
					data: data,
					dataType: "json",
					beforeSend: function() {
						loading_show();
					},
					success: function (response) {
						loading_hide();
						Alert.success('Success',"Data Berhasil di Simpan");
						$('#modal-form').modal('hide');
						$('#table-payment').DataTable().ajax.reload()
					},
					error: function() {
						loading_hide();
						Alert.error('Something Error!');
					}
				});
            },
            onDelete(){
                
            },
            onAdd(){
                this.form.type = "insert"
                $("#modal-form").modal("show")
                this.clearForm(this.form.inputs);
                setTimeout(() => {
                    $('.selectpicker').selectpicker('refresh')    
                }, 100);
                
            },
            
        },
    }) 

    $(document).ready(function () {
        $('#table-payment').on('click', '.btn-edit', async function () {
            $("#modal-form").modal("show")
            var data = $("#table-payment").DataTable().row($(this).closest('tr')).data();
            vm.form.type = "Update"
            setTimeout(() => {
                var form = vm.form.inputs
                vm.form.inputs.id.value = data.id
                vm.form.inputs.bank.value = data.type_fkid
                vm.form.inputs.account.value = data.id_account   
            }, 100);
             setTimeout(() => {
                    $('.selectpicker').selectpicker('refresh')    
                }, 100);
        });
    });  
</script>