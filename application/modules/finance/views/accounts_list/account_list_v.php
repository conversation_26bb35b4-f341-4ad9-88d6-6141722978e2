<div id="app">
	<div class="container-fluid">
        <div class="content-uniq">
			<div class="row">
				<div class="col-sm-6 offset-sm-6">
					<div class="btn-group">
						<a href="<?=base_url('finance/accounts/closing_book')?>" class="btn btn-info btn-block btn-sm" style="background-color: #5bc0de" type="button"><i class="fa fa-book"></i> Tutup Buku</a>                  
					</div>
				</div>
			</div>
	    	<div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-book"></i> Account List  </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
	            	<div class="pull-right">
		                <div class="btn-group">
		                    <select class="form-control btn btn-info outletSelect" v-model="filter.outlet">
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-primary btn-block btn-sm" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
            </div>
            <hr style="margin:10px">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-responsive table-report datatable" id="mytable" width="100%">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>#</th>
                                <th>Kode Akun</th>
                                <th>Nama Akun</th>
                                <th>Kategori</th>
                                <th>Saldo Awal</th>
                                <th>Keterangan</th>
                                <th>Active</th>
                                <th>Action </th>
                            </tr>
                        </thead>
						<tbody>
							<tr>
								<td colspan="9" class="text-center">Pilih outlet untuk menampilkan data</td>
							</tr>
						</tbody>
                    </table>			
                </div>			
            </div>
        </div>
	</div><!-- /.container-fluid -->

	<!-- Modal -->
	<div class="modal fade" id="myModal">
		<div class="modal-dialog">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">{{attr.formTitle}}</h4>
				</div>
				<form method="post" action="" class="form-horizontal" id="myForm">
					<div class="modal-body">					
						<div class="form-group">
							<label class="col-xs-3 control-label">Nama Akun</label>
							<div class="col-xs-9">
								<input type="text" class="form-control" v-model="inputs.name.value" id="name" name="name" placeholder="Nama Akun">
								<small class="text-warning" id="err-name">{{inputs.name.error}}</small>
							</div>
						</div>						
						<div class="form-group">
							<label class="col-xs-3 control-label">Kategori</label>
							<div class="col-xs-9">
								<select id="kategori" :class="['form-control form-control-sm selectpicker', inputs.category_id.error ? 'is-invalid' : '']" data-live-search="true" name="kategori" v-model="inputs.category_id.value" v-select="inputs.category_id.value" style="width:100%" require>
									<option value="" selected disabled>-- Pilih Kategori --</option>	
									<?php foreach ($category as $a): ?>
										<option value="<?=$a['account_category_id']?>" data-code="<?=$a['code'] ?>"><?=htmlentities($a['name']) ?></option>
									<?php endforeach ?>
								</select>
								<small class="text-warning" id="err-category">{{inputs.category_id.error}}</small>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-3 control-label">Kode Akun</label>
							<div class="col-xs-1" style="padding-right: 0px;">
								<input type="text" class="form-control" name="code-category" id="code-category" v-model="inputs.category_id.kode" style="padding-left: 0px;padding-right: 0px;text-align: center;">
							</div>
							<div class="col-xs-8" style="padding-left: 6px;">
								<input type="text" class="form-control" v-model="inputs.kode.value" v-number="inputs.kode.value" id="code" name="code" placeholder="Kode Akun" autocomplete="off">
								<p style="margin-left: -40px">Hasil Kode : {{inputs.category_id.kode}}-{{inputs.kode.value}} </p>
								<small class="text-warning" id="err-code">{{inputs.kode.error}}</small>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-3 control-label">Saldo</label>
							<div class="col-xs-9">
								<input type="text" class="form-control" name="saldo" readonly="" v-model="inputs.saldo.value">
								<small class="text-warning" id="err-active_status">{{inputs.saldo.error}}</small>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-3 control-label">Keterangan</label>
							<div class="col-xs-9">
								<textarea type="text" class="form-control" name="keterangan" v-model="inputs.keterangan.value"> </textarea>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-3 control-label">Active</label>
							<div class="col-xs-9">
								<select class="form-control" v-model="inputs.active.value" id="active_status" name="active_status">
									<option value="1">Yes</option>
									<option value="0">No</option>
								</select>
								<small class="text-warning" id="err-active_status">{{inputs.active.error}}</small>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" @click="onSubmit" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading" id="btn-simpan">Simpan</button>
					</div>
				</form>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->

    <!-- Modal saldo -->
	<div class="modal fade" id="modal-saldo">
		<div class="modal-dialog modal-lg">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">Saldo Awal</h4>
				</div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label for="outletSaldo">Outlet* </label>
                                <select id="outletSaldo"  v-model="inputSaldo.outlet.value">
                                    <?php foreach ($form_select_outlet as $a): ?>                   
                                        <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>                                   
                            </div>                
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-striped table-responsive table-report">
                                <thead>
                                    <tr>
                                        <th width="60%">Account</th>
                                        <th width="20%">Debit</th>
                                        <th width="20%">Kredit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="i,idx in dataAccount">
                                        <td>{{i.code}}-{{i.name}}</td>
                                        <td>
                                            <input type="text" class="form-control form-dark input-sm" style="text-align: right;" name="saldoDebit" :disabled="i.type=='K'" v-model="dataAccount[idx].debit" v-money="dataAccount[idx].debit">
                                        </td>
            
                                        <td>
                                            <input type="text" class="form-control form-dark input-sm" style="text-align: right;" name="saldoKredit" v-money="dataAccount[idx].kredit" :disabled="i.type=='D'" v-model="dataAccount[idx].kredit">
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="2" class="text-right">{{money(total.debit)}}</th>
                                        <th class="text-right">{{money(total.kredit)}}</th>
                                    </tr>
                                </tfoot>

                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" @click="validateSaldo" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin '></i> Loading" id="btn-simpan">Simpan</button>
                </div>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->
</div>


<!-- DATATABLE LOAD START -->
<script type="text/javascript">

	var vm = new Vue({	
		el: "#app",
		data:{
			attr:{
				formTitle:'Form Input Akun',
				methods:'created',
			},
            filter:{
                outlet:'',
            },
			inputSaldo:{
				outlet:{
					value:"",
					error:"",
					allowClear: true,
                    required: true,
				},
			},
			inputs:{
				method:{
					value:"created",
					error:"",
					allowClear: false,
					required: true,
				},
				outlet:{
					value:"",
					error:"",
					allowClear: true,
                    required: false,
				},
				account_id:{
					value:"",
					allowClear: true,
				},
                category_id:{
                    value:"",
                    error:"",
                    kode:"",
                    allowClear:true,
                    required: true,
                },
				name:{
					value:"",
					error:"",
					allowClear: true,
					required: true,
				},
				kode:{
					value:"",
					error:"",
					allowClear: true,
					required: true,
				},
				saldo:{
					value:"0",
					error:"",
					allowClear: true,
					required: false,
				},
				keterangan:{
					value:"",
					error:"",
					allowClear: true,
				},
				active:{
					value:"1",
					error:"",
					allowClear: true,
					required: false,
				}
			},
            dataAccount:[]
	
		},
		methods:{
			dataTable(){
                var btnSaldo = ""
				$(document).ready(function() {
					var t = $("#mytable").DataTable({					
						processing: false,
						serverSide: false,
						scrollX:true,
						fixedHeader: true,
                        pageLength: 50,
                        destroy : true,
						ajax: {
				            url: "<?=current_url() ?>/datatables",
				            type: "POST",
                            data : {outlet:vm.filter.outlet}
				        },
				        dom:"<'row'<'col-sm-6'l><'col-sm-6'<'toolbar'>f>>" +
                            "<'row'<'col-sm-12'tr>>" +
                            "<'row'<'col-sm-5'i><'col-sm-7'p>>",
						columns: [
							{
								"data": null,
								"class":"text-center",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "lock",
								"class":"text-center",
								"orderable": false,								
								"searchable": false,
								"render": function(data, type, row) {
									return (data=='1') ? '<i class="fa fa-lock"></i>':''
								}
							},
							{"data": "code"},
							{"data": "account_name"},
							{"data": "category_name"},
							{
                                "data": "saldo",
                                render:function(data,type,row){
                                    return currency(data)
                                }
                            },							
							{
								"data": "keterangan",
								"render": function(data, type, row) {
									return parseString(data,30);
								}
							},
							{
								"data": "active_status",
								"render": function(data, type, row) {
									return (data==1)?'On':'Off'
								}
							},
							{
								"orderable": false,
								"searchable": false,
								"className": "text-center",
								"render": function(data, type, row) {
									var btn_edit = '<button class="btn btn-xs btn-warning btn-edit"><i class="fa fa-edit"></i></button>';
									var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus"><i class="fa fa-trash"></i></button>';
									if (row.lock =='1') {
										return ''
									}else{										
										return btn_edit+' '+btn_hapus
									}
									
								}
							}
						],
						order: [[2, 'asc']],
                        initComplete:function( settings, res){
                            if (!res.set_saldo) {
                                btnSaldo =  '<button type="button" class="btn btn-warning" onClick="onSaldo()" id="saldoBtn" style="margin-left:5px; padding:4px 10px;"><span class="glyphicon glyphicon-usd"></span> Saldo Awal</button>'
                            }else{
                                btnSaldo =  '<button type="button"  class="btn btn-warning" onClick="onSaldo()" id="saldoBtn" style="margin-left:5px; padding:4px 10px;"><span class="glyphicon glyphicon-usd"></span> Saldo Awal</button>'
                            }
                            $("div.toolbar").html(''+
                                '<div class="btn-group pull-right">'+
                                    '<button type="button" class="btn btn-primary uniq-add" onClick="onAdd()" id="tombolAdd" style="margin-left:5px; padding:4px 10px;"><span class="glyphicon glyphicon-plus"></span> Add New</button>'+btnSaldo+
                                '</div>');
                            }
					});//end datatable init

                    // console.log(t);

					
				});	// end jequery
			},
			onSubmit(){
				if (!this.validate(this.inputs)) {
					return;
				}
				var data = {};
				for (var key in this.inputs) {
					if (this.inputs.hasOwnProperty(key)) {
						data[key] = this.inputs[key].value;
					}
				}
                data.fullCode = this.inputs.category_id.kode+"-"+this.inputs.kode.value
                data.codeCategory = this.inputs.category_id.kode

				if (this.inputs.method.value != "created") {
					Alert.confirm("Anda Yakin Akan menyimpan perubahan?","Perubahan akan terjadi di semua outlet").then(function(result) {
						if (!result.value) {
							return;
						}

						$.ajax({
							type: "post",
							url: "<?=current_url() ?>/save",
							data: data,
							dataType: "json",
							beforeSend: function() {
								loading_show();
							},
							success: function (response) {
								loading_hide();
								Alert.success('Success',"Data Berhasil di Simpan");
								$('#myModal').modal('hide');
								$('#mytable').DataTable().ajax.reload()
							},
							error: function(err) {
								loading_hide();
								Alert.error('Something Error!',err.responseJSON.message);
							}
						});
					});
				}else{
					$.ajax({
						type: "post",
						url: "<?=current_url() ?>/save",
						data: data,
						dataType: "json",
						beforeSend: function() {
							loading_show();
						},
						success: function (response) {
							loading_hide();
							Alert.success('Success',"Data Berhasil di Simpan");
							$('#myModal').modal('hide');
							$('#mytable').DataTable().ajax.reload()
						},
						error: function(err) {
							loading_hide();
							Alert.error('Something Error!',err.responseJSON.message);
						}
					});
				}
				
				
			},
			onAdd(){
				this.clearForm(this.inputs);
				$("#myModal").modal('show');
				this.inputs.method.value = 'created'				
				this.inputs.saldo.value = '0'
				this.inputs.active.value = '1'
				$('select[name=kategori]').attr('disabled',false);
				$('#code').attr('disabled',false);
                // $('#code-category').attr('disabled','false');
				$('select[name=kategori]').val('');				
				$('.selectpicker').selectpicker('refresh')
				
			},
            setSaldo(){
                $.ajax({
                    type: "get",
                    url: "<?=base_url('finance/accounts/accounts_list/get_account') ?>",
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success: function (response) {
                        $("#modal-saldo").modal("show");
                        vm.dataAccount = []
                        loading.hide()
                        for (const i in response) {
                            var form ={
                                code : response[i].code,
                                name : response[i].name,
                                debit :0,
                                kredit :0,
                                type : response[i].type,
                                id : response[i].account_id
                            }
                            vm.dataAccount.push(form)
                        }
                        // vm.dataAccount = response
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error('error','gagal menampilkan data')
                    }
                });
            },
            validateSaldo(){
                if (this.inputSaldo.outlet.value == "") {
                    return Alert.warning("Warning","Pilih Outlet")
                }

                for (const i in this.dataAccount) {
                    //  clear format
                    this.dataAccount[i].debit = clearFormat( this.dataAccount[i].debit)
                    this.dataAccount[i].kredit = clearFormat( this.dataAccount[i].kredit)
                }

                // return console.log(this.dataAccount);
                Alert.confirm("Anda Yakin Akan menyimpan saldo awal?","saldo awal hanya diinput sekali dan tidak bisa dihapus kembali").then(function(result) {
                    if (!result.value) {
                        return;
                    }else if(result.value){
                        if (vm.total.debit != vm.total.kredit) {
                            var selisih = vm.total.debit - vm.total.kredit
                            Alert.confirm("Warning","Total debit dan kredit harus sama. Jika Anda tetap ingin menerbitkan saldo, selisih "+currency(selisih)+" akan di masukkan ke dalam akun Ekuitas Saldo Awal.").then(function(result) {
                                if (!result.value) {
                                    return;
                                }else if(result.value){
                                    vm.onSubmitSaldo();
                                }
                            })
                        }else{
                            vm.onSubmitSaldo();
                        }
                        
                    }
                });

            },
            onSubmitSaldo(){
                $.ajax({
                type: "post",
                url: "<?=base_url('finance/accounts/accounts_list/set_saldo')?>",
                data: {
                    data : vm.dataAccount,
                    outlet : vm.inputSaldo.outlet.value
                },
                dataType: "json",
                beforeSend(){
                    loading.show()
                },
                success: function (response) {
                    loading.hide();
                    Alert.success('Success','Data berhasil disimpan');
                    $("#mytable").DataTable().ajax.reload();
                    $("#modal-saldo").modal('hide');
                },
                error(err){
                    loading.hide();
                    console.log(err);
                    Alert.error('error','data gagal disimpan')
                }
                });
            },
            onApply(){
                if (this.filter.outlet == "") {
                    return Alert.warning("Warning","Pilih Outlet");
                }
                this.dataTable()
            },
            money(param){
                return currency(param)
            }
		},
        computed: {
            'total'(){
                let totalDebit = 0
                let totalKredit = 0
                for (const i in this.dataAccount) {
                    totalDebit += clearFormat( this.dataAccount[i].debit)
                    totalKredit += clearFormat( this.dataAccount[i].kredit)
                }

                let result ={
                    debit:totalDebit,
                    kredit:totalKredit
                }
                return result;
            }
        },
		mounted(){
            $('#outletSaldo').selectpicker({
                title:"Outlet",
                style: 'form-control btn btn-sm btn-info',
                selectedTextFormat:"count >3",
                dropdownAlignRight:true,
                width:"auto",
                liveSearch:true,
                actionsBox:true
            });
        }
	})

	function onAdd() {
		vm.onAdd()
	}
    function onSaldo(){
        vm.setSaldo()
    }
</script>

<script type="text/javascript">

$(document).ready(function() {
	//ACTION EDIT
	$('#mytable').on('click', '.btn-edit', function () {
		$("#myModal").modal('show');
		var data = $("#mytable").DataTable().row($(this).closest('tr')).data();
		vm.inputs.method.value = 'updated'
		vm.inputs.kode.value = data.code.split('-')[1]
		vm.inputs.name.value = data.account_name
		vm.inputs.category_id.value = data.code.substring(0, 1);
		vm.inputs.saldo.value = data.saldo
		vm.inputs.keterangan.value = data.keterangan
		vm.inputs.active.value = data.active_status
		vm.inputs.account_id.value = data.id
        vm.inputs.category_id.kode = data.code.substr(0,1)
		$('select[name=kategori]').val(data.account_category_fkid);
		// $('select[name=kategori]').attr('disabled',true);
		$('.selectpicker').selectpicker('refresh')
		$('#code').attr('disabled','true');
		// $('#code-category').attr('disabled','false');
	});
	//ACTION HAPUS
	$('#mytable').on('click', '.btn-hapus', function () {
		var self = this;
		var data = $("#mytable").DataTable().row($(this).closest('tr')).data();
		var id = data.id;
		Alert.confirm().then(function(result) {
			if (!result.value) {
				return;
			}
			$.ajax({
				type: "get",
				url: "<?=current_url() ?>/delete/"+id,
				dataType: "json",
				beforeSend: function() {
					loading_show();
				},
				success: function (response) {
					loading_hide();
					Alert.success(response.message);

					if (response.status == 'success') {
						$('#mytable').DataTable().ajax.reload(); //reload datatables
					}
				},
				error: function(err) {
					loading_hide();
					Alert.error('Something Error!',err.responseJSON);
					console.log(err)
				}
			});
		});
		//ambil semua data di row
	});

    $('select[name=kategori]').on("changed.bs.select", function() {
        codeCategory = $('option:selected', this).attr("data-code");
        vm.inputs.category_id.kode = codeCategory
    });

})
</script>