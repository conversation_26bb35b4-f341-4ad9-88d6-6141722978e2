<style lang="">
    .table-hover tbody tr:hover td, .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }

    .sub1{
        padding-left : 20px
    }
    .sub2{
        padding-left : 40px
    }
</style>

<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-book"></i> Neraca  </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
	            	<div class="pull-right">
	            		<div class="btn-group">
		                    <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de" id="btn-date">
		                        <span> 
		                            <i class="glyphicon glyphicon-calendar"></i> Date
		                        </span>
		                        <i class="caret"></i>
		                    </button>
		                </div>
		                <div class="btn-group">
		                    <select class="form-control btn btn-info outletSelect" multiple v-model="param.outlet">
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
            </div>
	    	<div class="row">
                <div class="col-sm-12 table-responsive">
	    			<table class="table table-report table-hover" id="labaRugiTable" width="100%">
                        <tbody>
                            <tr >
                                <th colspan="2">Aset</th>                               
                            </tr> 
                            <template v-for="i,idx in dataTable.data" v-if="i.code_category <= 7 && (i.debit !=0 || i.kredit != 0 || i.total != 0) ">
                                <tr>
                                    <td><b class="sub1">{{i.category}}</b></td>
                                    <td class="text-right"><b>{{currency(i.total)}}</b></td>
                                </tr>
                                <tr v-for="j,index in i.detail" style="color:#b69f9f" v-if="j.debit !=0 || j.kredit != 0 || j.total != 0">
                                    <td ><span class="sub2">{{j.account_code}}-{{j.account_name}}</span> </td>
                                    <td  class="text-right">{{currency(j.total)}}</td>
                                </tr>
                            </template>
                            <tr>
                                <td><b>Total Aset</b></td>
                                <td class="text-right"><b>{{currency(dataTable.total.aset)}}</b></td>
                            </tr>
                            <tr>
                                <th><b>Leabilitas dan Modal</b></th>
                                <th><b></b></th>
                            </tr>
                            <template v-for="i,idx in dataTable.data" v-if="i.code_category >= 8 && i.code_category <= 11 && (i.debit !=0 || i.kredit != 0 || i.total != 0)">
                                <tr>
                                    <td><b class="sub1">{{i.category}}</b></td>
                                    <td class="text-right"><b>{{currency(i.total)}}</b></td>
                                </tr>
                                <tr v-for="j,index in i.detail" style="color:#b69f9f" v-if="j.debit !=0 || j.kredit != 0 || j.total != 0">
                                    <td ><span class="sub2">{{j.account_code}}-{{j.account_name}}</span> </td>
                                    <td class="text-right">{{currency(j.total)}}</td>
                                </tr>
                            </template>
                            <tr>
                                <td ><b>Total liabilitas</b></td>
                                <td class="text-right"><b>{{currency(dataTable.total.leability)}}</b></td>
                            </tr>
                            <tr>
                                <th><b>Equity</b></th>
                                <th><b></b></th>
                            </tr>
                            <template v-for="i,idx in dataTable.data" v-if="i.code_category == 12 && (i.debit !=0 || i.kredit != 0 || i.total != 0)">
                                <tr>
                                    <td><b class="sub1">{{i.category}}</b></td>
                                    <td class="text-right">{{currency(i.total)}}</td>
                                </tr>
                                <tr v-for="j,index in i.detail" style="color:#b69f9f" v-if="j.debit !=0 || j.kredit != 0 || j.total != 0">
                                    <td ><span class="sub2">{{j.account_code}}-{{j.account_name}}</span> </td>
                                    <td class="text-right">{{currency(j.total)}}</td>
                                </tr>
                            </template>
                            <tr>
                                <td><span class="sub2">Keuntungan Periode Lalu</span></td>
                                <td class="text-right">{{currency(dataTable.total.lastPeriod)}}</td>
                            </tr>
                            <tr>
                                <td><span class="sub2">Keuntungan Periode sekarang</span></td>
                                <td class="text-right">{{currency(dataTable.total.curentPeriod)}}</td>
                            </tr>
                            <tr>
                                <td >Total Equity</td>
                                <td class="text-right"><b>{{currency(dataTable.total.equity)}}</b></td>
                            </tr>
                            <tr>
                                <th >Leabilitas dan Modal</th>
                                <th class="text-right"><b>{{currency(dataTable.total.labilityEquity)}}</b></th>
                            </tr>
                        </tbody>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>
</div>


<script>
    var vm = new Vue({
        el:"#app",
        data:{
            param:{
                startDate:'',
                endDate:'',
                outlet:[],
                timeZone:''
            },
            dataTable:{
                data:[],
                total : {
                    aset:0,
                    leability:0,
                    equity:0,
                    lastPeriod:0,
                    curentPeriod:0,
                    labilityEquity :0
                }
            },
        },
        methods:{
            onApply(){
                if (this.param.outlet.length == 0) {
                    return Alert.warning('Oops...','Outlet belum dipilih')
                }
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/report/neraca/get_neraca')?>",
                    data: vm.param,
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide();
                        vm.dataTable.data=res

                        vm.dataTable.total.aset = 0;
                        vm.dataTable.total.lastPeriod = 0
                        vm.dataTable.total.curentPeriod = 0
                        vm.dataTable.total.equity = 0
                        vm.dataTable.total.labilityEquity = 0
                        vm.dataTable.total.leability = 0
                        var totalDebit  = 0
                        var totalKredit  = 0
                        var totalPeriode = 0
                        for (let i in res) {
                            switch (true) {
                                case (res[i].code_category <= 7) : 
                                    vm.dataTable.total.aset += res[i].total
                                    break;
                                case (res[i].code_category >= 8 && res[i].code_category <=11) : 
                                    vm.dataTable.total.leability += res[i].total
                                    break;
                                case (res[i].code_category == 12) : 
                                    vm.dataTable.total.equity += res[i].total
                                    break;
                                case (res[i].code_category >=13 ) : 
                                
                                    if (res[i].type_account == "D") {
                                        totalDebit += res[i].total
                                        totalPeriode += res[i].total
                                    }else{
                                        totalPeriode += res[i].total
                                        totalKredit += res[i].total
                                    }
                                    break;
                            }
                        }
                        // vm.dataTable.total.lastPeriod = lastPeriod
                        vm.dataTable.total.curentPeriod = totalKredit-totalDebit

                         vm.dataTable.total.equity  += vm.dataTable.total.lastPeriod+ vm.dataTable.total.curentPeriod
                         vm.dataTable.total.labilityEquity  = vm.dataTable.total.equity + vm.dataTable.total.leability

                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error("error","Gagal menampilkan data");
                    }
                });
            },
            currency(val){
                return currency(val)
        	},
        },
        mounted(){
            $(function() {
                setTimeout(function() {
                    vm.param.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                    vm.param.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
                    vm.param.timeZone = timezone();
                }, 100);                
            });
        }
    })

$(function() {
    $('#btn-date').on('apply.daterangepicker', function(ev, val) {
        $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
        vm.param.startDate = val.startDate.valueOf()
        vm.param.endDate = val.endDate.valueOf()
    });
});
</script>