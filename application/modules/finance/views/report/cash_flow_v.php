<style lang="">
    .table-hover tbody tr:hover td,
    .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }

    .sub1 {
        padding-left: 20px
    }

    .sub2 {
        padding-left: 40px
    }

    
    #tab>li.active>a,
    #tab>li.active>a:hover,
    #tab>li.active>a:focus {
        color: #000;
        cursor: default;
        background-color: #ddd;
        border: 1px solid #ddd;
        border-bottom-color: transparent;
    }
    
    #tab>li>a {
        color: #54b733;
        text-decoration: none;
    }
</style>

<div id="app">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-5">
                    <ul class="nav nav-tabs" role="tablist" id="tab">
                        <li role="presentation" class="active"><a href="#cashflow" aria-controls="cashflow" role="tab"
                                data-toggle="tab"><i class="fa fa-book"></i> Cash Flow</a></li>
                        <li role="presentation"><a href="#cashflow-detail" aria-controls="cashflow-detail" role="tab"
                                data-toggle="tab"><i class="fa fa-book"></i> Cash Flow Detail</a></li>
                    </ul>
                </div>
                <div class="col-sm-7">
                    <div class="pull-right">
                        <div class="btn-group">
                            <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de"
                                id="btn-date">
                                <span>
                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                </span>
                                <i class="caret"></i>
                            </button>
                        </div>
                        <div class="btn-group">
                            <select class="form-control btn btn-info outletSelect" multiple v-model="param.outlet">
                                <?php foreach ($form_select_outlet as $a): ?>
                                    <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <!-- Tab panes -->
                    <div class="tab-content">
                        <!-- cash flow recap -->
                        <div role="tabpanel" class="tab-pane active" id="cashflow">
                            <div class="row">
                                <div class="col-sm-12 table-responsive">
                                    <table class="table table-report table-hover table-bordered" id="labaRugiTable"
                                        width="100%">
                                        <tbody>
                                            <tr>
                                                <th colspan="9"></th>
                                                <th width="5%">Pagu (%)</th>
                                            </tr>
                                            <tr>
                                                <th colspan="10">Arus kas dari Operasional</th>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="7"><b>Total Penerimaan dari pelanggan</b></td>
                                                <td class="text-right"><b>{{currency(dataTable.data.sales)}}</b></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="7"><b>Pendapatan Lain-lain</b></td>
                                                <!-- ambil kas dan bank transaksi tras-ot dan jurnal input (d-k) -->
                                                <td class="text-right">
                                                    <b>{{currency(dataTable.data.pendapatan_lain)}}</b>
                                                </td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="6"><b>Pembayaran ke suplier</b></td>
                                                <td class="text-right">
                                                    <b>{{currency(dataTable.total.prc+dataTable.total.op)}}</b>
                                                </td>
                                                <td class="text-left">
                                                    <b>
                                                        {{
                                                        isNaN(parseFloat((dataTable.total.prc + dataTable.total.op) /
                                                        dataTable.data.sales * 100).toFixed(2))? '0.00':
                                                        parseFloat((dataTable.total.prc
                                                        + dataTable.total.op) / dataTable.data.sales * 100).toFixed(2)
                                                        }} %
                                                    </b>
                                                </td>
                                                <td id="pembayaran_suplier"></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="4">Persediaan</td>
                                                <td class="text-right">{{currency(dataTable.total.prc)}}</td>
                                                <td class="text-left">
                                                    {{
                                                    isNaN(parseFloat(dataTable.total.prc/dataTable.data.sales*100).toFixed(2))?'0.00':parseFloat(dataTable.total.prc/dataTable.data.sales*100).toFixed(2)
                                                    }} %
                                                </td>
                                                <td></td>
                                                <td id="persediaan"></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="4">Operasional</td>
                                                <td class="text-right">{{currency(dataTable.total.op)}}</td>
                                                <td class="text-left">{{
                                                    isNaN(parseFloat(dataTable.total.op/dataTable.data.sales*100).toFixed(2))?'0.00':parseFloat(dataTable.total.op/dataTable.data.sales*100).toFixed(2)
                                                    }} %
                                                </td>
                                                <td></td>
                                                <td id="operasional"></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="2"><b>Sisa Hutang Usaha periode berjalan</b></td>
                                                <td style="text-align: right;">
                                                    <b>
                                                        {{currency(dataTable.data.hutang.hutang_usaha*-1)}}
                                                    </b>
                                                </td>
                                                <td class="text-left">
                                                    ({{isNaN(parseFloat(dataTable.data.hutang.hutang_usaha/dataTable.data.sales*100).toFixed(2))?'0.00':parseFloat(dataTable.data.hutang.hutang_usaha/dataTable.data.sales*100).toFixed(2)}}
                                                    %)
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td id="sisa_hutang"></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="2"><b>Pembayaran hutang periode sebelumya</b></td>
                                                <td style="text-align: right;">
                                                    <b>{{currency(dataTable.data.hutang.pembayaran_hutang)}}</b>
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr @click="onDetail('investasi')">
                                                <th colspan="8"><b>Arus Kas dari investasi</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.investasi)}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                            <tr v-if="detail.investasi" v-for="(i,idx) in detail.investasi"
                                                class="animated fadeInDown">
                                                <td></td>
                                                <td></td>
                                                <td colspan="5">{{i.account}}</td>
                                                <td class="text-right">{{currency(i.total)}}</td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr @click="onDetail('pendanaan')">
                                                <th colspan="8"><b>Arus kas dari pendanaan</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.pendanaan)}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                            <tr v-if="detail.pendanaan" v-for="(i,idx) in detail.pendanaan"
                                                class="animated fadeInDown">
                                                <td></td>
                                                <td></td>
                                                <td colspan="5">{{i.account}}</td>
                                                <td class="text-right">{{currency(i.total)}}</td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <th colspan="8"><b>Saldo awal kas dan Bank</b></th>
                                                <th class="text-right">
                                                    <b>{{currency(parseFloat(dataTable.data.saldo_awal))}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                            <tr>
                                                <th colspan="8"><b>Saldo akhir kas dan Bank</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.saldoAkhir)}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- detail cash flow -->
                        <div role="tabpanel" class="tab-pane" id="cashflow-detail">
                            <div class="row">
                                <div class="col-sm-12 table-responsive">
                                    <table class="table table-report table-hover table-bordered" id="labaRugiTable"
                                        width="100%">
                                        <tbody>
                                            <tr>
                                                <th colspan="9"></th>
                                                <th width="5%">Pagu (%)</th>
                                            </tr>
                                            <tr>
                                                <th colspan="10">Arus kas dari Operasional</th>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="7"><b>Total Penerimaan dari pelanggan</b></td>
                                                <td class="text-right"><b>{{currency(dataTable.data.sales)}}</b></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="7"><b>Pendapatan belum ditagih</b></td>
                                                <td class="text-right"><b>{{currency(dataTable.data.piutang)}}</b></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="7"><b>Pendapatan dari pelanggan yang sudah diterima</b>
                                                </td>
                                                <td class="text-right">
                                                    <b>{{currency(dataTable.data.sales-dataTable.data.piutang)}}</b>
                                                </td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="7"><b>Pendapatan Lain-lain</b></td>
                                                <!-- ambil kas dan bank transaksi tras-ot dan jurnal input (d-k) -->
                                                <td class="text-right">
                                                    <b>{{currency(dataTable.data.pendapatan_lain)}}</b>
                                                </td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="6"><b>Pembayaran ke suplier</b></td>
                                                <td class="text-right">
                                                    <b>{{currency(dataTable.total.prc+dataTable.total.op)}}</b>
                                                </td>
                                                <td class="text-left">
                                                    <b>
                                                        {{
                                                        isNaN(parseFloat((dataTable.total.prc + dataTable.total.op) /
                                                        dataTable.data.sales * 100).toFixed(2))? '0.00':
                                                        parseFloat((dataTable.total.prc
                                                        + dataTable.total.op) / dataTable.data.sales * 100).toFixed(2)
                                                        }} %
                                                    </b>
                                                </td>
                                                <td id="pembayaran_suplier"></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="4">Persediaan</td>
                                                <td class="text-right">{{currency(dataTable.total.prc)}}</td>
                                                <td class="text-left">
                                                    {{
                                                    isNaN(parseFloat(dataTable.total.prc/dataTable.data.sales*100).toFixed(2))?'0.00':parseFloat(dataTable.total.prc/dataTable.data.sales*100).toFixed(2)
                                                    }} %
                                                </td>
                                                <td></td>
                                                <td id="persediaan"></td>
                                            </tr>
                                            <tr v-for="i in dataTable.data.prc">
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td>{{i.account}}</td>
                                                <td class="text-right">{{currency(parseFloat(i.total))}}</td>
                                                <td>{{parseFloat(i.total/dataTable.data.sales*100).toFixed(2)}} %</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td>{{i.pagu?i.pagu.replace('.',','):0}} %</td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="4">Operasional</td>
                                                <td class="text-right">{{currency(dataTable.total.op)}}</td>
                                                <td class="text-left">{{
                                                    isNaN(parseFloat(dataTable.total.op/dataTable.data.sales*100).toFixed(2))?'0.00':parseFloat(dataTable.total.op/dataTable.data.sales*100).toFixed(2)
                                                    }} %
                                                </td>
                                                <td></td>
                                                <td id="operasional"></td>
                                            </tr>
                                            <tr v-for="i in dataTable.data.op">
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td>{{i.account}}</td>
                                                <td class="text-right">{{currency(parseFloat(i.total))}}</td>
                                                <td>{{parseFloat(i.total/dataTable.data.sales*100).toFixed(2)}} %</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td>{{i.pagu?i.pagu.replace('.',','):0}} %</td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="2"><b>Sisa Hutang Usaha periode berjalan</b></td>
                                                <td style="text-align: right;">
                                                    <b>
                                                        {{currency(dataTable.data.hutang.hutang_usaha*-1)}}
                                                    </b>
                                                </td>
                                                <td class="text-left">
                                                    ({{isNaN(parseFloat(dataTable.data.hutang.hutang_usaha/dataTable.data.sales*100).toFixed(2))?'0.00':parseFloat(dataTable.data.hutang.hutang_usaha/dataTable.data.sales*100).toFixed(2)}}
                                                    %)
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td id="sisa_hutang"></td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td></td>
                                                <td colspan="2"><b>Pembayaran hutang periode sebelumya</b></td>
                                                <td style="text-align: right;">
                                                    <b>{{currency(dataTable.data.hutang.pembayaran_hutang)}}</b>
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr @click="onDetail('investasi')">
                                                <th colspan="8"><b>Arus Kas dari investasi</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.investasi)}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                            <tr v-if="detail.investasi" v-for="(i,idx) in detail.investasi"
                                                class="animated fadeInDown">
                                                <td></td>
                                                <td></td>
                                                <td colspan="5">{{i.account}}</td>
                                                <td class="text-right">{{currency(i.total)}}</td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr @click="onDetail('pendanaan')">
                                                <th colspan="8"><b>Arus kas dari pendanaan</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.pendanaan)}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                            <tr v-if="detail.pendanaan" v-for="(i,idx) in detail.pendanaan"
                                                class="animated fadeInDown">
                                                <td></td>
                                                <td></td>
                                                <td colspan="5">{{i.account}}</td>
                                                <td class="text-right">{{currency(i.total)}}</td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <th colspan="8"><b>Total aktifitas kas dan bank</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.arusKas)}}</b></th>
                                                <th></th>
                                            </tr>
                                            <tr>
                                                <th colspan="8"><b>Saldo awal kas dan Bank</b></th>
                                                <th class="text-right">
                                                    <b>{{currency(parseFloat(dataTable.data.saldo_awal))}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                            <tr>
                                                <th colspan="8"><b>Saldo akhir kas dan Bank</b></th>
                                                <th class="text-right"><b>{{currency(dataTable.total.saldoAkhir)}}</b>
                                                </th>
                                                <th></th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            param: {
                startDate: '',
                endDate: '',
                outlet: [],
                timeZone: ''
            },
            detail: {
                investasi: [],
                pendanaan: []
            },
            dataTable: {
                data: {
                    sales: 0,
                    op: 0,
                    prc: [],
                    hutang: 0,
                    aset: 0,
                    saldo_awal: 0,
                    saldo_akhir: 0,
                    detail: []
                },
                total: {
                    sales: 0,
                    op: 0,
                    prc: 0,
                    arusKas: 0,
                    saldoAkhir: 0,
                    investasi: 0,
                    pendanaan: 0
                }
            },
        },
        methods: {
            onApply() {
                if (this.param.outlet.length == 0) {
                    return Alert.warning('Oops...', 'Outlet belum dipilih')
                }
                $.ajax({
                    type: "post",
                    url: "<?= base_url('finance/report/cash_flow/data_cash_flow') ?>",
                    data: vm.param,
                    dataType: "json",
                    beforeSend() {
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide();
                        // console.log(res);

                        vm.dataTable.data = res
                        vm.dataTable.total.sales = 0
                        vm.dataTable.total.op = 0
                        vm.dataTable.total.prc = 0
                        vm.dataTable.total.pendanaan = 0
                        vm.dataTable.total.sales = res.sales
                        vm.detail.pendanaan = []
                        res.prc.map((e) => {
                            vm.dataTable.total.prc += parseFloat(e.total)
                        })
                        res.op.map((e) => {
                            vm.dataTable.total.op += parseFloat(e.total)
                        })
                        let pembayarnSuplier = parseFloat(vm.dataTable.total.prc) + parseFloat(vm.dataTable.total.op);
                        vm.dataTable.total.arusKas = parseFloat(vm.dataTable.total.sales) - parseFloat(pembayarnSuplier) + parseFloat(vm.dataTable.data.hutang.hutang_usaha) - parseFloat(vm.dataTable.data.hutang.pembayaran_hutang);
                        vm.dataTable.total.saldoAkhir = parseFloat(vm.dataTable.total.arusKas) + parseFloat(vm.dataTable.data.saldo_awal);
                        // console.log(pembayarnSuplier);
                        // console.log(vm.dataTable.total.arusKas);
                        // console.log(vm.dataTable.data.saldo_awal);
                        // console.log(vm.dataTable.data.hutang.hutang_usaha);
                        // console.log(vm.dataTable.data.hutang.pembayaran_hutang);


                        for (const i in res.detail) {
                            switch (i) {
                                case 'investasi':
                                    for (const invest in res.detail.investasi) {
                                        vm.dataTable.total.investasi += res.detail.investasi[invest].total
                                    }
                                    break;
                                case 'pendanaan':
                                    for (const dana in res.detail.pendanaan) {
                                        vm.dataTable.total.pendanaan += res.detail.pendanaan[dana].total
                                    }
                                    break;
                            }
                        }

                        // pagu
                        if (res.pagu) {
                            for (const i in res.pagu) {
                                $("#" + res.pagu[i].key).html(res.pagu[i].pagu.replace('.', ',') + " %")
                            }

                        }

                    },
                    error(err) {
                        loading.hide();
                        console.log(err);
                        Alert.error("error", "Gagal menampilkan data");
                    }
                });
            },
            currency(val) {
                return currency(val)
            },
            number(val) {
                return number(val)
            },
            onDetail(type) {
                // console.log(type+"<br>"+this.detail);

                switch (type) {
                    case 'investasi':
                        this.detail.investasi.length == 0 ? this.detail.investasi = this.dataTable.data.detail.investasi : this.detail.investasi = []
                        break;
                    case 'pendanaan':
                        this.detail.pendanaan.length == 0 ? this.detail.pendanaan = this.dataTable.data.detail.pendanaan : this.detail.pendanaan = []
                        break;
                }
            }

        },
        mounted() {
            $(function () {
                setTimeout(function () {
                    vm.param.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                    vm.param.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
                    vm.param.timeZone = timezone();
                }, 100);
            });
        }
    })

    $(function () {
        $('#btn-date').on('apply.daterangepicker', function (ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY") + ' - ' + val.endDate.format("D MMMM YYYY"));
            vm.param.startDate = val.startDate.valueOf()
            vm.param.endDate = val.endDate.valueOf()
        });
    });
</script>