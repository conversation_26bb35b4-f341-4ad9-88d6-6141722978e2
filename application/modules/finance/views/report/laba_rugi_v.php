<style lang="">
    .table-hover tbody tr:hover td,
    .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }

    .table-report {
        table-layout: auto;
        /* Ensures that columns respect defined widths */
        width: 100%;
    }

    .sticky-column {
        position: sticky;
        left: 0;
        max-width: 40%;
        /* Set to 20% of the table width */
        background-color: #313233;
        /* Match your table color */
        z-index: 100;
        /* Keeps it above other columns */
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.3);
        /* Adds subtle depth */
        padding-left: 10px;
        border-right: 1px solid #555;
        /* Defines separation */
        white-space: nowrap;
        /* Prevents text wrapping if content overflows */
    }

    .table-report>thead>tr>th,
    .table-report>thead>tr>th:focus {
        border-bottom: none;
    }
</style>

<div id="app">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-12">
                    <ul class="nav nav-tabs" role="tablist" id="tab">
                        <li role="presentation" class="active"><a href="#labarugi" aria-controls="labarugi" role="tab"
                                data-toggle="tab"><i class="fa fa-book"></i> Laba Rugi</a></li>
                        <li role="presentation"><a href="#labarugi-detail" aria-controls="labarugi-detail" role="tab"
                                data-toggle="tab"><i class="fa fa-book"></i> Laba Rugi Detail</a></li>
                    </ul>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="tab-content">
                        <!-- laba rugi recap -->
                        <div role="tabpanel" class="tab-pane active" id="labarugi">
                            <div class="row" style="margin-top: 10px;">
                                <div class="col-sm-7 col-sm-offset-5">
                                    <div class="pull-right">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-info daterange"
                                                style="background-color: #5bc0de" id="btn-date">
                                                <span>
                                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                                </span>
                                                <i class="caret"></i>
                                            </button>
                                        </div>
                                        <div class="btn-group">
                                            <select class="form-control btn btn-info outletSelect" multiple
                                                v-model="param.outlet">
                                                <?php foreach ($form_select_outlet as $a): ?>
                                                    <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?>
                                                    </option>
                                                <?php endforeach ?>
                                            </select>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-primary btn-block" @click="onApply"
                                                type="button">Apply</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 table-responsive">
                                    <table class="table table-report table-hover" id="labaRugiTable" width="100%">
                                        <tbody>
                                            <tr>
                                                <th></th>
                                                <th></th>
                                                <th width="5%"></th>
                                                <th width="5%" class="text-center">Pagu (%)</th>
                                            </tr>
                                            <tr>
                                                <th colspan="4">Pendapatan</th>
                                            </tr>
                                            <template v-for="(i,idx) in dataTable.data">
                                                <tr v-for="(j,index) in i.detail"
                                                    v-if="i.code_category == 13 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                                                    <td>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                        {{j.account_code}}-{{j.account_name}}</td>
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </tr>
                                            </template>
                                            <tr>
                                                <td class="text-bold">&ensp;&ensp;&ensp;Total Pendapatan</td>
                                                <td class="text-right text-bold">{{curent(dataTable.total.pendapatan)}}
                                                </td>
                                                <td class="text-right text-bold"></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <th colspan="4">Beban Pokok</th>
                                            </tr>
                                            <template v-for="(i,idx) in dataTable.data">
                                                <tr v-for="(j,index) in i.detail"
                                                    v-if="i.code_category == 15 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                                                    <td>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                        {{j.account_code}}-{{j.account_name}}</td>
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </tr>
                                            </template>
                                            <tr>
                                                <td class="text-bold">&ensp;&ensp;&ensp; Total Beban</td>
                                                <td class="text-right text-bold">{{curent(dataTable.total.beban)}}</td>
                                                <td class="text-center text-bold">{{percentage(dataTable.total.beban)}}
                                                </td>
                                                <td id="total_beban"></td>
                                            </tr>
                                            <tr>
                                                <td class="text-bold">&ensp;Laba Kotor</td>
                                                <td class="text-right text-bold">
                                                    {{curent(dataTable.total.pendapatan-dataTable.total.beban)}}</td>
                                                <td class="text-center text-bold">
                                                    {{percentage(dataTable.total.pendapatan-dataTable.total.beban)}}
                                                </td>
                                                <td id="laba_kotor"></td>
                                            </tr>
                                            <tr>
                                                <th colspan="4">Beban Operasional</th>
                                            </tr>
                                            <template v-for="(i,idx) in dataTable.data">
                                                <tr v-for="(j,index) in i.detail"
                                                    v-if="i.code_category == 16 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                                                    <td>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                        {{j.account_code}}-{{j.account_name}}</td>
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </tr>
                                            </template>
                                            <tr>
                                                <td class="text-bold">&ensp;&ensp;&ensp;Total dari Beban Operasional
                                                </td>
                                                <td class="text-right text-bold">
                                                    {{curent(dataTable.total.bebanOprasional)}}</td>
                                                <td class="text-center text-bold">
                                                    {{percentage(dataTable.total.bebanOprasional)}}</td>
                                                <td id="beban_operasional"></td>
                                            </tr>

                                            <tr>
                                                <td class="text-bold">&ensp; Laba Oprasional</td>
                                                <td class="text-right text-bold">
                                                    {{curent(dataTable.total.pendapatan-dataTable.total.beban-dataTable.total.bebanOprasional)}}
                                                </td>
                                                <td class="text-center text-bold">
                                                    {{percentage(dataTable.total.pendapatan-dataTable.total.beban-dataTable.total.bebanOprasional)}}
                                                </td>
                                                <td id="laba_operasional"></td>
                                            </tr>

                                            <tr>
                                                <th colspan="4">Pendapatan (Beban Lain-Lain)</th>
                                            </tr>
                                            <tr>
                                                <td colspan="4">&ensp;&ensp;&ensp; Pendapatan lain-lain</td>
                                            </tr>
                                            <template v-for="(i,idx) in dataTable.data">
                                                <tr v-for="(j,index) in i.detail"
                                                    v-if="i.code_category == 14 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                                                    <td>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                        {{j.account_code}}-{{j.account_name}}</td>
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </tr>
                                            </template>
                                            <tr>
                                                <td class="text-bold">&ensp;&ensp;&ensp; Total pendapatan Lain-Lain</td>
                                                <td class="text-right text-bold">{{curent(dataTable.total.pendapatan2)}}
                                                </td>
                                                <td class="text-center text-bold">
                                                    {{percentage(dataTable.total.pendapatan2)}}</td>
                                                <td id="pendapatan_lain"></td>
                                            </tr>
                                            <tr>
                                                <td colspan="4">&ensp;&ensp;&ensp; Beban lain-lain</td>
                                            </tr>
                                            <template v-for="(i,idx) in dataTable.data">
                                                <tr v-for="(j,index) in i.detail"
                                                    v-if="i.code_category == 17 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                                                    <td>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                        {{j.account_code}}-{{j.account_name}}</td>
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </tr>
                                            </template>
                                            <tr>
                                                <td class="text-bold">&ensp;&ensp;&ensp; Total Beban Lain-Lain</td>
                                                <td class="text-right text-bold">{{curent(dataTable.total.beban2)}}</td>
                                                <td class="text-center text-bold">{{percentage(dataTable.total.beban2)}}
                                                </td>
                                                <td id="beban_lain"></td>
                                            </tr>
                                            <tr>
                                                <td class="text-bold">&ensp; Total Dari Pendapatan (Beban Lain-Lain)
                                                </td>
                                                <td class="text-right">
                                                    {{curent(dataTable.total.pendapatan2-dataTable.total.beban2)}}
                                                </td>
                                                <td class="text-center text-bold">
                                                    {{percentage(dataTable.total.pendapatan2-dataTable.total.beban2)}}
                                                </td>
                                                <td id="total_pendapatan"></td>
                                            </tr>
                                            <tr>
                                                <th class="text-bold"> Laba (Rugi)</th>
                                                <th class="text-right text-bold">
                                                    {{curent(dataTable.total.pendapatan-dataTable.total.beban-dataTable.total.bebanOprasional+(dataTable.total.pendapatan2-dataTable.total.beban2))}}
                                                </th>
                                                <th class="text-right"></th>
                                                <th></th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <!-- laba rugi detail -->
                        <div role="tabpanel" class="tab-pane" id="labarugi-detail">
                            <div class="row" style="margin-top: 10px;">
                                <div class="col-sm-7 col-sm-offset-5">
                                    <div class="pull-right">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-info datepicker3"
                                                style="background-color: #5bc0de" id="btn-date-detail">
                                                <span id="date-text">
                                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                                </span>
                                                <i class="caret"></i>
                                            </button>
                                        </div>
                                        <div class="btn-group">
                                            <select class="form-control btn btn-info outletSelect" multiple
                                                v-model="paramDetail.outletDetail">
                                                <?php foreach ($form_select_outlet as $a): ?>
                                                    <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?>
                                                    </option>
                                                <?php endforeach ?>
                                            </select>
                                        </div>
                                        <div class="btn-group">
                                            <button class="btn btn-primary btn-block" @click="onApplyDetail"
                                                type="button">Apply</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 table-responsive" style="padding: 0px;">
                                    <table class="table table-report table-hover table-bordered"
                                        id="labaRugiTableDetail" width="100%">
                                        <thead>
                                            <tr>
                                                <th rowspan="2" class="sticky-column"
                                                    style="background-color: #232323;border-bottom: none;">
                                                    Rincian</th>
                                                <th colspan="3" class="text-center">JAN</th>
                                                <th colspan="3" class="text-center">FEB</th>
                                                <th colspan="3" class="text-center">MAR</th>
                                                <th colspan="3" class="text-center">APR</th>
                                                <th colspan="3" class="text-center">MEI</th>
                                                <th colspan="3" class="text-center">JUN</th>
                                                <th colspan="3" class="text-center">JUL</th>
                                                <th colspan="3" class="text-center">AGS</th>
                                                <th colspan="3" class="text-center">SEP</th>
                                                <th colspan="3" class="text-center">OKT</th>
                                                <th colspan="3" class="text-center">NOV</th>
                                                <th colspan="3" class="text-center">DES</th>
                                            </tr>
                                            <tr>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                                <th class="text-right">Nominal</th>
                                                <th class="text-center">%</th>
                                                <th>Pagu</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="sticky-column">Pendapatan</td>
                                                <template v-for="i in 12">
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr v-for="i in dataDetail"
                                                v-if="i.code_category == 13 && (i.debit !=0 || i.kredit != 0 || i.total != 0)"
                                                style="font-size: x-small;">
                                                <td class="sticky-column">{{i.account_code}}- {{i.account_name}}
                                                </td>
                                                <template v-for="(j,idx) in i.detail">
                                                    <template v-if="'bulan-'+idx">
                                                        <td class="text-right" v-if="j.type_account=='D'">
                                                            {{curent(j.debit-j.kredit)}}</td>
                                                        <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                        <td>{{10}}%</td>
                                                        <td>0</td>
                                                    </template>
                                                    <template v-else>
                                                        <td class="text-right">0</td>
                                                        <td class="text-right">0%</td>
                                                        <td>0</td>
                                                    </template>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column ">Total Pendapatan</td>
                                                <template v-for="(j,idx) in totalDetail">
                                                    <td  class="text-right text-bold">{{curent(j.totalPendapatan)}}
                                                    </td>
                                                    <td class="text-right text-bold"></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="sticky-column">Beban Pokok</td>
                                                <template v-for="i in 12">
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr v-for="i in dataDetail"
                                                v-if="i.code_category == 15 && (i.debit !=0 || i.kredit != 0 || i.total != 0)"
                                                style="font-size: x-small;">
                                                <td class="sticky-column">
                                                    {{i.account_code}}-{{i.account_name}}</td>
                                                <template v-for="j in i.detail">
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right">0%</td>
                                                    <td>0</td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column">Total Beban</td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold">{{curent(j.totalBeban)}}</td>
                                                    <td class="text-center text-bold">0%
                                                    </td>
                                                    <td id="total_beban"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column">Laba Kotor</td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold">
                                                        {{curent(j.totalPendapatan-j.totalBeban)}}</td>
                                                    <td class="text-center text-bold">
                                                        0%
                                                    </td>
                                                    <td id="laba_kotor"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="sticky-column">Beban Operasional</td>
                                                <template v-for="i in 12">
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr v-for="i in dataDetail"
                                                v-if="i.code_category == 16 && (i.debit !=0 || i.kredit != 0 || i.total != 0)"
                                                style="font-size: x-small;">
                                                <td class="sticky-column">
                                                    {{i.account_code}}-{{i.account_name}}
                                                </td>
                                                <template v-for="j in i.detail">
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column">Total dari Beban Operasional
                                                </td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold">{{curent(j.totalBebanOperasional)}}</td>
                                                    <td class="text-center text-bold">
                                                        0%</td>
                                                    <td id="beban_operasional"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column">Laba Oprasional</td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold">{{curent(j.totalPendapatan-j.totalBeban-j.totalBebanOperasional)}}
                                                    </td>
                                                    <td class="text-center text-bold">
                                                        0%
                                                    </td>
                                                    <td id="laba_operasional"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="sticky-column">Pendapatan (Beban Lain-Lain)</td>
                                                <template v-for="i in 12">
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="sticky-column"> Pendapatan lain-lain</td>
                                                <template v-for="i in 12">
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr v-for="i in dataDetail"
                                                v-if="i.code_category == 14 && (i.debit !=0 || i.kredit != 0 || i.total != 0)"
                                                style="font-size: x-small;">
                                                <td class="sticky-column">
                                                    {{i.account_code}}-{{i.account_name}}
                                                </td>
                                                <template v-for="j in i.detail">
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right">0%</td>
                                                    <td></td>
                                                </template>
                                            </tr>

                                            <tr>
                                                <td class="text-bold sticky-column">Total pendapatan Lain-Lain</td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold"> {{curent(j.totalPendapatan2)}}
                                                    </td>
                                                    <td class="text-center text-bold">
                                                        0%</td>
                                                    <td id="pendapatan_lain"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="sticky-column"> Beban lain-lain</td>
                                                <template v-for="i in 12">
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                            <tr v-for="i in dataDetail"
                                                v-if="i.code_category == 17 && (i.debit !=0 || i.kredit != 0 || i.total != 0)"
                                                style="font-size: x-small;">
                                                <td class="sticky-column">
                                                    {{i.account_code}}-{{i.account_name}}
                                                </td>
                                                <template v-for="j in i.detail">
                                                    <td class="text-right" v-if="j.type_account=='D'">
                                                        {{curent(j.debit-j.kredit)}}</td>
                                                    <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </template>
                                            </tr>

                                            <tr>
                                                <td class="text-bold sticky-column">Total Beban Lain-Lain</td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold">{{curent(j.totalBeban2)}}</td>
                                                    <td class="text-center text-bold">0%
                                                    </td>
                                                    <td id="beban_lain"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column">&ensp; Total Dari Pendapatan (Beban
                                                    Lain-Lain)
                                                </td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right">{{curent(j.totalPendapatan2-j.totalBeban2)}}
                                                    </td>
                                                    <td class="text-center text-bold">
                                                        0%
                                                    </td>
                                                    <td id="total_pendapatan"></td>
                                                </template>
                                            </tr>
                                            <tr>
                                                <td class="text-bold sticky-column"> Laba (Rugi)</td>
                                                 <template v-for="(j,idx) in totalDetail">
                                                    <td class="text-right text-bold"> {{curent((j.totalPendapatan-j.totalBeban-j.totalBebanOperasional)+(j.totalPendapatan2-j.totalBeban2))}}
                                                    </td>
                                                    <td class="text-right"></td>
                                                    <td></td>
                                                </template>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    var vm = new Vue({
        el: "#app",
        data: {
            param: {
                startDate: '',
                endDate: '',
                outlet: [],
                timeZone: ''
            },
            paramDetail: {
                year: "",
                outletDetail: [],
                timeZone:timezone()
            },
            dataTable: {
                data: [],
                total: {
                    pendapatan: 0,
                    beban: 0,
                    bebanOprasional: 0,
                    pendapatan2: 0,
                    beban2: 0
                }
            },
            dataDetail: [],
            totalDetail: []
        },
        methods: {
            onApply() {
                if (this.param.outlet.length == 0) {
                    return Alert.warning('Oops...', 'Outlet belum dipilih')
                }
                $.ajax({
                    type: "post",
                    url: "<?= base_url('finance/report/laba_rugi/get_laba_rugi') ?>",
                    data: vm.param,
                    dataType: "json",
                    beforeSend() {
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide();
                        vm.dataTable.data = res

                        vm.dataTable.total.pendapatan = 0;
                        vm.dataTable.total.beban = 0;
                        vm.dataTable.total.pendapatan2 = 0;
                        vm.dataTable.total.beban2 = 0;
                        vm.dataTable.total.bebanOprasional = 0;
                        for (let i in res) {
                            switch (res[i].code_category) {
                                case '13': //pendapatan
                                    vm.dataTable.total.pendapatan += (res[i].kredit - res[i].debit)
                                    break;
                                case '15': //beban
                                    vm.dataTable.total.beban += (res[i].debit - res[i].kredit);
                                    break;
                                case '14': //pendapatan lain lain
                                    vm.dataTable.total.pendapatan2 += (res[i].kredit - res[i].debit)
                                    break;
                                case '17': //bebean lainya
                                    vm.dataTable.total.beban2 += (res[i].debit - res[i].kredit)
                                    // console.log(res[i].saldo_debit+res[i].debit);
                                    break;
                                case '16': //beban
                                    vm.dataTable.total.bebanOprasional += (res[i].debit - res[i].kredit)
                                    break;
                                default:
                                    break;
                            }
                        }

                        // pagu
                        if (res.pagu) {
                            for (const i in res.pagu) {
                                $("#" + res.pagu[i].key).html(res.pagu[i].pagu.replace('.', ',') + " %")
                            }
                        }
                    },
                    error(err) {
                        loading.hide();
                        console.log(err);
                        Alert.error("error", "Gagal menampilkan data");
                    }
                });
            },
            onApplyDetail() {
                if (this.paramDetail.outletDetail.length == 0) {
                    return Alert.warning('Oops...', 'Outlet belum dipilih')
                }
                if (this.paramDetail.year == '') {
                    return Alert.warning('Oops...', 'Tahun belum dipilih')
                }
                $.ajax({
                    type: "post",
                    url: "<?= base_url('finance/report/laba_rugi/laba_rugi_detail') ?>",
                    data: vm.paramDetail,
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success(res) {
                        loading.hide()
                        vm.dataDetail = res
                        // total
                        const result = {};

                        // Initialize result structure for 12 months
                        for (let i = 1; i <= 12; i++) {
                            result[`bulan-${i}`] = {
                                totalPendapatan: 0,
                                totalBeban: 0,
                                totalPendapatan2: 0,
                                totalBeban2: 0,
                                totalBebanOperasional: 0
                            };
                        }

                        // Process each account
                        res.forEach(account => {
                            const { code_category, detail } = account;

                            // Process each month in the account details
                            for (let month in detail) {
                                const { debit, kredit } = detail[month];
                                const debitValue = parseFloat(debit) || 0;
                                const kreditValue = parseFloat(kredit) || 0;

                                if (result[month]) {
                                    switch (code_category) {
                                        case "13": // Pendapatan
                                            result[month].totalPendapatan += kreditValue - debitValue;
                                            break;
                                        case "15": // Beban
                                            result[month].totalBeban += debitValue - kreditValue;
                                            break;
                                        case "14": // Pendapatan Lainnya
                                            result[month].totalPendapatan2 += kreditValue - debitValue;
                                            break;
                                        case "17": // Beban Lainnya
                                            result[month].totalBeban2 += debitValue - kreditValue;
                                            break;
                                        case "16": // Beban Operasional
                                            result[month].totalBebanOperasional += debitValue - kreditValue;
                                            break;
                                    }
                                }
                            }
                        });
                        vm.totalDetail = result;
                    },
                    error(err) {
                        Alert.error("gagal menampilkan data")
                        loading.hide()
                        console.log(err);

                    }
                });
            },
            curent(val) {
                return currency(val)
            },
            percentage(nominal) {
                let totalPendapatan = this.dataTable.total.pendapatan
                let percent = nominal / totalPendapatan * 100;
                let result = "0 %"
                if (!isNaN(percent)) {
                    result = Number(percent).toFixed(2) + ' %'
                }
                return result;
            }
        },
        mounted() {
            $(function () {
                setTimeout(function () {
                    vm.param.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                    vm.param.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
                    vm.param.timeZone = timezone();
                }, 100);
            });
        }
    })

    $(function () {
        $('#btn-date').on('apply.daterangepicker', function (ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY") + ' - ' + val.endDate.format("D MMMM YYYY"));
            vm.param.startDate = val.startDate.valueOf()
            vm.param.endDate = val.endDate.valueOf()
        });

        $('#btn-date-detail').datepicker({
            autoclose: true,
            format: "yyyy",
            viewMode: "years",
            minViewMode: "years",
        }).on('changeDate', function (e) {
            // Get the selected date
            const selectedDate = e.format(); // Formatted based on your datepicker format
            // Update the button's content
            $('#date-text').html(`<i class="glyphicon glyphicon-calendar"></i> ${selectedDate}`);
            vm.paramDetail.year = selectedDate
        });;


    });
</script>