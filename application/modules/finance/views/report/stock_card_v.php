<style type="text/css">
    div.dt-buttons {
        float: right;
        margin-left: 10px;
    }

    .table-hover tbody tr:hover td,
    .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }

    table .collapse.in {
        display: table-row;
    }

    .hiddenRow {
        padding: 0 !important;
    }
</style>
<div id="app">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-book"></i> Kartu Persediaan </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
                    <div class="pull-right">
                        <div class="btn-group">
                            <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de"
                                id="btn-date">
                                <span>
                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                </span>
                                <i class="caret"></i>
                            </button>
                        </div>
                        <div class="btn-group">
                            <select class="form-control btn btn-info outletSelect" v-model="param.outlet">
                                <?php foreach ($form_select_outlet as $a): ?>
                                    <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-default btn-block" @click="onExport" type="button">Export xls</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 table-responsive">
                    <table class="table table-report table-hover table-bordered" id="stockCard-table" width="100%">
                        <thead>
                            <tr>
                                <th rowspan="2" class="text-center" style="width: 2%;">No</th>
                                <th rowspan="2" class="text-center" style="width: 18%;">Nama Produk</th>
                                <th rowspan="2" class="text-center" style="width: 15%;">Keterangan</th>
                                <th colspan="3" class="text-center" style="width: 15%;">Saldo Awal</th>
                                <th colspan="3" class="text-center" style="width: 20%;">In</th>
                                <th colspan="3" class="text-center" style="width: 20%;">Out</th>
                                <th colspan="3" class="text-center" style="width: 20%;">Sisa</th>
                                <th rowspan="2" class="text-center" style="width: 5%;">#</th>
                            </tr>
                            <tr>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Harga</th>
                                <th class="text-center">Jumlah</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Harga</th>
                                <th class="text-center">Jumlah</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Harga</th>
                                <th class="text-center">Jumlah</th>
                                <th class="text-center">Qty</th>
                                <th class="text-center">Harga</th>
                                <th class="text-center">Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center" v-if="dataProduk.length == 0">
                                <td colspan="16">Data tidak tersedia</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- modal detail -->
    <!-- modal form jurnal -->
    <div class="modal fade" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modalDetail"
        data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document" style="width:90%">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="exampleModalp">Detail Kartu Persediaan #{{detail.pd_id}}</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-report table-hover table-bordered" id="table-product"
                                width="100%">
                                <thead>
                                    <tr>
                                        <th rowspan="2" class="text-center" style="width: 18%;">Nama Produk</th>
                                        <th rowspan="2" class="text-center" style="width: 15%;">Keterangan</th>
                                        <th colspan="3" class="text-center" style="width: 15%;">Saldo Awal</th>
                                        <th colspan="3" class="text-center" style="width: 20%;">In</th>
                                        <th colspan="3" class="text-center" style="width: 20%;">Out</th>
                                        <th colspan="3" class="text-center" style="width: 20%;">Sisa</th>
                                    </tr>
                                    <tr>
                                        <th class="text-center">Qty</th>
                                        <th class="text-center">Harga</th>
                                        <th class="text-center">Jumlah</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-center">Harga</th>
                                        <th class="text-center">Jumlah</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-center">Harga</th>
                                        <th class="text-center">Jumlah</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-center">Harga</th>
                                        <th class="text-center">Jumlah</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{dataSelected.product_name}}</td>
                                        <td>{{dataSelected.outlet_name}}</td>
                                        <td>{{formatNumber(dataSelected.stock_awal)}}</td>
                                        <td>{{currency(dataSelected.price_awal)}}</td>
                                        <td>{{currency(dataSelected.total_price_awal)}}</td>
                                        <td>{{formatNumber(dataSelected.stock_in)}}</td>
                                        <td>{{currency(dataSelected.price_in)}}</td>
                                        <td>{{currency(dataSelected.total_price_in)}}</td>
                                        <td>{{formatNumber(dataSelected.stock_out)}}</td>
                                        <td>{{currency(dataSelected.price_out)}}</td>
                                        <td>{{currency(dataSelected.total_price_out)}}</td>
                                        <td>{{formatNumber(dataSelected.sisa_stock)}}</td>
                                        <td>{{currency(dataSelected.total_price)}}</td>
                                        <td>{{currency(dataSelected.total_price_sisa)}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-report table-hover table-bordered" id="table-detail"
                                width="100%">
                                <thead>
                                    <tr>
                                        <th rowspan="2" class="text-center" style="width: 2%;">No</th>
                                        <th colspan="5" class="text-center" style="width: 45%;">Stok Masuk</th>
                                        <th colspan="4" class="text-center" style="width: 45%;">Stok Keluar</th>
                                    </tr>
                                    <tr>
                                        <th class="text-center">Tanggal</th>
                                        <th class="text-center">Transaksi</th>
                                        <th class="text-center">Qty In</th>
                                        <th class="text-center">Qty Out</th>
                                        <th class="text-center">Harga Unit</th>
                                        <th class="text-center">Tanggal</th>
                                        <th class="text-center">Transaksi</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-center">Harga Unit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="text-center" v-if="dataInTable.length == 0">
                                        <td colspan="14">Data tidak tersedia</td>
                                    </tr>
                                    <template v-else v-for="(i,idx) in dataInTable" >
                                        <template v-if="i.type == 'in'">
                                            <tr>
                                                <td :rowspan="i.detail.length+1">{{idx+1}}</td>
                                                <td :rowspan="i.detail.length+1" >{{i.date}}</td>
                                                <td :rowspan="i.detail.length+1" >{{i.ket}}</td>
                                                <td :rowspan="i.detail.length+1" >{{number(i.stock_in)}}</td>
                                                <td :rowspan="i.detail.length+1" >{{number(i.stock_out)}}</td>
                                                <td :rowspan="i.detail.length+1" >{{currency(i.price)}}</td>
                                            </tr>
                                            <tr v-for="(detail, index) in i.detail">
                                                <td>{{detail.date}}</td>
                                                <td>{{detail.ket}}</td>
                                                <td>{{number(detail.stock_out)}}</td>
                                                <td>{{currency(detail.price)}}</td>
                                            </tr>
                                        </template>
                                        <template v-else>
                                            <tr>
                                                <td>{{idx+1}}</td>
                                                <td colspan="5" style="background-color:cadetblue"></td>
                                                <td>{{i.date}}</td>
                                                <td>{{i.ket}}</td>
                                                <td>{{number(i.stock_out)}}</td>
                                                <td>{{currency(i.price)}}</td>
                                            </tr>
                                        </template>
                                    </template>
                                    <!-- <tr v-else v-for="(i,idx) in dataInTable">
                                        <td>{{idx+1}}</td>
                                        <template v-if="i.type == 'in'">
                                            <td :rowspan="i.detail.length" >{{i.date}}</td>
                                            <td :rowspan="i.detail.length" >{{i.ket}}</td>
                                            <td :rowspan="i.detail.length" >{{i.stock_in}}</td>
                                            <td :rowspan="i.detail.length" >{{currency(i.price)}}</td>
                                        </template>
                                        <template v-for="(detail, index) in i.detail">
                                                <td>{{detail.date}}</td>
                                                <td>{{detail.ket}}</td>
                                                <td>{{detail.stock_out}}</td>
                                                <td>{{currency(detail.price)}}</td>
                                            </template>
                                        <template v-else>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td>{{i.date}}</td>
                                            <td>{{i.ket}}</td>
                                            <td>{{i.stock_out}}</td>
                                            <td>{{currency(i.price)}}</td>
                                        </template>
                                    </tr> -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div><!-- /.modal-body -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Close</button>
                    <!-- <button type="button" class="btn btn-primary" @click="onSubmit()" id="save">Simpan</button> -->
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
</div>

<script>
    var vm = new Vue({
        el: "#app",
        data: {
            param: {
                startDate: '',
                endDate: '',
                outlet: ''
            },
            detail:{
                pd_id:0
            },
            dataProduk: [],
            dataSelected:'',
            dataInTable:[]
        },
        methods: {
            datatable(param) {
                var t = $("#stockCard-table").DataTable({
                    ajax: {
                        url: '<?= base_url() ?>finance/report/stock_card/get_data',
                        type: "POST",
                        data: param
                    },
                    destroy: true,
                    serverSide: false,
                    language: {
                        decimal: ",",
                    },
                    // scrollY: '55vh',
                    // scrollCollapse: true,
                    // paging: false,
                    columns: [{
                        data: null,
                        class: "text-center",
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: "product_name",
                    },
                    {
                        data: null,
                        render(data, type, row) {
                            var html = `<ul>
                                        <li><small>pembelian : `+ row.pembelian + `</small></li>
                                        <li><small>penjuaan : `+ row.penjualan + `</small></li>
                                        <li><small>persediaan : `+ row.persediaan + `</small> </li>
                                    </ul>
                                `
                            return html
                        },
                    },
                    {
                        data: "stock_awal",
                        render(data, type, row) {
                            return number(data)
                        },
                    },
                    {
                        data: "price_awal",
                        render(data, type, row) {
                            return currency(data)
                        },
                    },
                    {
                        data: "total_price_awal",
                        render(data, type, row) {
                            return currency(data)
                        },
                    },
                    {
                        data: "stock_in",
                        render(data, type, row) {
                            return number(data)
                        },
                    },
                    {
                        data: "price_in",
                        render(data, type, row) {
                            return currency(data)
                        },
                    },
                    {
                        data: "total_price_in",
                        render(data, type, row) {
                            return currency(data)
                        },
                    },
                    {
                        data: "stock_out",
                        render(data, type, row) {
                            return number(data)
                        },
                    },
                    {
                        data: "price_out",
                        render(data, type, row) {
                            return currency(data)
                        },
                    },
                    {
                        data: "total_price_out",
                        render(data, type, row) {
                            return currency(data)
                        }
                    },
                    {
                        data: "sisa_stock",
                        render(data, type, row) {
                            let sisa = row.stock_awal+row.stock_in-row.stock_out
                            // console.log(row.stock_awal+row.stock_in);
                            return parseFloat(sisa).toFixed(2)
                        }
                    },
                    {
                        data: "total_price",
                        render(data, type, row) {
                            let sisa = row.stock_awal+row.stock_in-row.stock_out
                            let price = (parseFloat(sisa).toFixed(2)!="0.00")?row.total_price_sisa / parseFloat(sisa).toFixed(2):0
                            return currency(price)
                        }
                    },
                    {
                        data: "total_price_sisa",
                        render(data, type, row) {
                            let sisa = row.stock_awal+row.stock_in-row.stock_out
                            let totalPrice = (parseFloat(sisa).toFixed(2)!="0.00")?data:0
                            return currency(totalPrice)
                        }
                    },
                    {
                        data: null,
                        class: "text-center",
                        orderable: false,
                        searchable: false,
                        render(data, type, row) {
                            var html = `<button class="btn btn-warning btn-sm btn-detail" title="detail"><i class="fa fa-list"></i></button>`
                            return html
                        }
                    }
                    ]
                })

            },
            onApply() {
                if (vm.param.outlet.length <= 0) {
                    return Alert.warning("Oops...", "Pilih Outlet Dahulu")
                }
                // var outlet = this.param.outlet.join('","');
                var param = {}
                param.startDate = vm.param.startDate
                param.endDate = vm.param.endDate
                param.outlet = vm.param.outlet
                param.adminFkid = "<?= $this->session->userdata('admin_id') ?>"
                this.datatable(param)

                // $.ajax({
                //     type: "POST",
                //     url: "<?= base_url('finance/report/stock_card/get_data') ?>",
                //     data: param,
                //     beforeSend:function(){
                //         loading.show()
                //     },
                //     success: function (res) {
                //         loading.hide();
                //         vm.dataProduk = res;

                //     },
                //     error(err){
                //         loading.hide()
                //         Alert.error("error","Gagal Memuat Data")
                //         console.log(err);
                //     }
                // });
            },
            currency(key) {
                return currency(key)
            },
            datatableDetail(param){
                var tableDetail = $("#table-detail").DataTable({
                    ajax: {
                        url: '<?= base_url() ?>finance/report/stock_card/data_detail_v4',
                        type: "POST",
                        data: param
                    },
                    destroy: true,
                    serverSide: false,
                    ordering: false,    
                    columns: [{
                        data: null,
                        class: "text-center",
                        orderable: false,
                        searchable: false,
                    },
                    {
                        data: "tgl_in",
                    },
                    {
                        data: "transaksi_in",
                        render(data, type, row) {
                            var html = `<span>` + data + `</span>`
                            return html
                        },
                    },
                    {
                        data: "stock_in",
                        render(data, type, row) {
                            let result ="-"
                            data!=0?result=number(parseFloat(data)):''
                            return result
                        },
                    },
                    {
                        data: "price_in",
                        render(data, type, row) {
                            let result ="-"
                            data!=0?result=currency(data):''
                            return result
                        },
                    },
                    {
                        data: "tgl_out",
                        render(data, type, row) {
                            return data
                        },
                    },
                    {
                        data: "transaksi_out",
                        render(data, type, row) {
                            let result ="-"
                            data!=0?result=data:''
                            return result
                        },
                    },
                    {
                        data: "stock_out",
                        render(data, type, row) {
                            let result ="-"
                            data!=0?result=number(parseFloat(data)):''
                            return result
                        },
                    },
                    {
                        data: "price_out",
                        render(data, type, row) {
                            let result ="-"
                            data!=0?result=currency(data):''
                            return result
                        }
                    },
                    // {
                    //     data: "qty_sisa",
                    //     render(data, type, row) {
                    //         return data
                    //     }
                    // },
                    // {
                    //     data: "price_sisa",
                    //     render(data, type, row) {
                    //          return currency(data)
                    //     }
                    // },
                    // {
                    //     data: "total_sisa",
                    //     render(data, type, row) {
                    //         return currency(data)
                    //     }
                    // }
                    ]
                })
            },
            tableDetail(param){
                $.ajax({
                    type: "post",
                    url: "<?= base_url('finance/report/stock_card/data_detail_v4') ?>",
                    data: param,
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                        vm.dataInTable=[]
                    },
                    success: function (res) {
                        loading.hide();
                        vm.dataInTable = res
                    },
                    error(error){
                        loading.hide();
                        Alert.error('error','gagal menampilkan data')
                        console.log(error);
                    }
                });
            },
            onExport(){
                // validate
                if (this.param.outlet == "") {
                    return Alert.warning("Warning","Outlet belum dipilih");
                }
                let url = "<?=base_url('finance/report/stock_card/export_data?')?>startDate="+this.param.startDate+"&endDate="+this.param.endDate+"&outlet="+this.param.outlet
                window.open(url)
            },
            formatNumber(param){
                return number(param)
            },
            number(val){
                return parseFloat(val).toFixed(2)
            }

        },
        mounted() {
            setTimeout(() => {
                vm.param.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                vm.param.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
            }, 300);

        },
    })

    $(function () {
        $('#btn-date').on('apply.daterangepicker', function (ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY") + ' - ' + val.endDate.format("D MMMM YYYY"));
            vm.param.startDate = val.startDate.valueOf()
            vm.param.endDate = val.endDate.valueOf()
        });

        $("#stockCard-table tbody").on('click', '.btn-detail', function () {
            $("#modalDetail").modal("show");
            var data = $("#stockCard-table").DataTable().row($(this).closest('tr')).data();
            vm.dataSelected = data
            vm.detail.pd_id = data.product_detail_fkid
            let param = {
                startDate:vm.param.startDate,
                endDate:vm.param.endDate,
                outlet:vm.param.outlet,
                pd_id:data.product_detail_fkid
            }
            vm.tableDetail(param) 
        })
    });
</script>