<style type="text/css">
.finance-report{
    margin-top: 10px;
    color:#fff;
}
.finance-report:hover{
    color: antiquewhite;
}
.content-report:hover{
    background-color: #2d2d2d;
    border-radius: 10px;
}
</style>

<script type="text/javascript">
//hidden breadcrumb
$('.breadcrumb').hide();

function UnderDevelopment() {
	Swal.fire ( "Oops" ,  "Fitur sedang dalam pengembangan!" ,  "warning" )
}

</script>


<div class="container-fluid" id="main">
	<div class="row">
		<div class="col-md-12 col-sm-12 col-xm-12">
			<ul class="nav nav-tabs tab-uniq" >
				<li v-for="(data,idx) in masterMenu" v-if="data.isLock==false" :class="[idx == 0 ? 'active':'']"><a data-toggle="tab" :href="'#'+data.url" style="color: #fff;">{{data.name}}</a></li>		
			</ul>			
		</div>
	</div>
	<div class="tab-content">
       
        
		<div v-for="(data,idx) in masterMenu" :id="data.url" :class="[idx==0?'tab-pane fade in active':'tab-pane fade']">
			<div class="content-uniq">
				<section>
                    <div class="row">
                        <div class="col-sm-6 col-md-6  content-report" v-for="sub in data.sub" v-if="sub.isLock==false" style="height: 100px; margin-">
                            <a :href="'<?=site_url()?>'+sub.url">
                                <div class="media finance-report" style="margin-top: 20px;margin-left:10px">
                                    <div class="media-left">
                                            <i :class="[sub.icon,'fa-4x']"></i>
                                    </div>
                                    <div class="media-body">
                                        <h4 class="media-heading">{{sub.name}}</h4>
                                        <p style="size:3vw">{{sub.desc}}</p>
                                    </div>
                                </div>
                            </a>
                        </div>   
                    </div>
				</section>			
			</div>
		</div>
	</div>
</div>


<script type="text/javascript">
	var vm = new Vue({
		el: "#main",
		data:{
			masterMenu:[
				{
					name:'Sekilas Keuangan',
					url:'menu1',
					icon:'',
					isLock:false,
					sub:[
						{
							name:'Neraca',
							url:'finance/report/neraca',
							icon:'fa fa-line-chart',
							isLock:false,
                            desc:'Menampilan apa yang anda miliki (aset), apa yang anda hutang (liabilitas), dan apa yang anda sudah investasikan pada perusahaan anda (ekuitas).'
						},
						{
							name:'Laba Rugi',
							url:'finance/report/laba_rugi',
							icon:' fa fa-bar-chart',
							isLock:false,
                            desc:'Menampilkan setiap tipe transaksi dan jumlah total untuk pendapatan dan pengeluaran anda.'
						},
						{
							name:'Arus Kas',
							url:'',
							icon:'fa fa-pie-chart',
							isLock:true,
                            desc:'Laporan ini mengukur kas yang telah dihasilkan atau digunakan oleh suatu perusahaan dan menunjukkan detail pergerakannya dalam suatu periode.'
						},
						{
							name:'Perubahan Modal',
							url:'',
							icon:'glyphicon-calendar',
							isLock:true,
                            desc:'Menampilkan perubahan atau pergerakan dalam ekuitas pemilik yang terjadi dalam periode tertentu.'
						},
						{
							name:'Buku Besar',
							url:'finance/report/buku_besar',
							icon:'fa fa-university',
							isLock:false,
                            desc:'Laporan ini menampilkan semua transaksi yang telah dilakukan untuk suatu periode. Laporan ini bermanfaat jika Anda memerlukan daftar kronologis untuk semua transaksi yang telah dilakukan oleh perusahaan Anda.'
						},
						{
							name:'Jurnal',
							url:'finance/report/jurnal_umum',
							icon:'fa fa-outdent',
							isLock:false,
                            desc:'Daftar semua jurnal per transaksi yang terjadi dalam periode waktu. Hal ini berguna untuk melacak di mana transaksi Anda masuk ke masing-masing rekening'
						},
						{
							name:'Trial Balance',
							url:'finance/report/trial_balance',
							icon:'fa fa-exchange',
							isLock:false,
                            desc:'Menampilkan saldo dari setiap akun, termasuk saldo awal, pergerakan, dan saldo akhir dari periode yang ditentukan.'
						},
						{
							name:'Ringkasan Bisnis',
							url:'',
							icon:'glyphicon-calendar',
							isLock:true,
                            desc:'Laporan Ringkasan Bisnis Menampilkan ringkasan dari laporan keuangan standar beserta wawasannya.'
						},
					]
				},
				{
					name:'Penjualan',
					url:'menu2',
					icon:'',
					isLock:true,
					sub:[
						{
							name:'Daftar Penjualan',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Laporan Piutang Pelanggan',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Laporan Pengiriman Plenjualan',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Penjualan Per Pelanggan',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
					]
				},
				{
					name:'Pembelian',
					url:'menu3',
					icon:'',
					isLock:true,
					sub:[
						{
							name:'Daftar Pembelian',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Laporan Hutang Supplier',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Rincian Pengeluaran',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Laporan Pengiriman',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Pembelian per Supplier',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
						{
							name:'Daftar Pengeluaran',
							url:'',
							icon:'glyphicon-calendar',
                            desc:''
						},
					]
				},
				{
					name:'Produk',
					url:'menu4',
					icon:'',
					isLock:false,
					sub:[
						{
							name:'Kartu Persediaan',
							url:'finance/report/stock_card',
							icon:'fa fa-icon-archive',
							desc:'Ringkasan laporan persediaan produk masuk dan produk keluar',
							isLock:false,
						}
					]
				},
				{
					name:'Aset',
					url:'menu5',
					icon:'',
					isLock:true,
					sub:[{
						name:'',
						url:'',
						icon:''
					}]
				},
				{
					name:'Bank',
					url:'menu6',
					icon:'',
					isLock:true,
					sub:[{
						name:'',
						url:'',
						icon:''
					}]
				},
				{
					name:'Pajak',
					url:'menu7',
					icon:'',
					isLock:true,
					sub:[{
						name:'',
						url:'',
						icon:''
					}]
				},
			]
		}
	})
</script>