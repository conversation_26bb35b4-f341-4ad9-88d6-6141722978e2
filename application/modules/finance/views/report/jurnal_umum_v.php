<style>
	div.dt-buttons {
		float: right;
		margin-left:10px;
	}
	.line-input {
	  background: transparent;
	  border: none;
	  border-bottom: 1px solid #9c9c9c;
	  -webkit-box-shadow: none;
	  box-shadow: none;
	  border-radius: 0;
	  color: #fff;
	  height:auto;
	  font-size:20px;
	}

	.line-input:focus {
	  -webkit-box-shadow: none;
	  box-shadow: none;
	}

	/* Styles for wrapping the search box */

.main {
    width: 50%;
    margin: 50px auto;
}

/* Bootstrap 3 text input with search icon */

.has-search .form-control-feedback {
    right: initial;
    left: 0;
    color: #ccc;
}

.has-search .form-control {
    padding-right: 12px;
    padding-left: 34px;
}

.popover-title {
    color: black;
    font-size: 12px;
}
.popover-content {
    color: black;
    font-size: 10px;
    padding: 5px;
}

</style>
<br>
<div id="app">
	<div class="container-fluid">
		<div class="row">    
	        <div style="margin-bottom: 5px"> 
	        	<div class="col-sm-6">
	                <div class="label-tabele"><h3 style="display: inline;">Jurnal Umum</h3>
	                </div>
	            </div>
	            <div class="col-sm-6">
	            	<div class="pull-right">
						
						<div class="btn-group">
		                    <button class="btn btn-default btn-block" @click="onExport" type="button">Export</button>                  
		                </div>
	            		<div class="btn-group">
		                    <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de" id="btn-date">
		                        <span> 
		                            <i class="glyphicon glyphicon-calendar"></i> Date
		                        </span>
		                        <i class="caret"></i>
		                    </button>
		                </div>
		                <div class="btn-group">
		                    <select class="form-control btn btn-info" v-model="filterForm.outlet">
		                        <option value="0" selected="">Semua Outlet</option>
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
	        </div> 
	    </div>
	    <div class="content-uniq">
            <div class="row">
                <div  class="col-lg-3 col-xs-6">
                    <!-- small box -->
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h4 class="pull-right">{{total.debit}}</h4>
                            <h5>Total Debit</h5>
                        </div>
                        <div class="icon">
                            <i class="ion ion-stats-bars"></i>
                        </div>
                    </div>
                </div><!-- ./col -->
                <div  class="col-lg-3 col-xs-6">
                    <!-- small box -->
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h4 class="pull-right">{{total.kredit}}</h4>
                            <h5>Total Kredit</h5>
                        </div>
                        <div class="icon">
                            <i class="ion ion-stats-bars"></i>
                        </div>
                    </div>
                </div><!-- ./col -->
            </div>
	    	<div class="row">
	    		<div class="col-sm-12">
	    			<div class="form-group has-feedback has-search">
	    				<span class="glyphicon glyphicon-search form-control-feedback"></span>
	    				<input type="text" name="search" class="line-input form-control" id="search" placeholder="Pencarian Tipe Transaksi" v-model="filterForm.search" title="enter untuk mencari">
	    			</div>
	    		</div>
	    	</div>
	    	<div class="row">
	    		<div class="col-sm-12">     		
	    			<table id="tableProduct" class="table table-striped table-report" cellspacing="0" width="100%">
	    				<thead>
	    					<tr>
	    						<th>Rincian</th>
	    						<th>Debit</th>
	    						<th>Kredit</th>
	    					</tr>
	    				</thead>
	    				<tbody>
	    					<tr v-if="dataTable.length =='0'">
	    						<td colspan="3" class="text-center">Tidak Ada Data yang Tersedia Pada Table Ini</td>
	    					</tr>
	    					<template v-else v-for="data,idx in dataTable">
	    						<tr >
	    							<td colspan="3">
                                        <b>{{data.trans_name}}</b> {{data.outlet}} -> {{data.display_nota}} {{data.dateTime}} 
                                        <i tabindex="0" class="glyphicon glyphicon-info-sign" :id="'pop'+idx" role="button" data-toggle="popover" data-trigger="focus" title="Detail Jurnal"></i>
                                        <!-- <a class='info-icon' data-toggle="tooltip" data-placement="right">
                                            <i class='glyphicon glyphicon-info-sign'></i>
                                        </a> -->
									    <button v-show="editable"class="btn btn-sm btn-warning btn-edit-jurnal" @click="onEdit(idx)"><i class="fa fa-edit"></i></button>
								</td>
	    						</tr>
	    						<tr v-for="detail in data.detail">
	    							<td>
                                        ({{detail.account_code}}) - {{detail.account_name}} <br>
                                        <small :title="detail.description">{{parseString(detail.description,55)}}</small>
                                    </td>
	    							<td v-if="detail.type == 'D'">{{currency(parseFloat(detail.nominal))}}</td>
	    							<td v-else>{{currency(0)}}</td>
	    							<td v-if="detail.type == 'K'">{{currency(parseFloat(detail.nominal))}}</td>
	    							<td v-else>{{currency(0)}}</td>
	    						</tr>
	    						<tr >
	    							<td class="pull-right">Total :</td>
	    							<td>{{currency(parseFloat(data.total_d))}}</td>
	    							<td>{{currency(parseFloat(data.total_k))}}</td>
	    						</tr>
	    					</template>
	    					
	    				</tbody>
	    			</table>
	    		</div>
	    	</div>
	    </div>	    
	</div>

	<!-- modal form jurnal -->
	<div class="modal fade" id="modalJurnal" tabindex="-1" role="dialog" aria-labelledby="modalJurnal" data-keyboard="false" data-backdrop="static">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content" style="background: #27292a;">
				<div class="modal-header">
					<h4 class="modal-title" id="exampleModalp">Form Jurnal Input</h4>
				</div>
				<div class="modal-body">
					<div class="row">
						<!-- <div class="col-sm-6">
							<div class="form-group">
								<label for="outlet">Outlet*</label><br>
								<select class="form-control" id="input-outlet" v-model="form.outlet" v-select="form.outlet" id="outlet-id" multiple>
									<?php foreach ($form_select_outlet as $a): ?>                   
										<option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
									<?php endforeach ?>                
								</select>
							</div>
						</div>
						<div class="col-sm-6">
							<div class="form-group">
								<label for="tgl">Tanggal*</label>
								<div class="input-group date">
									<input type="text" class="form-control" id="tgl" name="tgl" v-model="form.date" v-datepicker="form.date" placeholder="Tanggal Jurnal" autocomplete="false"><span class="input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
								</div>
							</div>
						</div> -->
					</div>
					<div class="row">
						<div class="col-sm-12">
							<table class="table table-striped table-report" style="margin-bottom: 5px;">
								<thead>
									<tr>
										<th>Akun</th>
										<th>Debit</th>
										<th>Kredit</th>
										<th>Keterangan</th>
										<th>#</th>
									</tr>
								</thead>
								<tbody>
									<tr v-if="form.detail.length > 0" v-for="(i,idx) in form.detail">
										<td>
											<select :class="['form-control btn-sm btn btn-info selectAkun selectAkun']+idx" v-model="form.detail[idx].akun" v-select="form.detail[idx].akun" id="akunId" width="100%">
												<?php foreach ($account as $a): ?>                   
													<option value="<?=$a->code.";".htmlentities($a->name)?>"><?=$a->code."-".htmlentities($a->name)?></option>
												<?php endforeach ?>                
											</select>
										</td>
										<td>
											<input type="text" :class="['form-control text-right form-dark', 'input-sm']" v-model="form.detail[idx].debit" v-money="form.detail[idx].debit" placeholder="" required onfocus="this.select()" :disabled="clearFormat(form.detail[idx].kredit) > 0"/>
										</td>
										<td>
											<input type="text" :class="['form-control text-right form-dark', 'input-sm']" v-model="form.detail[idx].kredit" v-money="form.detail[idx].kredit" placeholder="" required onfocus="this.select()" :disabled="clearFormat(form.detail[idx].debit) > 0"/>
										</td>
										<td>
											<input type="text" :class="['form-control text-right form-dark', 'input-sm']" v-model="form.detail[idx].keterangan" placeholder="" required/>
										</td>
										<td>
											<button type="button" class="btn btn-sm btn-danger" @click="onDelete(idx)"><i class="fa fa-trash"></i></button>
										</td>
									</tr>
								</tbody>
								<thead>
									<tr>
										<th class="text-right">Total</th>
										<th class="text-right">{{currency(totalInput.debit)}}</th>
										<th class="text-right">{{currency(totalInput.kredit)}}</th>
										<th></th>
										<th></th>
									</tr>
								</thead>
							</table>
							<button type="button" class="btn btn-primary pull-right" @click="onAddAkun()"><i class="fa fa-plus"></i></button>
						</div>
					</div>
					<br> 
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Close</button>
						<button type="button" class="btn btn-primary" @click="validate()" id="save">Simpan</button>
					</div>
					<div style="clear: both;"></div>
				</div><!-- /.modal-body -->
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div>    
</div>

<script type="text/javascript">
    var vm = new Vue({    
        el: "#app",
        data:{
			editable:"<?=$detail_access['edit']?>",
            dataTable:[],
            no:1,
            filterForm:{
                startDate:'',
                endDate:'',
                timeZone:'',
                outlet:'0',
                limit:30,
                offset:0,
                search:''
            },
            total:{
                debit:0,
                kredit:0
            },
			form:{
				trans_id:0,
				trans_type:0,
				detail:[]
			}
        },
        methods:{
        	currency(val){
                return currency(val)
        	},
            onApply(){
                if (vm.filterForm.outlet == '0') {
                    Alert.confirm("Memilih Semua Outlet","Proses Ini Mungkin Membutuhkan Sedikit Waktu, Pastikan Koneksi Anda Dalam Keadaan Baik").then(function(result){
                        if (!result.value) {
                            return;
                        }
                        vm.getData()
                    })
                }else{
                    vm.getData()
                }
            },
            async getData(){
            	var data = {};
                for (var key in vm.filterForm) {
                    if (vm.filterForm.hasOwnProperty(key)) {
                        data[key] = vm.filterForm[key];
                    }
                }
				// return console.log(data);
            	$.ajax({
            		url: '<?=site_url()?>finance/report/jurnal_umum/jurnal_umum',
            		type: 'POST',
            		data: {param:data},
            		beforeSend(){
            			loading_show();
                        vm.data=[]
            		},
            		success(res){
            			loading_hide()
            			vm.dataTable = res
                        let debit = 0;
                        let kredit = 0
                        for(let i in res ){
                            console.log(res[i].info);
                            debit += parseInt(res[i].total_d)
                            kredit += parseInt(res[i].total_k)
                            let list = ''
                            let newInfo =JSON.parse(res[i].info)

                            // List the index and value
                            $.each(newInfo, function (idx, value) { 
                                list += `<a href="#" class="list-group-item">
                                            <b class="list-group-item-heading">#${idx}</b>
                                            <p class="list-group-item-text">${value}</p>
                                        </a>` 
                            });
                            
                            setTimeout(() => {
                                $('#pop'+i).popover({
                                    placement : 'right',
                                    trigger : 'hover',
                                    html:true,
                                    content: function(obj) {
                                        let html = `<div class="list-group">
                                        ${list}
                                    </div>`
                                        return html
                                    }
                                });
                            }, 100);
                        }
                        vm.total.debit = currency(debit)
                        vm.total.kredit = currency(kredit)
                        // setTimeout(() => {
                        //     $('[data-toggle="popover"]').popover({
                        //         placement : 'right',
                        //         trigger : 'hover',
                        //         html:true,
                                // content:`<div class="list-group">
                                //     <a href="#" class="list-group-item">
                                //         <b class="list-group-item-heading">List group item heading</b>
                                //         <p class="list-group-item-text">lor</p>
                                //     </a>
                                //     <a href="#" class="list-group-item">
                                //         <b class="list-group-item-heading">List group item heading</b>
                                //         <p class="list-group-item-text">lor</p>
                                //     </a>
                                // </div>`
                        //     });
                        // }, 200);
            		},
            		error(err){
            			Alert.error("Error","Terjadi Kesalhan...")
            			console.log(err)
            		}
            	})
            	.always(function() {
            		loading_hide()
            	});
            },
            parseString(param,length){
                return parseString(param,length);
            },
			onExport(){

				let outlet = this.filterForm.outlet.toString();
				// return console.log(outlet);
				let url = "<?=base_url('finance/report/jurnal_umum/export?')?>startDate="+this.filterForm.startDate+"&endDate="+this.filterForm.endDate+"&outlet="+outlet
				window.open(url);
			},
			onEdit(idx){
				$("#modalJurnal").modal('show')
				// reset form
				this.form.trans_id =0
				this.form.trans_type=0
				this.form.detail=[]

				this.form.trans_id = vm.dataTable[idx].trans_id
				this.form.trans_type = vm.dataTable[idx].trans_type
				vm.dataTable[idx].detail.map((i)=>{
					vm.form.detail.push({
						akun:i.account_code+";"+i.account_name,
						debit: (i.type=="D")?i.nominal:0,
						kredit:(i.type=="K")?i.nominal:0,
						keterangan:i.description
					})
				})

				setTimeout(() => {
					$('.selectAkun').selectpicker('refresh');
				}, 500);
			},
			validate(){
				let validate = true;
				if (this.totalInput.debit != this.totalInput.kredit) {
					Alert.warning('Oops','Jumlah debit tidak sama dengan jumlah kredit')
					validate =false
				}

				this.form.detail.find((e)=>{
					if (e.akun=="") {
						Alert.warning('Oops','akun tidak boleh kosong');
						validate = false
					}
					if (clearFormat(e.debit) < 0 || clearFormat(e.kredit) < 0) {
						Alert.warning('Oops','Nominal debit kredit tidak boleh minus (-)');
						validate = false 
					}
				})

				if (validate) {
					this.onSubmit()
				}
			},
			onSubmit (){
				this.form.detail.map((e,idx)=>{
					this.form.detail[idx].debit =clearFormat(e.debit)
					this.form.detail[idx].kredit =clearFormat(e.kredit)
				})
				$.ajax({
					type: "POST",
					url: "<?=base_url('finance/report/jurnal_umum/update_jurnal')?>",
					data: vm.form,
					dataType: "json",
					beforeSend(){
						loading.hide()
					},
					success(res) {
						loading.hide()
						Alert.success('Success','data berhasil disimpan')
						$("#modalJurnal").modal('hide')
						vm.getData()
					},
					error(err){
						loading.hide();
						Alert.error('Error','data gagal disimpan')
						console.log(err);
					}
				});
			},
			onDelete(idx){
				this.form.detail.splice(idx,1);
			},
			onAddAkun(){
				this.form.detail.push({
					akun:'',
					debit:0,
					kredit:0,
					keterangan:""
				})
				setTimeout(() => {
					$('.selectAkun').selectpicker({
                        title:"Pilih Akun",
                        // style: 'btn btn-sm btn-info',
                        selectedTextFormat:"count >3",
                        width:"100%",
                        liveSearch:true,
                        actionsBox:true,
                        allowClear: true,
                    });
				}, 500);
				
			},
			clearFormat(param){
                return clearFormat(param)
            }
        },
		computed:{
            totalInput(){
                let totalD = 0;
                let totalK = 0;
                this.form.detail.map((el)=>{
                    totalD +=clearFormat(el.debit);
                    totalK +=clearFormat(el.kredit);
                });
                let result ={
                    debit : totalD,
                    kredit : totalK, 
                }

                return result
            }
        },
        mounted(){
            $(function() {
                setTimeout(function() {
                    vm.filterForm.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                    vm.filterForm.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
                    vm.filterForm.timeZone = timezone();
                }, 100);
                
            });
        }
    })

$(function() {
    $('#btn-date').on('apply.daterangepicker', function(ev, val) {
        $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
        vm.filterForm.startDate = val.startDate.valueOf()
        vm.filterForm.endDate = val.endDate.valueOf()
    });

    let input = document.getElementById("search");
    input.addEventListener("keydown", function(event) {
      if (event.keyCode === 13) {
        event.preventDefault();
        vm.getData()
      }
    });

	// var checkForNewDiv = function() {
		// var lastDiv = document.querySelector("#tableProduct tbody tr:last-child");
		// console.log(lastDiv);
	// 	var lastDivOffset = lastDiv.offsetTop + lastDiv.clientHeight;
	// 	var pageOffset = window.pageYOffset + window.innerHeight;

	// 	console.log(pageOffset);

	// 	if(pageOffset > lastDivOffset - 100) {
	// 		console.log("add content");
	// 	}
	// };

	// $(window).on("scroll.once", function () {
        // console.log("scrlTp="+$(window).scrollTop());
        // console.log(Math.round($(window).scrollTop()));
        // console.log($(window).scrollTop());
        // console.info($(document).height() - $(window).height());

        // if (($(window).scrollTop()+0.01) >= $(document).height() - $(window).height() || lastDiv) {
            // vm.param.ofset = vm.param.ofset + vm.param.limit
            // if (!vm.theLast) {
                // vm.getUsulan()
            // }
		// 	console.log(lastDiv);
		// 	console.log("scrlTp="+$(window).scrollTop());
		// 	console.info($(document).height() - $(window).height());
		// 	console.log("add content");
        // }
    // });
});
</script>