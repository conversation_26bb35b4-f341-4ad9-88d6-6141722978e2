<style type="text/css">
    div.dt-buttons {
        float: right;
        margin-left:10px;
    }
    .table-hover tbody tr:hover td, .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }
    table .collapse.in {
        display:table-row;
    }
    .hiddenRow {
        padding: 0 !important;
    },
    .modal{
    display: block !important;
    }
    .modal-dialog{
        overflow-y: initial !important
    }
    .modal-body{
    height: 80vh;
    overflow-y: auto;
    }
</style>
<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-book"></i> Buku Besar  </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
	            	<div class="pull-right">
	            		<div class="btn-group">
		                    <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de" id="btn-date">
		                        <span> 
		                            <i class="glyphicon glyphicon-calendar"></i> Date
		                        </span>
		                        <i class="caret"></i>
		                    </button>
		                </div>
		                <div class="btn-group">
		                    <select class="form-control btn btn-info outletSelect" multiple v-model="param.outlet">
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
            </div>
	    	<div class="row">
                <div class="col-sm-12 table-responsive">
	    			<table class="table table-report table-hover" id="bukuBesarTable" width="100%">
	    				<thead>
	    					<tr>
	    						<th style="width: 20%;">Nama Akun/Tanggal</th>
	    						<th style="width: 20%;">Saldo Awal</th>
	    						<th style="width: 15%;">Debit</th>
	    						<th style="width: 20%;">Kredit</th>
	    						<th style="width: 20%;">Saldo Akhir</th>
	    						<th style="width: 5%;">#</th>
	    					</tr>
	    				</thead>
                        <tbody>
                            <tr class="text-center">
                                <td colspan="6" >Plih Outlet dan Tanggal Untuk Menampilkan Data</td>
                            </tr>
                        </tbody>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>

    <!-- modal detail -->
    <div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document" style="width: 80%">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title modal-fontcik">Detail</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <table class="table table-striped table-report" style="margin-bottom: 5px;">
                                <thead>
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Transaksi</th>
                                        <th>Debit</th>
                                        <th>Kredit</th>
                                        <th>Saldo Akhir</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="text-right" colspan="4">Saldo Awal : {{recapDetail.saldoAwal}}</td>
                                        <td></td>
                                    </tr>
                                    <tr v-for="(i,idx) in dataDetail.detail">
                                       <td>{{i.date}}</td>
                                       <td>{{i.trans_type}}</td>
                                       <td>{{currency(i.total_debit)}}</td>
                                       <td>{{currency(i.total_kredit)}}</td>
                                       <td>{{currency(i.saldo)}}</td>
                                    </tr>
                                    <tr>
                                        <th colspan="2" class="text-right">Total</th>
                                        <th>{{recapDetail.debit}}</th>
                                        <th>{{recapDetail.kredit}}</th>
                                        <th>{{recapDetail.saldoAkhir}}</th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div><!-- /.modal-body -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Close</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>    
</div>

<script>
    var vm = new Vue({
        el : "#app",
        data:{
            param:{
                startDate:'',
                endDate:'',
                outlet:[]
            },
            recapDetail:{
                saldoAwal:0,
                debit:0,
                kredit:0,
                saldoAkhir:0,
            },
            dataBuku:[],
            dataDetail:[]
        },
        methods: {
            onApply(){
                if (vm.param.outlet.length <=0) {
                    return Alert.warning("Oops...","Pilih Outlet Dahulu")
                }
                var outlet = this.param.outlet.join('","');
                var param = {}
                param.startDate = vm.param.startDate
                param.endDate = vm.param.endDate
                param.outlet = vm.param.outlet
                param.adminFkid = "<?=$this->session->userdata('admin_id')?>"
                $.ajax({
                    type: "POST",
                    url: "<?=base_url('finance/report/buku_besar/get_buku_besar')?>",
                    data: param,
                    beforeSend:function(){
                        loading.show()
                        vm.dataDetail=[]
                    },
                    success: function (res) {
                        loading.hide();
                        
                        var no = 0
                        // vm.dataBuku = res.Data\
                        $("#bukuBesarTable tbody").html("")
                        $.each(res.data, function (idx,val) { 
                            if (val.debit != 0 || val.kredit != 0 || val.total != 0) {
                                $.each(res.data[idx].detail, function (index,value) { 
                                    var saldo = value.total;
                                    if (value.debit != 0 || value.kredit != 0 || value.total != 0) {
                                        $("#bukuBesarTable>tbody").append(
                                            '<tr id="row'+value.account_id+'" >'+
                                                '<td >'+'('+value.account_code+')'+' '+value.account_name+'</td>'+
                                                '<td>'+currency(parseFloat(value.saldo_awal))+'</td>'+
                                                '<td>'+currency(parseFloat(value.debit))+'</td>'+
                                                '<td>'+currency(parseFloat(value.kredit))+'</td>'+
                                                '<td>'+currency(saldo)+'</td>'+
                                                '<td class="btn-detail" data-code="'+value.account_code+'" data-accountFkid= "'+value.account_id+'" data-target=".row'+value.account_id+'" data-saldo="'+value.saldo_awal+'" data-debit="'+value.debit+'" data-kredit="'+value.kredit+'" data-saldoakhir="'+saldo+'"> <button class="btn btn-primary btn-xs">Detail</button></td>'+
                                            '</tr>'
                                        )
                                    }
                                })                                
                            }                            
                        });
                    },
                    error(err){
                        loading.hide()
                        Alert.error("error","Gagal Memuat Data")
                        console.log(err);
                    }
                });
            },
            currency(val){
                return currency(val);
            }

        },
        mounted() {
            setTimeout(() => {
                vm.param.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                vm.param.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
            }, 300);
           
        },
    })

    $(function () {
        $('#btn-date').on('apply.daterangepicker', function(ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
            vm.param.startDate = val.startDate.valueOf()
            vm.param.endDate = val.endDate.valueOf()
        });

        $("#bukuBesarTable tbody").on('click','.btn-detail',function(){
            var outlet = vm.param.outlet.join("','");
            var param = {}
            param.startDate = vm.param.startDate
            param.endDate = vm.param.endDate
            param.outlet = outlet
            param.adminFkid = "<?=$this->session->userdata('admin_id')?>"
            param.accountCode = $(this).data("code")
            param.account_fkid = $(this).data("accountfkid")
            vm.recapDetail.saldoAwal = currency($(this).data("saldo"))
            vm.recapDetail.debit = currency($(this).data("debit"))
            vm.recapDetail.kredit = currency($(this).data("kredit"))
            vm.recapDetail.saldoAkhir = currency($(this).data("saldoakhir"))

            let saldoAwal = $(this).data("saldo")
            // var el = $(this).parent()
            // var idEl =el.attr("id")
            // var childElement = $("#bukuBesarTable tbody").find('.'+idEl);
            // var thisEl = $(this)
            $.ajax({
                type: "POST",
                url: "<?=base_url('finance/report/buku_besar/get_saldo')?>",
                data: param,
                beforeSend(){
                    loading.show()
                },
                success: function (res) {
                    loading.hide();
                    let saldo = saldoAwal;
                    res.data.detail.map((a,idx)=>{

                        // saldo += saldoAwal;
                        if (a.type_category=="D") {
                            saldo += (a.total_debit - a.total_kredit)
                        }else{
                            saldo += (a.total_kredit - a.total_debit)
                        }
                        res.data.detail[idx].saldo = saldo
                    })
                    vm.dataDetail = res.data
                    $("#modal-detail").modal('show')



                    // childElement.children().children().html("")
                    // var saldoTanggal = 0 
                    // var saldoAwal = 0
                    // var dataSaldo ={
                    //     saldo_debit:0,
                    //     saldo_kredit:0
                    // }

                    // saldo awal
                    // console.log(res.data.saldo);
                    // if (res.data.saldo != null) {
                    //     var dataSaldo = res.data.saldo
                    //     if (dataSaldo.account_type == "D") {
                    //         saldoAwal = parseFloat(dataSaldo.saldo_debit) - parseFloat(dataSaldo.saldo_kredit);
                    //     }else{
                    //         saldoAwal = parseFloat(dataSaldo.saldo_kredit) - parseFloat(dataSaldo.saldo_debit);
                    //     }
                    //      var htmlSaldo = '<tr style="color:#b69f9f">'+
                    //                 '<td style="width: 2%;"></td>'+
                    //                 '<td>'+res.data.saldo.date+'</td>'+
                    //                 '<td style="width: 38%;">'+res.data.saldo.trans_type+'</td>'+
                    //                 '<td style="width: 20%;"></td>'+
                    //                 '<td style="width: 20%;"></td>'+
                    //                 '<td style="width: 20%;">'+currency(saldoAwal)+'</td>'+
                    //             '</tr>'
                    //     childElement.children().children().append(htmlSaldo) 
                    // }

                    // $.each(res.data.detail, function (idx, val) { 
                    //     if (this.trans_type == "Saldo Awal") {
                    //          saldoTanggal=this.total
                    //     }

                    //     if (idx==0) {
                    //         if (this.type_category =="D") {
                    //             saldoTanggal = parseFloat(dataSaldo.saldo_debit)+parseFloat(this.total_debit)-this.total_kredit    
                    //         }else{
                    //             saldoTanggal = parseFloat(dataSaldo.saldo_kredit)+parseFloat(this.total_kredit)-this.total_debit
                    //         }
                    //     }else{
                    //         if (this.type_category =="D") {
                    //             saldoTanggal += parseFloat(this.total_debit)-this.total_kredit    
                    //         }else{
                    //             saldoTanggal += parseFloat(this.total_kredit)-this.total_debit
                    //         }
                    //     }
                        

                    //     var htmlSaldo = '<tr style="color:#b69f9f">'+
                    //             '<td style="width: 2%;"></td>'+
                    //             // '<td>'+moment.unix(this.date/1000).local().format("DD/MM/YYYY hh:mm:ss")+'</td>'+
                    //             '<td>'+this.date+'</td>'+
                    //             '<td style="width: 38%;">'+this.trans_type+'<br><small>'+parseString(this.description)+'</small> </td>'+
                    //             '<td style="width: 20%;"></td>'+
                    //             '<td style="width: 20%;"></td>'+
                    //             '<td style="width: 20%;">'+currency(this.total)+'</td>'+
                    //         '</tr>'
                    //     var htmlTable =  '<tr style="color:#b69f9f">'+
                    //             '<td style="width: 2%;"></td>'+
                    //             // '<td>'+moment.unix(this.date/1000).local().format("DD/MM/YYYY hh:mm:ss")+'</td>'+
                    //             '<td>'+this.date+'</td>'+
                    //             '<td style="width: 38%;">'+this.trans_type+'<br><small>'+parseString(this.description)+'</small></td>'+
                    //             '<td style="width: 20%;">'+currency(parseFloat(this.total_debit))+'</td>'+
                    //             '<td style="width: 20%;">'+currency(parseFloat(this.total_kredit))+'</td>'+
                    //             '<td style="width: 20%;">'+currency(saldoTanggal)+'</td>'+
                    //         '</tr>'
                    //     if (this.trans_type != "Saldo Awal") {
                    //         childElement.children().children().append(htmlTable)
                    //     }else{
                    //           childElement.children().children().append(htmlSaldo)
                    //     }
                    // });                   

                    // cange showing total saldo
                    // setTimeout(() => {
                    //     thisEl.html("") 
                    // }, 100);
                    // thisEl.children().parent().attr("data-toggle","#")
                    // thisEl.children().parent().attr("data-target","#")
                    // thisEl.children().parent().attr("class","#")
                    
                },
                error(err){
                    console.log(err);
                    Alert.error("error","Gagal Menampilkan Data")
                }
            });
        })
    });
</script>