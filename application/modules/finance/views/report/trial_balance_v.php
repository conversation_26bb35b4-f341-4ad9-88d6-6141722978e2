<style lang="">
    .table-hover tbody tr:hover td, .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }
</style>

<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-6">
                    <h4><i class="fa fa-book"></i> Trial Balance  </h4>
                    <!-- <p style="text-">Setting Bank Akun</p>  -->
                </div>
                <div class="col-sm-6">
	            	<div class="pull-right">
	            		<div class="btn-group">
		                    <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de" id="btn-date">
		                        <span> 
		                            <i class="glyphicon glyphicon-calendar"></i> Date
		                        </span>
		                        <i class="caret"></i>
		                    </button>
		                </div>
		                <div class="btn-group">
		                    <select class="form-control btn btn-info outletSelect" multiple v-model="param.outlet">
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
            </div>
	    	<div class="row">
                <div class="col-sm-12 table-responsive">
	    			<table class="table table-report table-hover table-bordered" id="trialBalanceTable" width="100%">
	    				<thead>
	    					<tr>
	    						<th style="width: 30%;vertical-align: middle;" rowspan="2" colspan="2" class="text-center">Daftar Akun</th>
	    						<th style="width: 25%;" colspan="2" class="text-center">Saldo Awal</th>
	    						<th style="width: 20%;" colspan="2" class="text-center">Mutasi</th>
	    						<th style="width: 25%;" colspan="2" class="text-center">Saldo Ahir</th>
	    					</tr>
                            <tr>
                                <th class="text-center">Debit</th>
                                <th class="text-center">Kredit</th>
                                <th class="text-center">Debit</th>
                                <th class="text-center">Kredit</th>
                                <th class="text-center">Debit</th>
                                <th class="text-center">Kredit</th>
                            </tr>
	    				</thead>
                        <tbody>
                            <tr class="text-center" v-if="dataTable.data.length ==0">
                                <td colspan="9" >Plih Outlet dan Tanggal Untuk Menampilkan Data</td>
                            </tr>
                            <template v-else v-for="i,idx in dataTable.data" v-if="i.debit !=0 || i.kredit !=0 || i.total !=0">
                                <tr>
                                    <td colspan="8" class="text-uppercase"><b>{{i.category}}</b></td>
                                </tr>
                                <tr v-for="j in i.detail"  v-if="j.debit !=0 || j.kredit !=0 || j.total !=0">
                                    <td colspan="2">&ensp;&ensp;&ensp;{{j.account_code}} {{j.account_name}}</td> 
                                    <td class="text-right">{{currency(j.saldo_debit)}}</td>
                                    <td class="text-right">{{currency(j.saldo_kredit)}}</td>
                                    <td class="text-right">{{currency(j.debit)}}</td>
                                    <td class="text-right">{{currency(j.kredit)}}</td>
                                    <td class="text-right">{{currency(j.saldoAhirD)}}</td>
                                    <td class="text-right">{{currency(j.saldoAhirK)}}</td>                                    
                                </tr>
                            </template>
                            <tfoot>
                                <tr v-if="dataTable.total.total.k !=0 || dataTable.total.total.d != 0">
                                    <th colspan="2">Total</th>
                                    <th class="text-right">{{currency(dataTable.total.saldo.d)}}</th>
                                    <th class="text-right">{{currency(dataTable.total.saldo.k)}}</th>
                                    <th class="text-right">{{currency(dataTable.total.mutasi.d)}}</th>
                                    <th class="text-right">{{currency(dataTable.total.mutasi.k)}}</th>
                                    <th class="text-right">{{currency(dataTable.total.total.d)}}</th>
                                    <th class="text-right">{{currency(dataTable.total.total.k)}}</th>
                                </tr>
                            </tfoot>
                        </tbody>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>
</div>


<script>
    var vm = new Vue({
        el:"#app",
        data:{
            param:{
                startDate:'',
                endDate:'',
                outlet:[],
                timeZone:''
            },
            dataTable:{
                data:[],
                total : {
                    saldo:{
                        d:0,
                        k:0
                    },
                    mutasi:{
                        d:0,
                        k:0
                    },
                    total:{
                        d:0,
                        k:0
                    }
                }
            },
        },
        methods:{
            onApply(){
                if (this.param.outlet.length == 0) {
                    return Alert.warning('Oops...','Outlet belum dipilih')
                }
                $(function () {
                    $.ajax({
                        type: "post",
                        url: "<?=base_url('finance/report/trial_balance/get_data')?>",
                        data: vm.param,
                        dataType: "json",
                        beforeSend(){
                            loading.show();
                            vm.dataTable.total.saldo.d =0
                            vm.dataTable.total.saldo.k =0
                            vm.dataTable.total.mutasi.k =0
                            vm.dataTable.total.mutasi.d =0
                            vm.dataTable.total.total.d =0
                            vm.dataTable.total.total.k =0
                            vm.dataTable.data=[]
                        },
                        success: function (res) {
                            loading.hide();
                            
                            var salD =0
                            var salK =0
                            for (let i in res) {
                                for (let j in res[i].detail) {
                                    var saldoDebit = parseInt(res[i].detail[j].saldo_debit)+res[i].detail[j].debit-res[i].detail[j].kredit
                                    var saldoKredit = parseInt(res[i].detail[j].saldo_kredit)+res[i].detail[j].kredit-res[i].detail[j].debit

                                    salD += parseInt(res[i].detail[j].saldo_debit)+res[i].detail[j].debit-res[i].detail[j].kredit
                                    salK += parseInt(res[i].detail[j].saldo_debit)+res[i].detail[j].kredit-res[i].detail[j].debit
                                    vm.dataTable.total.saldo.d += parseInt(res[i].detail[j].saldo_debit)
                                    vm.dataTable.total.saldo.k += parseInt(res[i].detail[j].saldo_kredit)
                                    vm.dataTable.total.mutasi.k += parseInt(res[i].detail[j].kredit)
                                    vm.dataTable.total.mutasi.d += parseInt(res[i].detail[j].debit)

                                    switch (true) {
                                        case res[i].detail[j].type_account == 'D' && res[i].detail[j].total <0:
                                            vm.dataTable.total.total.k += res[i].detail[j].total*-1
                                            res[i].detail[j].saldoAhirD = 0
                                            res[i].detail[j].saldoAhirK = res[i].detail[j].total*-1
                                            break;
                                        case res[i].detail[j].type_account == 'D' && res[i].detail[j].total >=0:
                                            vm.dataTable.total.total.d += res[i].detail[j].total
                                            res[i].detail[j].saldoAhirD = res[i].detail[j].total
                                            res[i].detail[j].saldoAhirK = 0
                                        break;
                                        case res[i].detail[j].type_account == 'K' && res[i].detail[j].total < 0:
                                            vm.dataTable.total.total.d += res[i].detail[j].total*-1
                                            res[i].detail[j].saldoAhirD = res[i].detail[j].total*-1
                                            res[i].detail[j].saldoAhirK = 0
                                        break;
                                        case res[i].detail[j].type_account == 'K' && res[i].detail[j].total >= 0:
                                            vm.dataTable.total.total.k += res[i].detail[j].total
                                            res[i].detail[j].saldoAhirD = 0
                                            res[i].detail[j].saldoAhirK = res[i].detail[j].total
                                        break;
                                    }

                               }
                            }
                            vm.dataTable.data=res
                        },
                        error(err){
                            loading.hide();
                            console.log(err);
                            Alert.error("error","Gagal menampilkan data");
                        }
                    });
                });
            },
            currency(val){
                return currency(val)
        	},
        },
        mounted(){
            $(function() {
                setTimeout(function() {
                    vm.param.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                    vm.param.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
                    vm.param.timeZone = timezone();
                }, 100);
                
            });
        }
    })

$(function() {
    $('#btn-date').on('apply.daterangepicker', function(ev, val) {
        $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
        vm.param.startDate = val.startDate.valueOf()
        vm.param.endDate = val.endDate.valueOf()
    });
});
</script>