<style>
    .panel-default {
        border-color: #ff9d0357;
    }

    .panel {
        background-color: #27292a;
    }
    div.dataTables_scrollBody {
        background: #27292a !important;
    }
    .tab-uniq > li.active > a, .tab-uniq > li.active > a:hover, .tab-uniq > li.active > a:focus {
        color: #F2C94C;
        cursor: default;
        border: 1px solid #6aa50d;
        border-bottom-color: transparent;
    }
    .active {
        color: #fff;
    }
     .select2-search { background-color: #2d2d2d; }
    .select2-search input { background-color: #2d2d2d; }
    .select2-results { background-color: #2d2d2d }
    /* .select2-choice { background-color: #2d2d2d !important; } */
    .select2-container--default .select2-selection--single .select2-selection__rendered { color: #fff; }
    .select2-container--default .select2-selection--single{
        background-color: #2d2d2d;
        color: #fff;
        border-color: #6c757d;
    }
    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #424242;
    }
   

</style>
<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-4">
                    <span><i class="fa fa-gears"></i> Pembelian Asset</span>
                    <a class="btn btn-sm btn-primary" href="<?=base_url()?>finance/assets/purchasing_assets/form_purchase">Tambah <i class="fa fa-plus"></i></a>
                </div>
                <div class="col-sm-8"> 
                    <div class="pull-right">
                        <div class="btn-group pull-right">
                            <button class="btn btn-primary btn-block btn-sm btn-apply" type="button" @click="onApply">Apply</button>                               
                        </div>
                        <div class="btn-group pull-right">
                            <select class="form-control btn btn-info outletSelect" v-model="form.outlet" v-select="form.outlet" id="outlet-id" multiple>
                                <?php foreach ($form_select_outlet as $a): ?>                   
                                    <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                <?php endforeach ?>                
                            </select>                                   
                        </div>
                        <div class="btn-group pull-right">
                            <button type="button" class="btn btn-info daterange btn-sm" id="date-purchase" >
                                <span> 
                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                </span>
                                <i class="caret"></i>
                            </button>
                        </div>
                    </div>             
                </div> 
                
                <div class="col-sm-8">
                    <div class="btn-group pull-right">
		                
		            </div>
                </div>
            </div>
            <hr>
	    	<div class="row">
                <div class="col-sm-12 table-responsive">
	    			<table class="table table-condensed table-report " id="table-purchase" width="100%">
	    				<thead>
	    					<tr>
	    						<th>No</th>
	    						<th>Tanggal</th>
	    						<th>Invoice</th>
	    						<th>Outlet</th>
	    						<th>Suppier</th>
	    						<th>Grand Total</th>
	    						<th>Total Bayar</th>
	    						<th>Retur</th>
	    						<th>Hutang</th>
	    						<th>Piutang</th>
	    						<th>Status</th>
	    						<th>#</th>
	    					</tr>
	    				</thead>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>

    <!-- modal detail purchasing -->
    <div class="modal fade" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modalDetail" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog " role="document" style="width: 95%">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Detail Pembelian</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Invoice</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.invoice}}</span>
                                </div>
                            </div>
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Outlet</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.outlet_name}}</span>
                                </div>
                            </div>
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Shift</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.shift_name}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Supplier</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.supplier_name}}</span>
                                </div>
                            </div>
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Tanggal</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.data_created}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="row">
                        <div class="col-sm-12">
                            <ul class="nav nav-tabs tab-uniq">
                                <li id="tabPurchase" class="active"><a style="color: #fff;padding-top:5px;padding-bottom:5px" data-toggle="tab" href="#purchase">Detail Pembelian <i class="fa fa-shopping-cart"></i></a> </li>
                                <!-- <li id="tabRetur"><a style="color: #fff;" data-toggle="tab" href="#menu1">Detail Pengembalian <i class="fa fa-undo"></i></a> </li> -->
                            </ul>
                        </div>
                    </div>
                    <div class="tab-content row">
                        <div id="purchase" class="tab-pane fade in active">
                            <div class="row container-fluid">
                                <div class="col-sm-12 table-responsive">
                                    <button class="btn btn-sm btn-primary pull-right">Export <i class="fa fa-file-export"></i></button>
                                    <table class="table table-responsive table-report" id="ttble" style="font-size: 12px;">
                                        <thead style="background: black">
                                            <tr>
                                                <th width="3%">No</th>
                                                <th>Nama Barang</th>
                                                <th>Unit</th>
                                                <th>Qty</th>
                                                <th>Harga</th>
                                                <th>Sub Total</th>
                                                <th>Diskon</th>
                                                <th>Pajak</th>
                                                <th>Total</th>
                                                <th>Keterangan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(i,idx) in detail.detailPrc">
                                                <td style="text-align:center;">{{idx+1}}</td>
                                                <td>{{i.product_name}}</td>
                                                <td>{{i.unit_nota}}</td>
                                                <td>{{numberFormat(parseFloat(i.qty_nota))}}</td>
                                                <td>{{numberFormat(i.price_nota)}}</td>
                                                <td>{{numberFormat(i.total)}}</td>
                                                <td>{{i.discount}}</td>
                                                <td>{{i.tax_name}}</td>
                                                <td>{{numberFormat(i.tot_dis)}}</td>
                                                <td>{{i.keterangan}}</td>
                                            </tr>
                                        </tbody>
                                    </table>                            
                                </div>
                            </div>
                            <div class="row container-fluid">
                                <div class="col-sm-5 col-sm-offset-7">
                                    <div class="form-group row mb-1">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Sub Total</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{numberFormat(detail.sub_total)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1" v-if="detail.diskonBaris>0">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Diskon Baris</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{numberFormat(detail.diskonBaris)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1" v-if="detail.disNominal>0">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Diskon Nota</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{numberFormat(detail.disNominal)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1" >
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Pajak</label>
                                        <div class="col-sm-9">
                                            <span v-for="i in detail.pajakBaris"  class="form-control form-dark input-line input-sm text-right">{{i.text}} - {{numberFormat(i.nominal)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Grand Total</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{numberFormat(detail.grand_total)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Pembayaran</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{numberFormat(detail.bayar)}}</span>
                                        </div>
                                    </div>
                                    <template v-if="detail.hutang > 0">
                                        <div class="form-group row mb-1">
                                            <label class="col-sm-3 col-form-label col-form-label-sm text-right">Hutang</label>
                                            <div class="col-sm-9">
                                                <span class="form-control form-dark input-line input-sm text-right">{{numberFormat(detail.hutang)}}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row mb-1">
                                            <label class="col-sm-3 col-form-label col-form-label-sm text-right">Tempo</label>
                                            <div class="col-sm-9">
                                                <span class="form-control form-dark input-line input-sm text-right">{{reverseDate(detail.jatuh_tempo)}}</span>
                                            </div>
                                        </div>
                                    </template>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Close</button>
                    </div>
                    <div style="clear: both;"></div>
                </div><!-- /.modal-body -->
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- modal end -->

    <!-- modal penerimaan pengembalian -->
    <div class="modal fade" id="modalPiutang" tabindex="-1" role="dialog" aria-labelledby="modalPiutang" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Penerimaan Piutang</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group row mb-1">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Total Piutang *
                        </label>
                        <div class="col-sm-9">
                            <h4>{{numberFormat(formPiutang.data.totalSaldo)}}</h4>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="nominal" class="col-sm-3 col-form-label col-form-label-sm text-right">Nominal *</label>
                        <div class="col-sm-9">
                            <input type="text" id="nominal" :class="['form-control form-dark text-right', 'input-sm', formPiutang.inputs.nominal.error ? 'is-invalid' : '']" name="payment" v-model="formPiutang.inputs.nominal.value" v-money="formPiutang.inputs.nominal.value" required />
                            <div class="invalid-feedback">{{ formPiutang.inputs.nominal.error }}</div>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="bank" class="col-sm-3 col-form-label col-form-label-sm text-right"></label>
                        <div class="col-sm-9">
                            <select id="bank" :class="['form-control form-dark select2select']" name="payment_media_bank" v-model="formPiutang.inputs.bank.value" v-select="formPiutang.inputs.bank.value" style="width:100%" data-placeholder="-- Pilih Bank --">
                                <option value="cash" selected>Cash</option>
                                <?php foreach ($form_select_bank as $a) : ?>
                                    <option value="<?= $a->bank_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                </div><!-- /.modal-body -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" @click="insertPiutang()">Simpan</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- modal end -->
</div>


<script>
    $(function () {
        $('#date-purchase').on('apply.daterangepicker', function(ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
            purchaseApp.form.startDate = val.startDate.valueOf()
            purchaseApp.form.endDate = val.endDate.valueOf()
        });

        $("#table-purchase").on('click', '.btn-detail', function(e) {  
            var data = $("#table-purchase").DataTable().row($(this).closest('tr')).data();
            purchaseApp.detail=data;
            $.ajax({
                type: "get",
                url: "<?=base_url()?>purchasing/purchasing_product/detail",
                data: {id:data.purchase_id},
                dataType: "json",
                beforeSend:function(){
                    loading.show();
                },
                success: function (res) {
                    loading.hide()
                    purchaseApp.detail.detailPrc = res
                    $("#modalDetail").modal("show")
                    purchaseApp.localtime()
                    purchaseApp.diskonBaris()
                    purchaseApp.pajakBaris()
                },
                error(err){
                    loading.hide();
                    Alert.error();
                    console.log(err);
                }
            });
        })

         $("#table-purchase").on('click', '.btn-piutang', function(e) {  
            var data = $("#table-purchase").DataTable().row($(this).closest('tr')).data();
            // console.log(data);
            $('#modalPiutang').modal('show')
            var saldo = data.total_retur-data.hutang-data.refund
            purchaseApp.formPiutang.data.totalSaldo = saldo
            purchaseApp.formPiutang.inputs.purchase_id.value = data.purchase_id
            purchaseApp.formPiutang.inputs.nominal.value = 0
            purchaseApp.formPiutang.inputs.bank.value = 'cash'
            setTimeout(() => {
                $("#bank").select2();
            }, 200);

         })
    });

    function redirect(type,param){
        location.href = "<?=base_url()?>purchasing/purchasing_product/"+type+"/"+param
    }

    var purchaseApp = new Vue({
        el : "#app",
        data : {
            form:{
                startDate : '',
                endDate : '',
                outlet : []
            },
            formPiutang:{
                data:{
                    totalSaldo:'',
                },
                inputs:{
                    purchase_id:{
                        value:'',
                        error:'',
                        allowClear: true,
                        required: true,
                    },
                    nominal:{
                        value:'',
                        error:'',
                        allowClear: true,
                        required: true,
                    },
                    bank:{
                        value:'cash',
                        error:'',
                        allowClear: true,
                        required: true,
                    }
                }
            },
            detail:""
        },
        methods: {
            datatable(){
                $(function () {
                    $("#table-purchase").DataTable({
                        ajax: {
                            url: '<?=base_url()?>finance/assets/purchasing_assets/datatable',
                            type: "POST",
                            data:{
                                startDate : purchaseApp.form.startDate,
                                endDate : purchaseApp.form.endDate,
                                outlet : purchaseApp.form.outlet.toString(),
                            }
                        },
                        destroy : true,
                        serverSide: true,
                        scrollY: '55vh',
                        scrollCollapse: true,
                        // paging: false,
                        columns: [{
                                data: null,
                                class: "text-center",
                                orderable: false,
                                searchable: false,
                            },
                            {
                                data: "data_created",
                                render(data,type,row){
                                    return moment.unix(data/1000).local().format("DD/MM/YYYY")
                                }
                            },
                            {
                                data: "invoice",
                            },
                            {
                                data: "outlet_name",
                            },
                            {
                                data: "supplier_name",
                            },
                            {
                                data: "grand_total",
                                render(data,type,row){
                                    return currency(data)
                                }
                            },
                            {
                                data: "bayar",
                                render(data,type,row){
                                    var bayar = parseInt(row.bayar) + parseInt(row.debt_payment)
                                    return currency(bayar)
                                }
                            },
                            {
                                data: "total_retur",
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    return currency(row.total_retur)
                                }
                            },
                            {
                                data: "hutang", //hutang
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    var total_hutang = data-row.total_retur

                                    if(total_hutang < 0){
                                        total_hutang = 0
                                    }
                                    return currency(total_hutang)
                                    
                                }
                            },
                            {
                                data: "hutang", //piutang
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    var total_hutang = data-row.total_retur

                                    if(total_hutang > 0){
                                        total_hutang = 0
                                    }
                                    return currency((total_hutang*-1)-row.refund)
                                    
                                }
                            },
                            {
                                data: "status_lunas",
                                className: "text-center",
                                render: function(data, type, row) {
                                    var balace = row.hutang-row.total_retur

                                    if (balace<0) {
                                        return '<span class="label label-success">Lunas</span>';
                                    }else{
                                        return '<span class="label label-default">Hutang</span>';
                                    }
                                    
                                }
                            },
                            {
                                data:"purchase_id",
                                class:"text-center",
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    var detail = `<button class="btn btn-primary btn-xs btn-detail" title="detail"><i class="glyphicon glyphicon-th-list"></i></button>`;
                                    return detail;
                                }
                            }
                        ]
                    })
                });
               
            },
            onApply(){
                this.datatable()
            },
            localtime(){
                this.detail.data_created = moment.unix(this.detail.data_created/1000).local().format("DD/MM/YYYY")
            },
            numberFormat(val){
                return number(val)
            },
            diskonBaris(){
                var diskonBaris = 0
                for (const i in this.detail.detailPrc) {
                    var disNominal = 0
                    if (this.detail.detailPrc[i].discount >0) {
                        if (this.detail.detailPrc[i].disc_type == 'percent') {
                            disNominal = (this.detail.detailPrc[i].qty_nota*this.detail.detailPrc[i].price_nota)/100*this.detail.detailPrc[i].discount
                        }else{
                            disNominal = this.detail.detailPrc[i].discount
                        }
                    }
                    this.detail.detailPrc[i].disNominal = disNominal
                    diskonBaris +=disNominal
                }
                this.detail.disBaris = diskonBaris
            },
            diskonNota(){
                var dis = this.detail.discount_total
                var disType = this.detail.discount_total
                var disNominal = 0
                if (disType == 'percent') {
                    disNominal = (this.detail.sub_total*dis)/100
                }else{
                    disNominal = dis
                }
                this.detail.disNominal = disNominal
            },
            pajakBaris(){
                var pajakBaris = 0
                var pjkArry = [];
                for (const i in this.detail.detailPrc) {
                    var pajak = 0
                    this.detail.detailPrc[i].pajakNom = 0;
                    if (this.detail.detailPrc[i].tax >0) {
                        if (this.detail.detailPrc[i].tax_type == 'percentage') {
                            pajak = ((this.detail.detailPrc[i].qty_nota*this.detail.detailPrc[i].price_nota)-this.detail.disBaris)/100*this.detail.detailPrc[i].tax
                        }else{
                            pajak = this.detail.detailPrc[i].tax
                        }
                        this.detail.detailPrc[i].pajakNom = pajak;

                        var tmpPajak = {
                            text : this.detail.detailPrc[i].tax_name,
                            nominal : pajak
                        }
                        pjkArry.push(tmpPajak)
                    }
                    pajakBaris +=pajak
                }
                this.detail.pajak = pajakBaris

                var newPjkArr = [];
                pjkArry.reduce(function(res, value) {
                    if (!res[value.text]) {
                        res[value.text] = { text: value.text, nominal: 0 };
                        newPjkArr.push(res[value.text])
                    }
                    res[value.text].nominal += parseInt(value.nominal);
                    return res;
                }, {});
                // console.log(newPjkArr);
                this.detail.pajakBaris = newPjkArr
            },
            numberFormat(val){
                return number(val)
            },
            reverseDate(param){
                return reverseDate(param)
            },
            insertPiutang(){
                if (!this.validate(this.formPiutang.inputs)) {
					return;
				}
                var data = {};
				for (var key in this.formPiutang.inputs) {
					if (this.formPiutang.inputs.hasOwnProperty(key)) {
						this.formPiutang.inputs[key].error = "";
						data[key] = this.formPiutang.inputs[key].value;
					}
				}
                data.nominal = clearFormat(data.nominal)
                // 
                $.ajax({
                    type: "POST",
                    url: "<?=base_url('purchasing/purchasing_product/insert_piutang')?>",
                    data: data,
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (response) {
                        loading.hide();
                        $('#modalPiutang').modal('hide')
                        Alert.success('Success','Data berhasil disimpan')
                        $("#table-purchase").DataTable().ajax.reload();
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error('Error','Gagal Menyimpan Data')
                    }
                });
            }
        },
        mounted() {
            setTimeout(() => {
                purchaseApp.form.startDate = $('#date-purchase').data('daterangepicker').startDate._d.valueOf()
                purchaseApp.form.endDate = $('#date-purchase').data('daterangepicker').endDate._d.valueOf()
                purchaseApp.datatable()
            }, 300);
        },
    })
</script>