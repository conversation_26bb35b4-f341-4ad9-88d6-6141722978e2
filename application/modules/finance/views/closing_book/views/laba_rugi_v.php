<div class="row" id="laba">
    <div class="col-sm-12">
        <table class="table table-report table-hover" id="labaRugiTable" width="100%">
            <tbody>
                <tr >
                    <th colspan="2">Pendapatan</th>                               
                </tr> 
                <template v-for="(i,idx) in dataTable.data" >
                    <tr v-for="(j,index) in i.detail" v-if="i.code_category == 13 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                        <td >&ensp;&ensp;&ensp;&ensp;&ensp; {{j.account_code}}-{{j.account_name}}</td>
                        <td class="text-right" v-if="j.type_account=='D'">{{curent(j.debit-j.kredit)}}</td>
                        <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                    </tr>
                </template>
                    <tr>
                        <td class="text-bold">&ensp;&ensp;&ensp;Total Pendapatan</td>
                        <td class="text-right text-bold">{{curent(dataTable.total.pendapatan)}}</td>
                    </tr>
                <tr >
                    <th colspan="2">Beban Pokok</th>                               
                </tr> 
                <template v-for="(i,idx) in dataTable.data" >
                    <tr v-for="(j,index) in i.detail" v-if="i.code_category == 16 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                        <td >&ensp;&ensp;&ensp;&ensp;&ensp; {{j.account_code}}-{{j.account_name}}</td>
                        <td class="text-right" v-if="j.type_account=='D'">{{curent(j.debit-j.kredit)}}</td>
                        <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                    </tr>
                </template>
                    <tr>
                        <td class="text-bold">&ensp;&ensp;&ensp; Total Beban</td>
                        <td class="text-right text-bold">{{curent(dataTable.total.beban)}}</td>
                    </tr>
                <tr >
                    <td class="text-bold">&ensp;Laba Kotor</td>  
                    <td class="text-right text-bold"> {{curent(dataTable.total.pendapatan-dataTable.total.beban)}}</td>                    
                </tr> 
                <tr >
                    <th colspan="2">Beban Operasional</th>                               
                </tr> 
                <template v-for="(i,idx) in dataTable.data" >
                    <tr v-for="(j,index) in i.detail" v-if="i.code_category == 15 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                        <td >&ensp;&ensp;&ensp;&ensp;&ensp; {{j.account_code}}-{{j.account_name}}</td>
                        <td class="text-right" v-if="j.type_account=='D'">{{curent(j.debit-j.kredit)}}</td>
                        <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                    </tr>
                </template>
                    <tr>
                        <td class="text-bold">&ensp;&ensp;&ensp;Total dari Beban Operasional</td>
                        <td class="text-right text-bold">{{curent(dataTable.total.bebanOprasional)}}</td>
                    </tr>
                
                <tr >
                    <td class="text-bold">&ensp; Laba Oprasional</td>  
                    <td class="text-right text-bold"> {{curent(dataTable.total.pendapatan-dataTable.total.beban-dataTable.total.bebanOprasional)}}</td>                    
                </tr> 

                <tr >
                    <th colspan="2">Pendapatan (Beban Lain-Lain)</th>                               
                </tr> 
                <tr >
                    <td colspan="2">&ensp;&ensp;&ensp; Pendapatan lain-lain</td>                    
                </tr>
                <template v-for="(i,idx) in dataTable.data" >
                    <tr v-for="(j,index) in i.detail" v-if="i.code_category == 14 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                        <td >&ensp;&ensp;&ensp;&ensp;&ensp; {{j.account_code}}-{{j.account_name}}</td>
                        <td class="text-right" v-if="j.type_account=='D'">{{curent(j.debit-j.kredit)}}</td>
                        <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                    </tr>
                </template>
                    <tr>
                        <td class="text-bold">&ensp;&ensp;&ensp; Total pendapatan Lain-Lain</td>
                        <td class="text-right text-bold">{{curent(dataTable.total.pendapatan2)}}</td>
                    </tr>
                <tr >
                    <td colspan="2">&ensp;&ensp;&ensp; Beban lain-lain</td>                  
                </tr>
                <template v-for="(i,idx) in dataTable.data" >
                    <tr v-for="(j,index) in i.detail" v-if="i.code_category == 17 && (j.debit !=0 || j.kredit != 0 || j.total != 0)">
                        <td >&ensp;&ensp;&ensp;&ensp;&ensp; {{j.account_code}}-{{j.account_name}}</td>
                        <td class="text-right" v-if="j.type_account=='D'">{{curent(j.debit-j.kredit)}}</td>
                        <td class="text-right" v-else>{{curent(j.kredit-j.debit)}}</td>
                    </tr>
                </template>
                    <tr>
                        <td class="text-bold">&ensp;&ensp;&ensp; Total Beban Lain-Lain</td>
                        <td class="text-right text-bold">{{curent(dataTable.total.beban2)}}</td>
                    </tr>
                <tr >
                    <td class="text-bold">&ensp; Total Dari Pendapatan (Beban Lain-Lain)</td>   
                    <td class="text-right">{{curent(dataTable.total.pendapatan2-dataTable.total.beban2)}} </td>                    
                </tr> 
                <tr >
                    <th class="text-bold"> Laba (Rugi)</th>   
                    <th class="text-right text-bold">{{curent(dataTable.total.pendapatan-dataTable.total.beban-dataTable.total.bebanOprasional-dataTable.total.pendapatan2-dataTable.total.beban2)}} </th>                    
                </tr> 
            </tbody>
        </table>	
    </div>
</div>

<script>
     var vmLaba = new Vue({
        el:"#laba",
        data:{
            dataTable:{
                data:"",
                total : {
                    pendapatan:0,
                    beban:0,
                    bebanOprasional:0,
                    pendapatan2:0,
                    beban2:0
                }
            },
        },
        methods:{
            onApply(){
                if (this.param.outlet.length == 0) {
                    return Alert.warning('Oops...','Outlet belum dipilih')
                }
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/report/laba_rugi/get_laba_rugi')?>",
                    data: vm.param,
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide();
                        vm.dataTable.data=res

                            vm.dataTable.total.pendapatan = 0;
                            vm.dataTable.total.beban = 0;
                            vm.dataTable.total.pendapatan2 = 0;
                            vm.dataTable.total.beban2 = 0;
                            vm.dataTable.total.bebanOprasional = 0 ;
                        for (let i in res) {
                            switch (res[i].code_category) {
                                case '13': //pendapatan
                                    vm.dataTable.total.pendapatan += res[i].total
                                    break;
                                case '14': //beban
                                    vm.dataTable.total.beban += res[i].total;
                                    break;
                                case '16': //pendapatan lain lain
                                    vm.dataTable.total.pendapatan2 += res[i].total;
                                    break;
                                case '17':
                                    vm.dataTable.total.beban2 += res[i].total
                                    // console.log(res[i].saldo_debit+res[i].debit);
                                    break;
                                case '15':
                                    vm.dataTable.total.bebanOprasional += res[i].total
                                    break;
                                default:
                                    break;
                            }
                        }
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error("error","Gagal menampilkan data");
                    }
                });
            },
            total(){
                let res = vmLaba.dataTable.data
                vmLaba.dataTable.total.pendapatan = 0;
                vmLaba.dataTable.total.beban = 0;
                vmLaba.dataTable.total.pendapatan2 = 0;
                vmLaba.dataTable.total.beban2 = 0;
                vmLaba.dataTable.total.bebanOprasional = 0 ;
                for (let i in res) {
                    switch (res[i].code_category) {
                        case '13': //pendapatan
                            vmLaba.dataTable.total.pendapatan += res[i].total
                            break;
                        case '16': //beban
                            vmLaba.dataTable.total.beban += res[i].total;
                            break;
                        case '14': //pendapatan lain lain
                            vmLaba.dataTable.total.pendapatan2 += res[i].total;
                            break;
                        case '17':
                            vmLaba.dataTable.total.beban2 += res[i].total
                            // console.log(res[i].saldo_debit+res[i].debit);
                            break;
                        case '15':
                            vmLaba.dataTable.total.bebanOprasional += res[i].total
                            break;
                        default:
                            break;
                    }
                }
            },
            curent(val){
                return currency(val)
        	},
        }
    })
</script>