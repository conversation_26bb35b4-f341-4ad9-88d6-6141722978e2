<style lang="">
    .table-hover tbody tr:hover td, .table-hover tbody tr:hover th {
        background-color: #3a3e40;
    }
</style>

<div id="app-trial">
    <div class="row">
        <div class="col-sm-12 table-responsive">
            <table class="table table-report table-hover table-bordered" id="trialBalanceTable" width="100%">
                <thead>
                    <tr>
                        <th style="width: 30%;vertical-align: middle;" rowspan="2" colspan="2" class="text-center">Daftar Akun</th>
                        <th style="width: 25%;" colspan="2" class="text-center">Sal<PERSON>wal</th>
                        <th style="width: 20%;" colspan="2" class="text-center">Mutasi</th>
                        <th style="width: 25%;" colspan="2" class="text-center">Saldo Ahir</th>
                    </tr>
                    <tr>
                        <th class="text-center">Debit</th>
                        <th class="text-center">Kredit</th>
                        <th class="text-center">Debit</th>
                        <th class="text-center">Kredit</th>
                        <th class="text-center">Debit</th>
                        <th class="text-center">Kredit</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="text-center" v-if="dataTable.data.length ==0">
                        <td colspan="9" >Plih Outlet dan Tanggal Untuk Menampilkan Data</td>
                    </tr>
                    <template v-else v-for="i,idx in dataTable.data" v-if="i.debit !=0 || i.kredit !=0 || i.total !=0">
                        <tr>
                            <td colspan="8" class="text-uppercase"><b>{{i.category}}</b></td>
                        </tr>
                        <tr v-for="j in i.detail"  v-if="j.debit !=0 || j.kredit !=0 || j.total !=0">
                            <td colspan="2">&ensp;&ensp;&ensp;{{j.account_code}} {{j.account_name}}</td> 
                            <td class="text-right">{{currency(j.saldo_debit)}}</td>
                            <td class="text-right">{{currency(j.saldo_kredit)}}</td>
                            <td class="text-right">{{currency(j.debit)}}</td>
                            <td class="text-right">{{currency(j.kredit)}}</td>
                            <template v-if="(j.type_account == 'D' && (j.saldo_debit+j.debit-j.kredit) > 0) || (j.saldo_debit+j.debit-j.kredit) > 0">
                                <td class="text-right">{{currency(parseInt(j.saldo_debit)+parseInt(j.debit)-j.kredit)}}</td>
                                <td class="text-right">{{currency(0)}}</td>
                            </template>
                            <template v-else>
                                <td class="text-right">{{currency(0)}}</td>
                                <td class="text-right">{{currency(parseInt(j.saldo_kredit)+parseInt(j.kredit)-j.debit)}}</td>
                            </template>
                            
                        </tr>
                    </template>
                    <tfoot>
                        <tr v-if="dataTable.total.total.k !=0 || dataTable.total.total.d != 0">
                            <th colspan="2">Total</th>
                            <th class="text-right">{{currency(dataTable.total.saldo.d)}}</th>
                            <th class="text-right">{{currency(dataTable.total.saldo.k)}}</th>
                            <th class="text-right">{{currency(dataTable.total.mutasi.d)}}</th>
                            <th class="text-right">{{currency(dataTable.total.mutasi.k)}}</th>
                            <th class="text-right">{{currency(dataTable.total.total.d)}}</th>
                            <th class="text-right">{{currency(dataTable.total.total.k)}}</th>
                        </tr>
                    </tfoot>
                </tbody>
            </table>			
        </div>
    </div>
</div>

<script>
    var vmTrial = new Vue({
        el:"#app-trial",
        data:{
            param:{
                startDate:"",
                endDate:"",
                outlet:[],
                timeZone:timezone()
            },
            dataTable:{
                data:"",
                total : {
                    saldo:{
                        d:0,
                        k:0
                    },
                    mutasi:{
                        d:0,
                        k:0
                    },
                    total:{
                        d:0,
                        k:0
                    }
                }
            },
        },
        methods:{
            total(){
                let res = this.dataTable.data
                let salD =0
                let salK =0
                for (let i in res) {
                    for (let j in res[i].detail) {
                        salD += parseInt(res[i].detail[j].saldo_debit)+res[i].detail[j].debit-res[i].detail[j].kredit
                        salK += parseInt(res[i].detail[j].saldo_debit)+res[i].detail[j].kredit-res[i].detail[j].debit
                        vmTrial.dataTable.total.saldo.d += parseInt(res[i].detail[j].saldo_debit)
                        vmTrial.dataTable.total.saldo.k += parseInt(res[i].detail[j].saldo_kredit)
                        vmTrial.dataTable.total.mutasi.k += parseInt(res[i].detail[j].kredit)
                        vmTrial.dataTable.total.mutasi.d += parseInt(res[i].detail[j].debit)
                        if ((res[i].detail[j].type_account == 'D' && (res[i].detail[j].saldo_debit+res[i].detail[j].debit-res[i].detail[j].kredit)<0) || res[i].detail[j].saldo_debit+res[i].detail[j].debit-res[i].detail[j].kredit < 0) {
                            vmTrial.dataTable.total.total.d += (res[i].detail[j].saldo_debit+res[i].detail[j].debit-res[i].detail[j].kredit)*-1
                            vmTrial.dataTable.total.total.k += res[i].detail[j].saldo_debit+res[i].detail[j].kredit-res[i].detail[j].debit
                            
                        }
                    }
                }
            },
            currency(val){
                return currency(val)
        	},
        }
    })
</script>