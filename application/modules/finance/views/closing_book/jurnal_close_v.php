<div id="jurnal" style="display:none">
	<div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-12">
                <div class="alert alert-info" role="alert">Periksa kembali data jurnal input tutup buku yang terbentuk pada semua outlet</div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-striped table-report">
                        <thead>
                            <tr>
                                <th>Account</th>
                                <th>Debit</th>
                                <th>Kredit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="(i,idx) in dataVew">
                                <tr>
                                    <td colspan="3" style="background-color:darkcyan">
                                        {{i.outlet}}
                                    </td>
                                </tr>
                                <tr v-for="(j,ndx) in dataVew[idx].detail">
                                    <td>
                                        {{j.account}}<br>
                                        <small>{{j.des}}</small>
                                    </td>
                                    <td>{{currency(j.debit)}}</td>
                                    <td>{{currency(j.kredit)}}</td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">Total</td>
                                    <td>{{currency(i.totalD)}}</td>
                                    <td>{{currency(i.totalK)}}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6 col-sm-offset-6">
                    <button class="btn btn-primary pull-right btn-simpan" @click="onSave">Simpan</button>
                    <button class="btn btn-default pull-right btn-btl" style="margin-right:5px">Batal</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var jurnalApp = new Vue({
        el:"#jurnal",
        data:{
            dataJurnal:[
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "8-81000",
                    "account_name": "Selisih Nilai Persediaan",
                    "type": "K",
                    "nominal": **********,
                    "description": "closing book expenses",
                    "account_category_fkid": "17",
                    "outlet_fkid": "611",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Bhayangkara - Nasional"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "8-80100",
                    "account_name": "Penyesuaian Persediaan",
                    "type": "K",
                    "nominal": -116000,
                    "description": "closing book expenses",
                    "account_category_fkid": "17",
                    "outlet_fkid": "611",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Bhayangkara - Nasional"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": **********,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "611",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Bhayangkara - Nasional"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "4-40000",
                    "account_name": "Pendapatan",
                    "type": "D",
                    "nominal": 6470000,
                    "description": "closing book income",
                    "account_category_fkid": "13",
                    "outlet_fkid": "647",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "boenko Trial"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "5-50000",
                    "account_name": "Beban Pokok Pendapatan",
                    "type": "K",
                    "nominal": ********,
                    "description": "closing book expenses",
                    "account_category_fkid": "15",
                    "outlet_fkid": "647",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "boenko Trial"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "6-60217",
                    "account_name": "Listrik",
                    "type": "K",
                    "nominal": -21000,
                    "description": "closing book expenses",
                    "account_category_fkid": "16",
                    "outlet_fkid": "647",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "boenko Trial"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "8-80999",
                    "account_name": "Beban Lain - lain",
                    "type": "K",
                    "nominal": -10000,
                    "description": "closing book expenses",
                    "account_category_fkid": "17",
                    "outlet_fkid": "647",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "boenko Trial"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10003",
                    "account_name": "Kas di Mesin Kasir",
                    "type": "K",
                    "nominal": -825266.**********,
                    "description": "tax expense",
                    "account_category_fkid": "3",
                    "outlet_fkid": "647",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "boenko Trial"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "K",
                    "nominal": -********.8,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "647",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "boenko Trial"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "648",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Cyber Bull Club"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "642",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Dr Franco"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "6-60100",
                    "account_name": "Biaya Umum & Administratif",
                    "type": "K",
                    "nominal": 152000,
                    "description": "closing book expenses",
                    "account_category_fkid": "16",
                    "outlet_fkid": "628",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Drawing Franco Studio"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "6-60217",
                    "account_name": "Listrik",
                    "type": "K",
                    "nominal": -30000,
                    "description": "closing book expenses",
                    "account_category_fkid": "16",
                    "outlet_fkid": "628",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Drawing Franco Studio"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "8-80100",
                    "account_name": "Penyesuaian Persediaan",
                    "type": "K",
                    "nominal": ********,
                    "description": "closing book expenses",
                    "account_category_fkid": "17",
                    "outlet_fkid": "628",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Drawing Franco Studio"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": ********,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "628",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Drawing Franco Studio"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "614",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Godean - Yogyakarta"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "613",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Jakal - Yogyakarta"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "440",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Outlet Biru"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "8-81000",
                    "account_name": "Selisih Nilai Persediaan",
                    "type": "K",
                    "nominal": 1000,
                    "description": "closing book expenses",
                    "account_category_fkid": "17",
                    "outlet_fkid": "447",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Outlet Merah"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 1000,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "447",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Outlet Merah"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "469",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Outlet Ungu"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "612",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Sagan - Yogyakarta"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "615",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Seturan - Yogyakarta"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "D",
                    "nominal": 0,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "643",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Toko Cantiq Jogja"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "8-81000",
                    "account_name": "Selisih Nilai Persediaan",
                    "type": "K",
                    "nominal": -**********,
                    "description": "closing book expenses",
                    "account_category_fkid": "17",
                    "outlet_fkid": "439",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Toko Oke"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10003",
                    "account_name": "Kas di Mesin Kasir",
                    "type": "K",
                    "nominal": -2558254.**********,
                    "description": "tax expense",
                    "account_category_fkid": "3",
                    "outlet_fkid": "439",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Toko Oke"
                },
                {
                    "trans_type": "21",
                    "trans_id": *************,
                    "account_code": "1-10200",
                    "account_name": "Persediaan Barang",
                    "type": "K",
                    "nominal": -********.8,
                    "description": "Net Profit\/Loss After Tax",
                    "account_category_fkid": "4",
                    "outlet_fkid": "439",
                    "admin_fkid": "7",
                    "trans_created": *************,
                    "created_at": *************,
                    "updated_at": *************,
                    "data_status": "1",
                    "outlet_neme": "Toko Oke"
                }
            ],
            dataVew:''
        },
        methods: {
            currency(val){
                return currency(val)
            },
            onSave(){
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/accounts/closing_book/save_jurnal_closing')?>",
                    data: {
                        data:JSON.stringify(jurnalApp.dataJurnal),
                        startDate:app.form.prop.startDate,
                        endDate:app.form.prop.endDate
                    },
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success: function (response) {
                        loading.hide()
                        Alert.success("success","data jurnal berhasil disimpan")
                        $("#jurnal").css('display','none');
                        $("#summary").css('display','block');
                        // seting data trial balance
                        vmTrial.dataTable.data = response.res
                        vmTrial.total()
                        //setup data laba rugi
                        vmLaba.dataTable.data = response.res
                        vmLaba.total();
                        //setup data neraca
                        vmNeraca.dataTable.data = response.res
                        vmNeraca.total();
                    },
                    error(err){
                        loading.hide()
                        Alert.error("error","data gagal disimpan")
                        console.log(err);
                    }
                });
            }
        },
        watch:{
            'dataJurnal'(newVal){
                let newArr = [];
                let data = newVal;
                for (const i of data) {
                    let tmp = {
                        outlet:i.outlet_neme,
                        outletId:i.outlet_fkid,
                        totalD:0,
                        totalK:0,
                        detail:[]
                    }
                    let find = newArr.find(o=>o.outletId === tmp.outletId)
                    if (!find) {
                        newArr.push(tmp);
                    }
                }

                for (const j in newArr) {
                    let find = data.filter(k=>k.outlet_fkid === newArr[j].outletId)
                    if (find) {
                        for (const l of find) {
                            let tmpJurnal ={
                                account:'',
                                debit:0,
                                kredit:0,
                                des:''
                            }
                            tmpJurnal.account = l.account_code+" - "+l.account_name;
                            tmpJurnal.debit = l.type=="D"?l.nominal:0
                            tmpJurnal.kredit = l.type=="K"?l.nominal:0
                            tmpJurnal.des = l.description
                            l.type=="D"?newArr[j].totalD +=l.nominal:newArr[j].totalK += l.nominal
                            newArr[j].detail.push(tmpJurnal);
                        }
                    } 
                }
                this.dataVew =newArr
            }
        },
    })

    $(function () {
        $(".btn-btl").on('click',function(){
            $("#jurnal").css('display','none');
            $("#app").css('display','block');
        })
    });
</script>