<div class="container-fluid" id="summary" style="display:none">
    <div class="row">
        <div class="col-sm-6">
            <h3>TUTUP BUKU</h3>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 col-sm-12">
            <ul class="nav nav-tabs tab-uniq" id="myTab">
                <li class="active"><a data-toggle="tab" href="#menu1" style="color: #fff;">Trial Balance</a></li>
                <li><a data-toggle="tab" href="#menu2" style="color: #fff;">Laba Rugi</a></li>
                <li><a data-toggle="tab" href="#menu3" style="color: #fff;">Neraca</a></li>
                <li><a data-toggle="tab" href="#menu4" style="color: #fff;">Arus Kas</a></li>
            </ul>

        </div>
        <div class="tab-content">
            <div id="menu1" class="tab-pane fade in active">
                <div class="col-md-12 col-sm-12">
                    <div class="content-uniq">
                        <?php $this->load->view("views/trial_balance_v") ?>
                    </div>
                </div>
            </div>

            <div id="menu2" class="tab-pane fade in">
                <div class="col-md-12 col-sm-12">
                    <div class="content-uniq">
                    <?php $this->load->view("views/laba_rugi_v") ?>
                    </div>
                </div>
            </div>

            <div id="menu3" class="tab-pane fade in">
                <div class="col-md-12 col-sm-12">
                    <div class="content-uniq">
                    <?php $this->load->view("views/neraca_v") ?>
                    </div>
                </div>
            </div>

            <div id="menu4" class="tab-pane fade in">
                <div class="col-md-12 col-sm-12">
                    <div class="content-uniq">
                    <?php $this->load->view("views/aruskas_v") ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6 col-sm-offset-6">
            <button class="btn btn-primary pull-right btn-simpan" @click="onSave">Simpan</button>
            <button class="btn btn-default pull-right btn-btl-summary" style="margin-right:5px">Batal</button>
        </div>
    </div>
</div>

<script>
    var summaryApp = new Vue({
        el:"#summary",
        data:{

        },
        methods: {
            onSave(){
                // set saldo awal per accout setelah tutup buku
                $.ajax({
                    type: "post",
                    url: "url",
                    data: "data",
                    dataType: "dataType",
                    success: function (response) {
                        
                    }
                });
            }
        },
    })

    $(function () {
        $(".btn-btl-summary").on('click',function(){
            $("#jurnal").css('display','block');
            $("#summary").css('display','none');
        })
    });
</script>