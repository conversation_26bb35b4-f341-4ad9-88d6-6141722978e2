<div id="app" style="display:block">
	<div class="container-fluid">
        <div class="content-uniq">
            <div class="row">    
                <div style="margin-bottom: 5px"> 
                    <div class="col-sm-6">
                        <h3 style="display: inline;">Kertas Kerja Neraca Saldo</h3> 
                    </div>
                    <div class="col-sm-6">
                        <div class="pull-right">
                            <div class="btn-group">
                                        <select class="form-control btn  outletSelect bg-primary" multiple v-model="param.outlet">
                                            <?php foreach ($form_select_outlet as $a): ?>
                                        <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-primary btn-block" @click="getData" type="button">Apply</button>
                            </div>
                        </div>
                    </div>
                </div> 
            </div>
            <div class="row">
                <div class="col-sm-12">     		
                    <table id="table-close" class="table table-striped table-report" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th rowspan="2" class="text-center">Daftar Akun</th>
                                <th colspan="2" class="text-center">Trial Balance</th>
                                <th colspan="2" class="text-center">Income Statment</th>
                                <th colspan="2" class="text-center">Balance Sheet</th>
                            </tr>
                            <tr>
                                <th class="text-center">Debit</th>
                                <th class="text-center">Credit</th>
                                <th class="text-center">Debit</th>
                                <th class="text-center">Credit</th>
                                <th class="text-center">Debit</th>
                                <th class="text-center">Credit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="(i,idx) in dataNeraca" v-if="form.prop.neraca.includes(i.categoryType)">
                                <tr style="background-color:darkcyan" >
                                    <td>{{i.category}}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr v-for="j,index in dataNeraca[idx].detail">
                                    <td>{{j.account}}</td>
                                    <td class="text-right">{{currency(j.debit)}}</td>
                                    <td class="text-right">{{currency(j.kredit)}}</td>
                                    <td></td>
                                    <td></td>
                                    <td class="text-right">{{currency(j.debit)}}</td>
                                    <td class="text-right">{{currency(j.kredit)}}</td>
                                </tr>
                            </template>
                            
                            <template v-for="(i,idx) in dataNeraca" v-if="form.prop.laba_rugi.includes(i.categoryType)">
                                <tr style="background-color:darkcyan" >
                                    <td>{{i.category}}</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr v-for="j,index in dataNeraca[idx].detail">
                                    <td>({{j.account}})</td>
                                    <td class="text-right">{{currency(j.debit)}}</td>
                                    <td class="text-right">{{currency(j.kredit)}}</td>
                                    <td class="text-right">{{currency(j.debit)}}</td>
                                    <td class="text-right">{{currency(j.kredit)}}</td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </template>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>Total</th>
                                <th class="text-right">{{currency(form.prop.total.debit)}}</th>
                                <th class="text-right">{{currency(form.prop.total.kredit)}}</th>
                                <th class="text-right">{{currency(form.prop.total.income_d)}}</th>
                                <th class="text-right">{{currency(form.prop.total.income_k)}}</th>
                                <th class="text-right">{{currency(form.prop.total.balance_d)}}</th>
                                <th class="text-right">{{currency(form.prop.total.balance_k)}}</th>
                            </tr>
                            <tr>
                                <td style="background-color:darkgoldenrod;max-width">Net Profit/Loss outlet {{parseString(form.prop.outletSelectText,60)}}</td>
                                <td style="background-color:darkgoldenrod"></td>
                                <td style="background-color:darkgoldenrod"></td>
                                <td style="background-color:darkgoldenrod"></td>
                                <td style="background-color:darkgoldenrod" class="text-right">{{currency(form.prop.netProfitOutlet)}}</td>
                                <td style="background-color:darkgoldenrod"></td>
                                <td style="background-color:darkgoldenrod"></td>
                            </tr>
                            <tr>
                                <th>Net Profit/Loss all outlet</th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th class="text-right">{{currency(form.prop.netProfit)}}</th>
                                <th></th>
                                <th></th>
                            </tr>
                            <tr>
                                <th>Tax Expense</th>
                                <th colspan="2">
                                    <div class="form-group">
                                        <label class="control-label">Tax expense Account</label>
                                        <select name="tax-expense" id="tax-expense"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.taxExpense.value" v-select="form.inputs.taxExpense.value" style="width:100%" require title="- Pilih Akun -">
                                            <?php foreach ($account as $a): ?>
                                                <option value="<?=$a->account_id ?>"><?=htmlentities($a->output) ?></option>
                                            <?php endforeach ?>
                                        </select>	
                                        <small class="text-warning" id="err-category">{{form.inputs.taxExpense.error}}</small>
                                    </div>	
                                    <div class="form-group">
                                        <label class="control-label">Tax payable Account</label>
                                        <select name="tax-payable" id="tax-payable"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.taxPayable.value" v-select="form.inputs.taxPayable.value" style="width:100%" require title="- Pilih Akun -">
                                            <?php foreach ($account as $a): ?>
                                                <option value="<?=$a->account_id ?>"><?=htmlentities($a->output) ?></option>
                                            <?php endforeach ?>
                                        </select>	
                                        <small class="text-warning" id="err-category">{{form.inputs.taxPayable.error}}</small>
                                    </div>	
                                </th>
                                <th>
                                    <div class="float-right">
                                        <label class="control-label">Persen Pajak</label>
                                        <div class="input-group">
                                            <input type="text" id="taxPersen" :class="['form-control form-dark text-right', 'input-sm']" name="taxPersen" v-model="form.prop.taxPersen" v-number="form.prop.taxPersen" required />
                                            <span class="input-group-addon" style="background-color: #2d2d2d;color: #fff;border-color: #6c757d;">%</span>
                                        </div>
                                    </div>
                                </th>
                                <th>
                                    <div class="form-group">
                                        <label class="control-label">Jumlah Pajak</label>
                                        <input type="text" id="tax" :class="['form-control form-dark text-right', 'input-sm', form.inputs.tax.error ? 'is-invalid' : '']" name="tax" v-model="form.inputs.tax.value" v-money="form.inputs.tax.value" required />
                                        <small class="text-warning" id="err-category">{{form.inputs.tax.error}}</small>
                                    </div>
                                </th>
                                <th></th>
                                <th></th>
                            </tr>
                            <tr>
                                <th>Net Profit/Lost After Tax</th>
                                <th colspan="2">
                                    <div class="form-group">
                                        <label class="control-label">Retained erning account</label>
                                        <select name="net" id="net"  :class="['form-control form-control-sm selectpicker']" data-live-search="true" v-model="form.inputs.netProfitId.value" v-select="form.inputs.netProfitId.value" style="width:100%" require title="- Pilih Akun -">
                                            <?php foreach ($account as $a): ?>
                                                <option value="<?=$a->account_id ?>"><?=htmlentities($a->output) ?></option>
                                            <?php endforeach ?>
                                        </select>	
                                        <small class="text-warning" id="err-category">{{form.inputs.netProfitId.error}}</small>
                                    </div>	
                                </th>
                                <th></th>
                                <th>{{currency(profitAfterTax)}}</th>
                                <th></th>
                                <th></th>
                            </tr>
                        </tfoot>
	    			</table>

	    		</div>
	    	</div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="control-label">Memo</label>
                        <textarea name="" id="" cols="30" rows="5" :class="['form-control form-dark', 'input-sm', form.inputs.memo.error ? 'is-invalid' : '']" name="memo" v-model="form.inputs.memo.value"></textarea>
                        <small class="text-warning" id="err-category">{{form.inputs.memo.error}}</small>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-sm-12">
                    <div class="pull-right">
                        <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Batal</button>
                        <button type="button" class="btn btn-primary" id="save" @click="onSubmit">Lanjutkan</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- modal form jurnal -->
	</div>
</div>

<script>
    const url =window.location.toLocaleString();
    const urlParam = new URL(url).searchParams
    const start = urlParam.get('start');
    const end = urlParam.get('end');
    var app = new Vue({
        el:"#app",
        data:{
            param:{
                outlet:[]
            },
            dataMaster:[],
			form:{
                prop:{
                    startDate:start,
                    endDate:end,
                    taxPersen:11,
                    netProfit:0,
                    netProfitOutlet:0,
                    outletSelectText:'',
                    neraca: ['aset','kewajiban','ekuitas'],
                    laba_rugi: ['pendapatan','beban'],
                    total:{
                        debit:0,
                        kredit:0,
                        income_d:0,
                        income_k:0,
                        balance_d:0,
                        balance_k:0
                    }

                },
				inputs:{
					taxExpense:{
						value:"",
						error:"",
                        allowClear: true,
                        required: true,
					},
					taxPayable:{
						value:"",
						error:"",
                        allowClear: true,
                        required: true,
					},
					netProfitId:{
						value:"",
						error:"",
                        allowClear: true,
                        required: true,
					},
					netProfit:{
						value:"",
						error:""
					},
					tax:{
						value:"",
						error:""
					},
					memo:{
						value:"",
						error:""
					},
                    profitAfterTax:{
                        value:"",
                        error:""
                    }
				}
			},
            dataNeraca:[]
        },
        methods: {
            onApplayFilter(){

            },
            currency(val){
                return currency(val)
            },
            onSubmit(){

                if (!this.validate(this.form.inputs)) {
					return;
				}
                
                var data = {};
				for (var key in this.form.inputs) {
					if (this.form.inputs.hasOwnProperty(key)) {
						data[key] = this.form.inputs[key].value;
					}
				}
                data.startDate =start
                data.endDate =end
                data.netProfit = this.form.prop.netProfit
                data.tax =clearFormat(this.form.inputs.tax.value)
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/accounts/closing_book/data_jurnal_closing')?>",
                    data: data,
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success(res) {
                        loading.hide()
                        const jurnal =document.getElementById("jurnal");
                        const summary =document.getElementById("summary");
                        const elApp =document.getElementById("app");
                        jurnal.style.display="block"
                        summary.style.display="none"
                        elApp.style.display="none"
                        // app.dataMaster = res
                        // vmTrial.dataTable.data = res
                        // vmTrial.total()

                        // vmLaba.dataTable.data = res
                        // vmLaba.total();

                        // vmNeraca.dataTable.data = res
                        // vmNeraca.total();

                        // console.log(res);
                        jurnalApp.dataJurnal = res
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error()
                    }
                });
            },
            getData(){
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/accounts/closing_book/data_neraca')?>",
                    data: {
                        start: start,
                        end: end,
                        outlet: app.param.outlet
                    },
                    beforeSend(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide()
                        var dat = []
                        app.form.prop.total.kredit = 0
                        app.form.prop.total.debit = 0
                        app.form.prop.total.balance_d = 0
                        app.form.prop.total.balance_k = 0
                        app.form.prop.total.income_d = 0
                        app.form.prop.total.income_k = 0
                        
                        for (const i in res) {
                            let tmp ={}
                            tmp.detail =[]; 
                            for (const key in res[i].detail) {
                                if (res[i].detail[key]['total'] !=0) {
                                    tmp.category = res[i].category;
                                    tmp.categoryType = res[i].category_type;
                                    let det = {}
                                    det.account = res[i].detail[key]['account_code']+" "+res[i].detail[key]['account_name'];
                                    switch (true) {
                                        case res[i].detail[key]['type_account'] == "D" && res[i].detail[key]['total'] < 0:
                                            det.type = "K"
                                            det.debit = 0
                                            det.kredit = res[i].detail[key]['total']*-1
                                            app.form.prop.total.kredit += res[i].detail[key]['total']*-1

                                            break;
                                        case res[i].detail[key]['type_account'] == "D" && res[i].detail[key]['total'] >= 0:
                                            det.type = "D"
                                            det.debit = res[i].detail[key]['total']
                                            det.kredit = 0
                                            app.form.prop.total.debit += res[i].detail[key]['total']
                                            break;
                                        case res[i].detail[key]['type_account'] == "K" && res[i].detail[key]['total'] < 0:
                                            det.type = "D"
                                            det.debit = res[i].detail[key]['total']*-1
                                            det.kredit = 0
                                            app.form.prop.total.debit += res[i].detail[key]['total']*-1
                                            break;
                                        case res[i].detail[key]['type_account'] == "K" && res[i].detail[key]['total'] >= 0:
                                            det.type = "K"
                                            det.debit = 0
                                            det.kredit = res[i].detail[key]['total']
                                            app.form.prop.total.kredit += res[i].detail[key]['total']
                                            break;
                                    }
                                    tmp.detail.push(det);
                                }                               
                            }
                            dat.push(tmp);
                        }

                        // mencari total
                        for (const i in dat) {
                            for (const j in dat[i].detail) {
                                if (app.form.prop.neraca.includes(dat[i].categoryType)) {
                                    app.form.prop.total.balance_d += dat[i].detail[j].debit
                                    app.form.prop.total.balance_k += dat[i].detail[j].kredit
                                }else{
                                    app.form.prop.total.income_d += dat[i].detail[j].debit
                                    app.form.prop.total.income_k += dat[i].detail[j].kredit
                                }    
                            }
                        }

                        app.dataNeraca = dat

                        app.form.prop.netProfitOutlet = app.form.prop.total.income_k-app.form.prop.total.income_d
                        // let taxPersen = app.form.prop.netProfit/100*app.form.prop.taxPersen
                        // app.form.inputs.tax.value = currency(taxPersen)
                        // let afterTax = app.form.prop.netProfit-taxPersen

                        // if (app.form.prop.netProfit <0) {
                        //     app.form.inputs.taxExpense.required =false
                        //     app.form.inputs.taxPayable.required =false
                        //     app.form.inputs.tax.value =0
                        //     app.form.inputs.tax.value =0
                        //     app.form.prop.taxPersen = 0
                            
                        // }

                        // update selectpicker
                        var options = $('.outletSelect option:selected');
                        var selected = [];

                        $(options).each(function(){
                            selected.push( $(this).text() ); 
                        });
                        app.form.prop.outletSelectText=selected.toString()
                    },
                    error(err){
                        Alert.error("Error","gagal menampilkan data")
                        console.log(err);
                    }
                });
            },
            getFirstData(){
                $.ajax({
                    type: "post",
                    url: "<?=base_url('finance/accounts/closing_book/data_neraca')?>",
                    data: {
                        start: start,
                        end: end,
                        outlet: app.param.outlet
                    },
                    beforeSend(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide()
                        var dat = []
                        app.form.prop.total.kredit = 0
                        app.form.prop.total.debit = 0
                        app.form.prop.total.balance_d = 0
                        app.form.prop.total.balance_k = 0
                        app.form.prop.total.income_d = 0
                        app.form.prop.total.income_k = 0
                        
                        for (const i in res) {
                            let tmp ={}
                            tmp.detail =[]; 
                            for (const key in res[i].detail) {
                                if (res[i].detail[key]['total'] !=0) {
                                    tmp.category = res[i].category;
                                    tmp.categoryType = res[i].category_type;
                                    let det = {}
                                    det.account = res[i].detail[key]['account_code']+" "+res[i].detail[key]['account_name'];
                                    switch (true) {
                                        case res[i].detail[key]['type_account'] == "D" && res[i].detail[key]['total'] < 0:
                                            det.type = "K"
                                            det.debit = 0
                                            det.kredit = res[i].detail[key]['total']*-1
                                            app.form.prop.total.kredit += res[i].detail[key]['total']*-1

                                            break;
                                        case res[i].detail[key]['type_account'] == "D" && res[i].detail[key]['total'] >= 0:
                                            det.type = "D"
                                            det.debit = res[i].detail[key]['total']
                                            det.kredit = 0
                                            app.form.prop.total.debit += res[i].detail[key]['total']
                                            break;
                                        case res[i].detail[key]['type_account'] == "K" && res[i].detail[key]['total'] < 0:
                                            det.type = "D"
                                            det.debit = res[i].detail[key]['total']*-1
                                            det.kredit = 0
                                            app.form.prop.total.debit += res[i].detail[key]['total']*-1
                                            break;
                                        case res[i].detail[key]['type_account'] == "K" && res[i].detail[key]['total'] >= 0:
                                            det.type = "K"
                                            det.debit = 0
                                            det.kredit = res[i].detail[key]['total']
                                            app.form.prop.total.kredit += res[i].detail[key]['total']
                                            break;
                                    }
                                    tmp.detail.push(det);
                                }                               
                            }
                            dat.push(tmp);
                        }

                        // mencari total
                        for (const i in dat) {
                            for (const j in dat[i].detail) {
                                if (app.form.prop.neraca.includes(dat[i].categoryType)) {
                                    app.form.prop.total.balance_d += dat[i].detail[j].debit
                                    app.form.prop.total.balance_k += dat[i].detail[j].kredit
                                }else{
                                    app.form.prop.total.income_d += dat[i].detail[j].debit
                                    app.form.prop.total.income_k += dat[i].detail[j].kredit
                                }    
                            }
                        }

                        app.dataNeraca = dat
                        app.form.prop.netProfitOutlet = app.form.prop.total.income_k-app.form.prop.total.income_d
                        app.form.prop.netProfit = app.form.prop.total.income_k-app.form.prop.total.income_d
                        let taxPersen = app.form.prop.netProfit/100*app.form.prop.taxPersen
                        app.form.inputs.tax.value = currency(taxPersen)
                        let afterTax = app.form.prop.netProfit-taxPersen

                        if (app.form.prop.netProfit <0) {
                            app.form.inputs.taxExpense.required =false
                            app.form.inputs.taxPayable.required =false
                            app.form.inputs.tax.value =0
                            app.form.inputs.tax.value =0
                            app.form.prop.taxPersen = 0
                            
                        }
                    },
                    error(err){
                        Alert.error("Error","gagal menampilkan data")
                        console.log(err);
                    }
                });
            },
            parseString(str,len){
                return parseString(str,len)
            }
        },
        watch:{
            'form.inputs.tax.value'(newVal){
                this.form.inputs.profitAfterTax.value = this.form.prop.netProfit-newVal
                // this.form.prop.taxPersen = newVal/this.form.prop.netProfit
            },
            'form.prop.taxPersen'(newVal){
                let persen = this.form.prop.netProfit/100*newVal
                this.form.inputs.tax.value = currency(persen)
            },

        },
        computed:{
            'profitAfterTax'(){
                let profitAfterTax = this.form.prop.netProfit - clearFormat(this.form.inputs.tax.value)
                this.form.inputs.profitAfterTax.value = profitAfterTax
                console.log("tes");
                return profitAfterTax
            },
        },
        mounted() {
            // this.getData()            
        },
    })

    $(function () {
        $('.outletSelect').selectpicker('selectAll');
        app.getFirstData(); 

        // $('.outletSelect').on('changed.bs.select', function (e) {
            // var options = $('.outletSelect option:selected');
            // var selected = [];

            // $(options).each(function(){
            //     selected.push( $(this).text() ); 
            // });
            // app.form.prop.outletSelectText=selected.toString()
            // console.log(selected);
        // });

        let mstOutlet = '<?=json_encode($form_select_outlet)?>'
        let tmpArr = [];
        for (const o of $.parseJSON(mstOutlet)) {
            tmpArr.push(o.name)
        }
        app.form.prop.outletSelectText=tmpArr.toString()

    });
</script>

<?php $this->load->view('summary_v');?>
<?php $this->load->view('jurnal_close_v');?>