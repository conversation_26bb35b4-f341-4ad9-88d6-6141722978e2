<div id="app">
	<div class="container-fluid">
		<div class="row">    
	        <div style="margin-bottom: 5px"> 
	        	<div class="col-sm-6">
	                <div class="col-sm-3" >
                        <h3 style="display: inline;">Tutup Buku</h3>
	                </div>    
	            </div>
                <div class="col-sm-6">
                    <button type="button" class="btn btn-primary pull-right" @click="onAdd()"><PERSON><PERSON> Tutup Buku <i class="fa fa-plus"></i></button>
                </div>
	        </div> 
	    </div>
	    <div class="content-uniq">
	    	<div class="row">
	    		<div class="col-sm-12">     		
	    			<table id="table-close" class="table table-striped table-report" cellspacing="0" width="100%">
	    				<thead>
	    					<tr>
	    						<th width="5%">No</th>
	    						<th width="20%">Periode</th>
	    						<th width="35%">Catatan</th>
	    						<th width="25">Keuntung<PERSON>/(Rugi)</th>
	    						<th width="15"><i class="fa fa-cog fa-spin"></i></th>
	    					</tr>
	    				</thead>
	    			</table>
	    		</div>
	    	</div>
	    </div>	

        <!-- modal form jurnal -->
        <div class="modal fade" id="modal-form" tabindex="-1" role="dialog" aria-labelledby="modal-form" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog modal-ls" role="document">
                <div class="modal-content" style="background: #27292a;">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalp">Form Tutup Buku</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
							<div class="col-sm-12">
								<p class="text-center">Anda akan melakukan proses tutup buku untuk periode finansial:</p>
							</div>
							<div class="col-sm-6">
								<h4>Dari Tanggal</h4>
							</div>
							<div class="col-sm-6">
								<h4>{{form.inputs.startDate.value}}</h4>
							</div>
							<div class="col-sm-6">
								<h4>Sampai Tanggal</h4>
							</div>
							<div class="col-sm-6">
								<div class="input-group date">
                                    <input type="text" class="form-control" id="end-date" name="tanggal" v-model="form.inputs.endDate.value" v-datepicker="form.inputs.endDate.value" placeholder="Tanggal Selesai" autocomplete="false"><span class="input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
                                </div>
							</div>
							<div class="col-sm-12" style="margin-top:30px">
								<div class="alert alert-info text-center" role="alert">Setela proses tutup buku, Anda tidak dapat lagi melakukan perubahan terhadap buk Anda pada tanggal SEBELUM {{form.inputs.endDate.value}}</div>
							</div>
                        </div>
                        <br>
                        
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Batal</button>
                            <button type="button" class="btn btn-primary" id="save" @click="onNext">Lanjutkan</button>
                        </div>
                        <div style="clear: both;"></div>
                    </div><!-- /.modal-body -->
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div>    
	</div>
</div>

<script>
    var app = new Vue({
        el:"#app",
        data:{
			form:{
				inputs:{
					startDate:{
						value:"",
						error:""
					},
					endDate:{
						value:"",
						error:""
					}
				}
			}
        },
        methods: {
            datatable(){
                $(function () {
                    var t = $("#table-close").DataTable({					
						processing: false,
						serverSide: false,
						scrollX:true,
						fixedHeader: true,
                        pageLength: 50,
                        destroy : true,
						ajax: {
				            url: "<?=current_url() ?>/data_table",
				            type: "POST",
				        },
						columns: [
							{
								"data": null,
								"class":"text-center",
								"orderable": true,								
								"searchable": false,
							},							
							{
								"data": "end_date",
								"render": function(data, type, row) {
									return row.start_date +"-"+ row.end_date
								}
							},
							{"data": "description"},
							{
                                "data": "laba",
                                "render": function(data, type, row) {
									return currency(data)
								}
                            },
							{
								"orderable": false,
								"searchable": false,
								"className": "text-center",
								"render": function(data, type, row) {
									var btn_edit = '<button class="btn btn-xs btn-warning btn-edit"><i class="fa fa-edit"></i></button>';
									var btn_hapus = '<button class="btn btn-xs btn-danger btn-hapus"><i class="fa fa-trash"></i></button>';									
									return btn_edit+' '+btn_hapus
									
								}
							}
						],
						order: [[1, 'asc']],
					});//end datatable init
                });
            },
            onAdd(){
				// get last close book
				$.ajax({
					type: "get",
					url: "<?=base_url('finance/accounts/closing_book/get_last')?>",
					dataType: "json",
					beforeSend(){
						loading.show()
					},
					success: function (res) {
						loading.hide()
						app.form.inputs.startDate.value = milisToLocal(res.end_date,"DD-MM-YYYY")
						$("#modal-form").modal("show");
					},
					error(err){
						loading.hide()
						Alert.error("error")
						console.log(err);
					}
				});
            },
			onNext(){
				let startDate = reverseDate(this.form.inputs.startDate.value)
				let endDate = reverseDate(this.form.inputs.endDate.value)
				window.open("<?=base_url('')?>finance/accounts/closing_book/neraca_saldo?start="+dateToMilis(startDate)+"&end="+dateToMilis(endDate));
			}
        },
        mounted() {
            this.datatable()
        },
    })
	$(function () {
		$('#end-date').datepicker({
			autoclose: true,
			format: "dd-mm-yyyy",
			// startDate: new Date(),
			todayHighlight: true,
		}).datepicker('setDate','now');
	});
</script>