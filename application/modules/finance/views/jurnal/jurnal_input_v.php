<style>
	div.dt-buttons {
		float: right;
		margin-left:10px;
	}
	.line-input {
	  background: transparent;
	  border: none;
	  border-bottom: 1px solid #9c9c9c;
	  -webkit-box-shadow: none;
	  box-shadow: none;
	  border-radius: 0;
	  color: #fff;
	  height:auto;
	  font-size:20px;
	}

	.line-input:focus {
	  -webkit-box-shadow: none;
	  box-shadow: none;
	}

	/* Styles for wrapping the search box */

.main {
    width: 50%;
    margin: 50px auto;
}

/* Bootstrap 3 text input with search icon */

.has-search .form-control-feedback {
    right: initial;
    left: 0;
    color: #ccc;
}

.has-search .form-control {
    padding-right: 12px;
    padding-left: 34px;
}
</style>
<br>
<div id="app">
	<div class="container-fluid">
		<div class="row">    
	        <div style="margin-bottom: 5px"> 
	        	<div class="col-sm-6">
	                <div class="col-sm-3" >
                        <h3 style="display: inline;">Jurnal Input</h3>
	                </div>
                    <div class="col-sm-9">
                        <button type="button" class="btn btn-primary" @click="onAdd()">Tambah Jurnal <i class="fa fa-plus"></i></button>
                    </div>
	            </div>
	            <div class="col-sm-6">
	            	<div class="pull-right">
	            		<div class="btn-group">
		                    <button type="button" class="btn btn-info daterange" style="background-color: #5bc0de" id="btn-date">
		                        <span> 
		                            <i class="glyphicon glyphicon-calendar"></i> Date
		                        </span>
		                        <i class="caret"></i>
		                    </button>
		                </div>
		                <div class="btn-group">
		                    <select class="form-control btn btn-info" v-model="filterForm.outlet">
		                        <option value="0" selected="">Semua Outlet</option>
		                        <?php foreach ($form_select_outlet as $a): ?>                   
		                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
		                        <?php endforeach ?>
		                    </select>                                   
		                </div>
		                <div class="btn-group">
		                    <button class="btn btn-primary btn-block" @click="onApply" type="button">Apply</button>                  
		                </div>
	            	</div>	                
	            </div>
	        </div> 
	    </div>
	    <div class="content-uniq">
            <div class="row">
                <div  class="col-lg-3 col-xs-6">
                    <!-- small box -->
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h4 class="pull-right">{{total.debit}}</h4>
                            <h5>Total Debit</h5>
                        </div>
                        <div class="icon">
                            <i class="ion ion-stats-bars"></i>
                        </div>
                    </div>
                </div><!-- ./col -->
                <div  class="col-lg-3 col-xs-6">
                    <!-- small box -->
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h4 class="pull-right">{{total.kredit}}</h4>
                            <h5>Total Kredit</h5>
                        </div>
                        <div class="icon">
                            <i class="ion ion-stats-bars"></i>
                        </div>
                    </div>
                </div><!-- ./col -->
            </div>
	    	<div class="row">
	    		<div class="col-sm-12">
	    			<div class="form-group has-feedback has-search">
	    				<span class="glyphicon glyphicon-search form-control-feedback"></span>
	    				<input type="text" name="search" class="line-input form-control" id="search" placeholder="Pencarian Tipe Transaksi" v-model="filterForm.search" title="enter untuk mencari">
	    			</div>
	    		</div>
	    	</div>
	    	<div class="row">
	    		<div class="col-sm-12">     		
	    			<table id="tableProduct" class="table table-striped table-report" cellspacing="0" width="100%">
	    				<thead>
	    					<tr>
	    						<th>Rincian</th>
	    						<th>Debit</th>
	    						<th>Kredit</th>
	    					</tr>
	    				</thead>
	    				<tbody>
	    					<tr v-if="dataTable.length =='0'">
	    						<td colspan="3" class="text-center">Tidak Ada Data yang Tersedia Pada Table Ini</td>
	    					</tr>
	    					<template v-else v-for="data,idx in dataTable">
	    						<tr >
	    							<td colspan="3"><b>{{data.trans_name}}</b> {{data.outlet}} -> {{data.display_nota}} {{data.dateTime}} </td>
	    						</tr>
	    						<tr v-for="detail in data.detail">
	    							<td>
                                        ({{detail.account_code}}) - {{detail.account_name}} <br>
                                        <small>{{detail.description}}</small>
                                    </td>
	    							<td v-if="detail.type == 'D'">{{currency(detail.nominal)}}</td>
	    							<td v-else>{{currency(0)}}</td>
	    							<td v-if="detail.type == 'K'">{{currency(detail.nominal)}}
                                    </td>
	    							<td v-else>{{currency(0)}}</td>
	    						</tr>
	    						<tr >
	    							<td class="pull-right">Total :</td>
	    							<td>{{currency(data.total_d)}}</td>
	    							<td>{{currency(data.total_k)}}</td>
	    						</tr>
	    					</template>
	    					
	    				</tbody>
	    			</table>
	    		</div>
	    	</div>
	    </div>	

        <!-- modal form jurnal -->
        <div class="modal fade" id="modalJurnal" tabindex="-1" role="dialog" aria-labelledby="modalJurnal" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content" style="background: #27292a;">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalp">Form Jurnal Input</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="outlet">Outlet*</label><br>
                                    <select class="form-control" id="input-outlet" v-model="form.outlet" v-select="form.outlet" multiple>
                                        <?php foreach ($form_select_outlet as $a): ?>                   
                                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                        <?php endforeach ?>                
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label for="tgl">Tanggal*</label>
                                    <div class="input-group date">
                                        <input type="text" class="form-control" id="tgl" name="tgl" v-model="form.date" v-datepicker="form.date" placeholder="Tanggal Jurnal" autocomplete="false"><span class="input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <table class="table table-striped table-report" style="margin-bottom: 5px;">
                                    <thead>
                                        <tr>
                                            <th>Akun</th>
                                            <th>Debit</th>
                                            <th>Kredit</th>
                                            <th>Keterangan</th>
                                            <th>#</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(i,idx) in form.detail">
                                            <td>
                                                <select :class="['form-control btn-sm btn btn-info selectAkun selectAkun']+idx" v-model="form.detail[idx].akun" v-select="form.detail[idx].akun" id="akunId" width="100%">
                                                    <?php foreach ($account as $a): ?>                   
                                                        <option value="<?=$a->account_id ?>"><?=$a->code." ".htmlentities($a->name) ?></option>
                                                    <?php endforeach ?>                
                                                </select>
                                            </td>
                                            <td>
                                                <input type="text" :class="['form-control text-right form-dark', 'input-sm']" v-model="form.detail[idx].debit" v-money="form.detail[idx].debit" placeholder="" required onfocus="this.select()" :disabled="clearFormat(form.detail[idx].kredit) != 0"/>
                                            </td>
                                            <td>
                                                 <input type="text" :class="['form-control text-right form-dark', 'input-sm']" v-model="form.detail[idx].kredit" v-money="form.detail[idx].kredit" placeholder="" required onfocus="this.select()" :disabled="clearFormat(form.detail[idx].debit) != 0"/>
                                            </td>
                                            <td>
                                                <input type="text" :class="['form-control text-right form-dark', 'input-sm']" v-model="form.detail[idx].keterangan" placeholder="" required/>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" @click="onDelete(idx)"><i class="fa fa-trash"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <thead>
                                        <tr>
                                            <th class="text-right">Total</th>
                                            <th class="text-right">{{currency(totalInput.debit)}}</th>
                                            <th class="text-right">{{currency(totalInput.kredit)}}</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                </table>
                                <button type="button" class="btn btn-primary pull-right" @click="onAddAkun()"><i class="fa fa-plus"></i></button>
                            </div>
                        </div>
                        <br> 
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Close</button>
                            <button type="button" class="btn btn-primary" @click="onSubmit()" id="save">Simpan</button>
                        </div>
                        <div style="clear: both;"></div>
                    </div><!-- /.modal-body -->
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div>    
	</div>    
</div>

<script type="text/javascript">

    var vm = new Vue({    
        el: "#app",
        data:{
            dataTable:[],
            no:1,
            filterForm:{
                startDate:'',
                endDate:'',
                timeZone:'',
                outlet:'0',
                limit:30,
                offset:0,
                search:''
            },
            total:{
                debit:0,
                kredit:0
            },
            form:{
                outlet:[],
                detail:[],
                date:''
            }
        },
        methods:{
        	currency(val){
                return currency(val)
        	},
            onApply(){
                if (vm.filterForm.outlet == '0') {
                    Alert.confirm("Memilih Semua Outlet","Proses Ini Mungkin Membutuhkan Sedikit Waktu, Pastikan Koneksi Anda Dalam Keadaan Baik").then(function(result){
                        if (!result.value) {
                            return;
                        }
                        vm.getData()
                    })
                }else{
                    vm.getData()
                }
            },
            async getData(){
            	var data = {};
                for (var key in vm.filterForm) {
                    if (vm.filterForm.hasOwnProperty(key)) {
                        data[key] = vm.filterForm[key];
                    }
                }
            	$.ajax({
            		url: '<?=site_url()?>finance/jurnal/jurnal_input/dataJurnal',
            		type: 'POST',
            		data: {param:data},
            		beforeSend(){
            			loading_show();
                        vm.data=[]
            		},
            		success(res){
            			loading_hide()
            			vm.dataTable = res
                        let debit = 0;
                        let kredit = 0
                        for(let i in res ){
                            debit += parseInt(res[i].total_d)
                            kredit += parseInt(res[i].total_k)
                        }
                        vm.total.debit = currency(debit)
                        vm.total.kredit = currency(kredit)
            		},
            		error(err){
            			Alert.error("Error","Terjadi Kesalhan...")
            			console.log(err)
            		}
            	})
            	.always(function() {
            		loading_hide()
            	});
            },
            onAdd(){
                $("#modalJurnal").modal("show");
                // vm.clearForm()
                // this.onAddAkun()
                // vm.form.outlet=[]
                // setTimeout(() => {
                //     $("#input-outlet").val();
                //     $("#tgl").val();
                //     $("#input-outlet").trigger('change');
                //     $('.selectAkun').selectpicker('refresh');
                // }, 500);
            },               
            onAddAkun(){
                var detail = {
                    akun : "",
                    debit : 0,
                    kredit :0,
                    keterangan:'',
                    account_id:'',
                }
                this.form.detail.push(detail)
                // recaling selec2
                $(document).ready(function () {
                    $('.selectAkun').selectpicker({
                        title:"Pilih Akun",
                        style: 'btn btn-sm btn-info',
                        selectedTextFormat:"count >3",
                        width:"100%",
                        liveSearch:true,
                        actionsBox:true,
                        allowClear: true,
                    });
                });
            },
            clearForm(){
                vm.form.outlet = []
                vm.form.detail = []
                setTimeout(function() {                          
                    $('.selectpicker').selectpicker('refresh').trigger('change');
                    $("#tgl").datepicker("refresh");
                },500);
            },
            clearFormat(param){
                return clearFormat(param)
            },
            onSubmit(){
                // validasi account must use per line
                var totalDebit = 0;
                var totalKredit = 0;
                for (const i in this.form.detail) {
                    if ((this.form.detail[i].akun == "" && this.form.detail.debit > 0) || this.form.detail.kredit > 0 || this.form.detail[i].akun=="") {
                        return Alert.warning("Oops","list akun tidak boleh kosong")
                    }

                    if (clearFormat(this.form.detail[i].debit) < 0 || clearFormat(this.form.detail[i].kredit) < 0) {
                        return Alert.warning('Oops','nominal debit/kredit tidak boleh minus (-)')
                    }

                    this.form.detail[i].debit = clearFormat(this.form.detail[i].debit)
                    this.form.detail[i].kredit = clearFormat(this.form.detail[i].kredit)

                    totalDebit += clearFormat(this.form.detail[i].debit)
                    totalKredit += clearFormat(this.form.detail[i].kredit)
                }
                // validasi debt kredit
                if (parseFloat(this.totalInput.debit).toFixed(2) != parseFloat(this.totalInput.kredit).toFixed(2)) {
                    // console.log("D:"+this.totalInput.debit);
                    // console.log("K:"+this.totalInput.kredit);
                    return Alert.warning("Oops","Jumlah Kredit dan Debit tidak sama");
                }

                if (this.form.outlet.length ==0) {
                    return Alert.warning("Oops","Outlet Belum Diisi");
                }

                let dateTrans = reverseDate(this.form.date)

                let milis = new Date(dateTrans).getTime();

                this.form.date = milis

                var data = this.form

                $.ajax({
                    type: "post",
                    url: "<?=base_url()?>finance/jurnal/jurnal_input/action/insert",
                    data: {data},
                    dataType: "json",
                    beforeSend:function(){
                        loading.show();
                    },
                    success: function (response) {
                        loading.hide();
                        Alert.success("Success","Data berhasil disimpan")
                        // vm.getData()
                        $("#modalJurnal").modal('hide')
                        location.reload()
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error("error","Data gagal disimpan");
                    }
                });
            },
            onDelete(idx){
               this.form.detail.splice(idx,1);
            },
            // onSelectAccount(idx){
            //     $(function () {
            //         $('.selectAkun'+idx).on('select2:select', function (e) {
            //             var data = e.params.data;
            //             console.log(data);
            //         });
            //     });
            // }
            
        },
        computed:{
            totalInput(){
                let totalD = 0;
                let totalK = 0;
                this.form.detail.map((el)=>{
                    totalD +=clearFormat(el.debit);
                    totalK +=clearFormat(el.kredit);
                });
                let result ={
                    debit : totalD,
                    kredit : totalK, 
                }

                return result
            }
        },
        mounted(){
            $(function() {
                setTimeout(function() {
                    vm.filterForm.startDate = $('#btn-date').data('daterangepicker').startDate._d.valueOf()
                    vm.filterForm.endDate = $('#btn-date').data('daterangepicker').endDate._d.valueOf()
                    vm.filterForm.timeZone = timezone();
                }, 100);
                
            });
        }
    })

$(function() {
    $('#btn-date').on('apply.daterangepicker', function(ev, val) {
        $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
        vm.filterForm.startDate = val.startDate.valueOf()
        vm.filterForm.endDate = val.endDate.valueOf()
    });

    let input = document.getElementById("search");
    input.addEventListener("keydown", function(event) {
      if (event.keyCode === 13) {
        event.preventDefault();
        vm.getData()
      }
    });

    $('#tgl').datepicker({
        autoclose: true,
        dateFormat: "yyyy-mm-dd",
        format : 'dd-mm-yyyy',
        // startDate: new Date(vm.master.startDate),
        todayHighlight: true,
    });

    $('#input-outlet').selectpicker({
        title:"Pilih Outlet",
        style: 'btn btn-default btn-block',
        selectedTextFormat:"count >3",
        dropdownAlignRight:true,
        width:'100%',
        liveSearch:true,
        actionsBox:true,
        color:'white'
    });
});
</script>