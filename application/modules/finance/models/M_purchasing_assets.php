<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class M_purchasing_assets extends CI_Model {

    public function data_purchase($param)
    {
        $this->datatables->select("
            purchase.purchase_id,
            purchase.invoice,
            purchase.keterangan,
            purchase.bayar,
            purchase.grand_total,
            purchase.hutang,
            purchase.status_lunas,
            purchase.outlet_fkid,
            purchase.supplier_fkid, 
            purchase.employee_fkid, 
            purchase.admin_fkid,
            purchase.jatuh_tempo,
            purchase.data_created, 
            purchase.data_status,
            purchase.sub_total,
            purchase.discount_total,
            purchase.type_discount,
            ifnull((select sum(nominal) from debt_payment WHERE purchase_fkid=purchase.purchase_id), 0) as debt_payment,
            ifnull((select sum(nominal) from purchase_refund WHERE purchase_fkid=purchase.purchase_id), 0) as refund,

            ifnull(sum((select sum(tot_dis) from retur_products where purchase_product_fkid = pp.purchase_products_id)),0) as total_retur,

            (CASE WHEN (purchase.employee_fkid is not null)
            THEN (SELECT name FROM employee WHERE employee_id = purchase.employee_fkid)
            ELSE (SELECT name FROM admin WHERE admin_id=purchase.admin_fkid )
            END) AS operator,

            outlets.name AS outlet_name,
            supplier.name AS supplier_name,
            purchase.user_input AS admin_name,
            (select name from shift where shift_id=purchase.shift_fkid) as shift_name,

            ifnull(sum((select sum(qty_arive) from purchase_confrim where purchase_product_fkid = pp.purchase_products_id)),0) as arive,

            sum(pp.qty_stok) as qty_stok

        ")
        ->from('purchase')
        ->join('purchase_products pp ', 'pp.purchase_fkid = purchase.purchase_id', 'left')
        ->join('outlets', 'outlets.outlet_id = purchase.outlet_fkid', 'left')
        ->join('supplier', 'supplier.supplier_id = purchase.supplier_fkid', 'left')
        ->where('purchase.data_status', 'on') //cari hanya data aktif
        ->where('purchase.admin_fkid', $this->session->userdata('admin_id')) //ambil berdasarkan yang login
        ->where('purchase.purchase_type','asset');


        if (!empty($param['outlet'])) {
            $this->datatables->where("outlets.outlet_id in (".$param['outlet'].")");
        }

        if (!empty($param['start_date'])) {
            $this->datatables->where("purchase.data_created >=", $param['start_date']);
        }
        if (!empty($param['end_date'])) {
            $this->datatables->where("purchase.data_created <=", $param['end_date']);
        } 

        $this->datatables->group_by('purchase.purchase_id');
        return $this->datatables->generate();
    }

}

/* End of file M_purchasing_assets.php */
