<?php

use Mpdf\Tag\I;

defined('BASEPATH') or exit('No direct script access allowed');

class M_set_jurnal_umum extends CI_Model
{

    public function __construct()
    {
        parent::__construct();
        //Do your magic here      

    }

    public function jurnal_umum_save($data)
    {
        return $this->db->insert('finance_jurnal_umu', $data);

    }

    public function add_to_jurnal_purchase($purchase_id)
    {

        if ($this->cek_subcribe()) {
            $data = $this->data_purchase($purchase_id);
            $data_jurnal = [];
            $data_insert = [
                'trans_type' => "2",
                'trans_id' => $purchase_id,
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => $data->invoice,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => $data->date_purchase,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $sisa = $data->grand_total - $data->bayar;
            $payment = $data->bayar;
            if ($sisa < $data->grand_total) {
                $payment = $data->grand_total;
            }
            // PAYMENT
            if ($data->hutang != 0 && $data->bayar == 0) { //full hutang
                $data_insert['account_code'] = $this->account('purchase_hutang')->account_code;
                $data_insert['account_name'] = $this->account('purchase_hutang')->account_name;
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $data->hutang;
                $data_insert['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
                $data_insert['description'] = "Total Hutang";
                array_push($data_jurnal, $data_insert);
            }

            if ($data->hutang == 0 && $data->bayar != 0) { //lunas
                $data_insert['account_code'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['code'];
                $data_insert['account_name'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['name'];
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $payment;
                $data_insert['account_category_fkid'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['category_id'];
                $data_insert['description'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['payment_name'];
                array_push($data_jurnal, $data_insert);
            }

            if ($data->hutang != 0 && $data->bayar != 0) { //lunas dibayar sebagian
                $data_insert['account_code'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['code'];
                $data_insert['account_name'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['name'];
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $data->bayar;
                $data_insert['account_category_fkid'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['category_id'];
                $data_insert['description'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['payment_name'];
                array_push($data_jurnal, $data_insert);

                $data_insert['account_code'] = $this->account('purchase_hutang')->account_code;
                $data_insert['account_name'] = $this->account('purchase_hutang')->account_name;
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $data->hutang;
                $data_insert['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
                $data_insert['description'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['payment_name'];
                array_push($data_jurnal, $data_insert);
            }



            // PERSEDIAAN
            $total_beban = 0;
            $total_pajak = 0;
            $total_discount = 0;
            $saldoProduct = 0;
            $discDiterima = 0;
            foreach ($data->detail as $a) {

                // PAJAK
                $tmptotal_pajak = 0;
                if ($a['tax'] > 0) {
                    if ($a['tax_type'] == "percentage") {
                        $pajak_persen = $a['total'] / 100 * $a['tax'];
                        $tmptotal_pajak = (int) $pajak_persen;
                    } else {
                        $tmptotal_pajak = $a['tax'];
                    }
                    // cek apakah ada custom pajak
                    $account_pajak = $this->get_account_gratuity($a['gratuity_fkid'], 'tax');
                    if ($account_pajak) {
                        $data_insert['account_code'] = $account_pajak->account_code;
                        $data_insert['account_name'] = $account_pajak->account_name;
                        $data_insert['type'] = "K";
                        $data_insert['nominal'] = $tmptotal_pajak;
                        $data_insert['account_category_fkid'] = $account_pajak->category_id;
                        $data_insert['description'] = $account_pajak->tax_name;
                        array_push($data_jurnal, $data_insert);
                    } else {
                        $total_pajak += $tmptotal_pajak;
                    }
                }

                // stock management no
                if ($a['stock_management'] == '0') {
                    $data_insert['account_code'] = $this->account_purchase($a['products_fkid'])['account_code'];
                    $data_insert['account_name'] = $this->account_purchase($a['products_fkid'])['account_name'];
                    $data_insert['type'] = "D";
                    $data_insert['nominal'] = $a['diterima'] * $a["price_stok"];
                    $data_insert['account_category_fkid'] = $this->account_purchase($a['products_fkid'])['category_id'];
                    $data_insert['description'] = $a['product_name'];
                    array_push($data_jurnal, $data_insert);
                } else {
                    if ($a['diterima'] != 0) {
                        // jika ada custom account finance product
                        $data_insert['account_code'] = $this->get_accunt_product($a['products_fkid'])['account_code'];
                        $data_insert['account_name'] = $this->get_accunt_product($a['products_fkid'])['account_name'];
                        $data_insert['type'] = "D";
                        $data_insert['nominal'] = $a['diterima'] * $a["price_stok"];
                        $data_insert['account_category_fkid'] = $this->get_accunt_product($a['products_fkid'])['category_id'];
                        $data_insert['description'] = $a['product_name'];
                        array_push($data_jurnal, $data_insert);
                    }
                }

                // penerimaan barang setengah
                if ($a['qty_stok'] != $a['diterima']) {
                    $saldoProduct = ($a['qty_stok'] - $a['diterima']) * $a["price_stok"];
                    $data_insert['account_code'] = $this->account('purchase_payment')->account_code;
                    $data_insert['account_name'] = $this->account('purchase_payment')->account_name;
                    $data_insert['type'] = "D";
                    $data_insert['nominal'] = $saldoProduct;
                    $data_insert['account_category_fkid'] = $this->account('purchase_payment')->category_id;
                    $data_insert['description'] = $a['product_name'];
                    array_push($data_jurnal, $data_insert);
                }
            }

            if ($total_pajak > 0) {
                $data_insert['account_code'] = $this->account('purchase_tax')->account_code;
                $data_insert['account_name'] = $this->account('purchase_tax')->account_name;
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $total_pajak;
                $data_insert['account_category_fkid'] = $this->account('purchase_tax')->category_id;
                $data_insert['description'] = "Total Tax";
                array_push($data_jurnal, $data_insert);
                // $this->db->insert('finance_jurnal_umum', $data_insert);
            }

            // $grouping = $this->group_account($data_jurnal);           
            $this->db->insert_batch('finance_jurnal_umum', $data_jurnal);

            // DEBIT MEMO JIKA PEMBAYARAN LEBIH
            $data_insert['trans_type'] = "6";
            $data_insert['trans_id'] = current_millis() . "." . $purchase_id;
            $hutang = $data->grand_total - $data->bayar;
            $dataDebitMemo = [];
            if ($hutang < 0) {
                $data_insert['account_code'] = $this->account('purchase_debitmemo')->account_code;  //"1-02001";
                $data_insert['account_name'] = $this->account('purchase_debitmemo')->account_name; //"Piutang Usaha";
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $hutang * -1;
                $data_insert['account_category_fkid'] = $this->account('purchase_debitmemo')->category_id;
                $data_insert['description'] = "Debit memo " . $data->invoice;
                array_push($dataDebitMemo, $data_insert);

                $data_insert['account_code'] = $this->account('hutang_usaha')->account_code; //"3-01000";
                $data_insert['account_name'] = $this->account('hutang_usaha')->account_name; //"Utang Usaha";
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $hutang * -1;
                $data_insert['account_category_fkid'] = $this->account('hutang_usaha')->category_id;
                $data_insert['description'] = "Debit memo " . $data->invoice;
                array_push($dataDebitMemo, $data_insert);
                // $this->db->insert('finance_jurnal_umum', $dataDebitMemo);    
                // $dataInserDebtMemo = $this->group_account($dataDebitMemo);           
                $this->db->insert_batch('finance_jurnal_umum', array_values($dataDebitMemo));
            }
        }

    }

    function jurnal_purchase_v2($purchase_id)
    {
        if ($this->cek_subcribe()) {
            $data = $this->data_purchase($purchase_id);
            $data_jurnal = [];
            $data_insert = [
                'trans_type' => "2",
                'trans_id' => $purchase_id,
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => $data->invoice,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => $data->date_purchase,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1'
            ];

            $sisa = $data->grand_total - $data->bayar;
            $payment = $data->bayar;
            if ($sisa < $data->grand_total) {
                $payment = $data->grand_total;
            }

            // data purchasing
            $qty_arive = 0;
            foreach ($data->detail as $a) {
                // PAJAK
                $tmptotal_pajak = 0;
                $total_pajak = 0;
                if ($a['tax'] > 0) {
                    if ($a['tax_type'] == "percentage") {
                        $pajak_persen = $a['total'] / 100 * $a['tax'];
                        $tmptotal_pajak = (int) $pajak_persen;
                    } else {
                        $tmptotal_pajak = $a['tax'];
                    }

                    // cek apakah ada custom pajak
                    $account_pajak = $this->get_account_gratuity($a['gratuity_fkid'], 'tax');
                    if ($account_pajak) {
                        $data_insert['trans_type'] = "2";
                        $data_insert['account_code'] = $account_pajak->account_code;
                        $data_insert['account_name'] = $account_pajak->account_name;
                        $data_insert['type'] = "K";
                        $data_insert['nominal'] = $tmptotal_pajak;
                        $data_insert['account_category_fkid'] = $account_pajak->category_id;
                        $data_insert['description'] = $account_pajak->tax_name;
                        array_push($data_jurnal, $data_insert);
                    } else {
                        $total_pajak += $tmptotal_pajak;
                    }
                }

                $qty_arive += $a['diterima'];

                $saldoProduct = $a['qty_stok'] * $a["price_stok"];
                $data_insert['trans_type'] = "2";
                $data_insert['account_code'] = $this->account('purchase_payment')->account_code;
                $data_insert['account_name'] = $this->account('purchase_payment')->account_name;
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $a['total'];
                $data_insert['account_category_fkid'] = $this->account('purchase_payment')->category_id;
                $data_insert['description'] = $a['product_name'];
                array_push($data_jurnal, $data_insert);
            }

            $data_insert['trans_type'] = "2";
            $data_insert['account_code'] = $this->account('purchase_hutang')->account_code;
            $data_insert['account_name'] = $this->account('purchase_hutang')->account_name;
            $data_insert['type'] = "K";
            $data_insert['nominal'] = $data->grand_total;
            $data_insert['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
            $data_insert['description'] = "Total Hutang";
            array_push($data_jurnal, $data_insert);

            if ($total_pajak > 0) {
                $data_insert['trans_type'] = "2";
                $data_insert['account_code'] = $this->account('purchase_tax')->account_code;
                $data_insert['account_name'] = $this->account('purchase_tax')->account_name;
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $total_pajak;
                $data_insert['account_category_fkid'] = $this->account('purchase_tax')->category_id;
                $data_insert['description'] = "Total Tax";
                array_push($data_jurnal, $data_insert);
            }
            $this->db->insert_batch('finance_jurnal_umum', $data_jurnal);


            // jurnal penerimaan
            if ($qty_arive > 0) {
                $data_confirm = $this->db->select('pc.purchase_confrim_id')
                    ->from('purchase_confrim pc')
                    ->join('purchase_products pp', 'pp.purchase_products_id=pc.purchase_product_fkid')
                    ->where('pp.purchase_fkid', $purchase_id)
                    ->get()->result_array();
                $arr_id_confirm = [];
                if ($data_confirm) {
                    foreach ($data_confirm as $confirm) {
                        // del all purchas confirm history jurnal
                        $this->db->where('trans_type', 5);
                        $this->db->where("SUBSTRING_INDEX(trans_id,'.',-1)", $confirm['purchase_confrim_id']);
                        $this->db->delete('finance_jurnal_umum');

                        array_push($arr_id_confirm, $confirm['purchase_confrim_id']);
                    }
                }
                // print_r($arr_id_confirm);die;
                $this->jurnal_purchase_confirm($arr_id_confirm);
            }

            $milis = current_millis();
            // jurnal pembayaran
            if ($data->bayar > 0) {
                $data_jurnal = [];
                $data_insert['trans_type'] = '14';
                $data_insert['trans_id'] = $purchase_id;
                $data_insert['description'] = $data->invoice;
                $data_insert['outlet_fkid'] = $data->outlet_fkid;
                $data_insert['account_code'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['code'];
                $data_insert['account_name'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['name']; // kas or bank
                $data_insert['type'] = 'K';
                $data_insert['nominal'] = $data->bayar;
                $data_insert['account_category_fkid'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['category_id'];
                array_push($data_jurnal, $data_insert);

                $data_insert['trans_type'] = '14';
                $data_insert['trans_id'] = $purchase_id;
                ;
                $data_insert['description'] = $data->invoice;
                $data_insert['outlet_fkid'] = $data->outlet_fkid;
                $data_insert['account_code'] = $this->account('purchase_hutang')->account_code;
                $data_insert['account_name'] = $this->account('purchase_hutang')->account_name; // hutang usaha
                $data_insert['type'] = 'D';
                $data_insert['nominal'] = $data->bayar;
                $data_insert['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
                array_push($data_jurnal, $data_insert);

                $this->db->insert_batch('finance_jurnal_umum', $data_jurnal);

            }
        }
    }

    public function account_purchase($id_product)
    {
        $priduct = $this->db->select('product_fkid')
            ->from("products_detail")
            ->where("product_detail_id", $id_product)
            ->get()->row();

        $data = $this->db->select('
            fa.code as account_code,
            fa.name as account_name,
            fa.account_category_fkid as category_id
        ')
            ->from('finance_account_setting as')
            ->join('finance_accounts fa', 'fa.account_id=as.account_debit')
            ->where('product_fkid', $priduct->product_fkid)
            ->get()->row();
        $result = [
            'account_code' => $data ? $data->account_code : $this->account('purchase_cogs')->account_code,//'1-03000',
            'account_name' => $data ? $data->account_name : $this->account('purchase_cogs')->account_name,//'Persediaan',
            'category_id' => $data ? $data->category_id : $this->account('purchase_cogs')->category_id//'1',
        ];
        return $result;

    }

    public function get_accunt_product($pd_id)
    {
        $priduct = $this->db->select('product_fkid')
            ->from("products_detail")
            ->where("product_detail_id", $pd_id)
            ->get()->row();

        $data = $this->db->select('
            code,
            name,
            account_category_fkid as category_id
        ')
            ->from('finance_accounts fa')
            ->join('finance_account_setting fas', 'fas.account_persediaan=fa.account_id')
            ->where('fas.product_fkid', $priduct->product_fkid)
            ->get()->row();
        $result = [
            'account_code' => $data ? $data->code : $this->account('persediaan_persediaan')->account_code,//'1-03000',
            'account_name' => $data ? $data->name : $this->account('persediaan_persediaan')->account_name,//'Persediaan',
            'category_id' => $data ? $data->category_id : $this->account('persediaan_persediaan')->category_id//'1',
        ];
        return $result;
    }

    public function get_account_gratuity($pajak_id, $type)
    {
        $this->db->select('
           (select name from finance_accounts where account_id = fag.account_fkid) as account_name,
           (select code from finance_accounts where account_id = fag.account_fkid) as account_code,
           (select account_category_fkid from finance_accounts where account_id = fag.account_fkid) as category_id,
           g.name as tax_name
           
           ')
            ->from('finance_account_general fag')
            ->join('gratuity g', 'g.gratuity_id = fag.type_fkid', 'left')
            ->where('fag.type', 'gratuity')
            ->where('type_fkid', $pajak_id);
        if ($type == 'tax') {
            $this->db->where('g.tax_category', 'tax');
        } else {
            $this->db->where('g.tax_category', 'discount');
        }
        return $this->db->get()->row();
    }

    public function cek_subcribe()
    {
        return $this->db->get_where('system_subscribe', ['admin_fkid' => $this->session->userdata('admin_id'), 'feature' => "finance"])->row();
    }

    public function get_account_category($account_code)
    {
        $data = $this->db->get_where('finance_accounts', ['code' => $account_code])->row();
        if ($data) {
            return $data->account_category_fkid;
        } else {
            return "0";
        }


    }

    public function get_payment($type, $pay_id = null)
    {
        $result = [];
        if ($type == "cash") {
            $pay = $this->account('purchase_cash');
            // print_r($pay);die;
            $result = [
                "code" => $pay->account_code,
                "name" => $pay->account_name,
                "category_id" => $pay->category_id,
                "payment_name" => "cash"
            ];
        } else {
            // cek maping bank ke kode rekening jika tidak ada gunakan devaultnya
            $map_bank = $this->db->select("
                (select name from finance_accounts where account_id = fg.account_fkid) as account_name,
                (select code from finance_accounts where account_id = fg.account_fkid) as account_code,
                (select account_category_fkid from finance_accounts where account_id = fg.account_fkid) as category_id,
                (select name from payment_media_bank where bank_id = fg.type_fkid) as bank_name
            ")
                ->from('finance_account_general fg')
                ->where([
                    'type' => 'payment',
                    'type_fkid' => $pay_id,
                ])
                ->get()->row();

            if ($map_bank) {
                $result = [
                    "code" => $map_bank->account_code,
                    "name" => $map_bank->account_name,
                    "category_id" => $map_bank->category_id,
                    "payment_name" => $map_bank->bank_name
                ];
            } else {
                $pay = $this->account('purchase_cash');
                $result = [
                    "code" => $pay->account_code,
                    "name" => $pay->account_name,
                    "category_id" => $pay->category_id,
                    "payment_name" => "cash"
                ];
            }
        }

        return $result;
    }

    public function group_account($data)
    {
        $groups = array();
        foreach ($data as $item) {
            $key = $item['account_code'];
            if (!array_key_exists($key, $groups)) {
                $groups[$key] = array(
                    'trans_type' => $item['trans_type'],
                    'trans_id' => $item['trans_id'],
                    'account_code' => $item['account_code'],
                    'account_name' => $item['account_name'],
                    'type' => $item['type'],
                    'nominal' => $item['nominal'],
                    'description' => $item['description'],
                    'outlet_fkid' => $item['outlet_fkid'],
                    'admin_fkid' => $item['admin_fkid'],
                    'account_category_fkid' => $item['account_category_fkid'],
                    'trans_created' => $item['trans_created'],
                    'created_at' => $item['created_at'],
                    'updated_at' => $item['updated_at'],
                    'data_status' => $item['data_status']
                );
            } else {
                $groups[$key]['nominal'] = $groups[$key]['nominal'] + $item['nominal'];
            }
        }
        return $groups;
    }

    public function data_purchase($id)
    {
        $purchase = $this->db->get_where('purchase', ["purchase_id" => $id])->row();
        $detail = $this->db->select('
            (select stock_management from products where product_id = pd.product_fkid) as stock_management,
            (select name from products where product_id = pd.product_fkid) as product_name,
            pp.*
        ')
            ->from('purchase_products pp')
            ->join('products_detail pd', 'pd.product_detail_id=pp.products_fkid', 'left')
            ->where('purchase_fkid', $id)
            ->get()->result_array();

        foreach ($detail as $key => $value) {
            $penerimaan = $this->db->select('*')
                ->from("purchase_confrim")
                ->where("purchase_product_fkid", $value['purchase_products_id'])
                ->order_by("purchase_confrim_id", 'asc')
                ->limit(1)->get()->row();
            if ($penerimaan) {
                $detail[$key]['diterima'] = $penerimaan->qty_arive;
            } else {
                $detail[$key]['diterima'] = 0;
            }

        }

        $purchase->detail = $detail;
        return $purchase;
    }

    public function jurnal_purchase_confirm($id_confirm)
    {
        if ($this->cek_subcribe()) {
            $id = implode(",", $id_confirm);
            // print_r($id);die;
            $data = $this->db->select('
                pc.*,
                pp.price_stok as hpp,
                (select stock_management from products where product_id=pd.product_fkid) as stock_management,
                (select outlet_fkid from purchase where purchase_id = pp.purchase_fkid) as outlet_fkid,
                (select invoice from purchase where purchase_id = pp.purchase_fkid) as invoice,
                pp.products_fkid as product_detail_id,
                (select name from products where product_id = pd.product_fkid) as product_name
            ')
                ->from('purchase_confrim pc')
                ->join('purchase_products pp', 'pp.purchase_products_id=pc.purchase_product_fkid', 'left')
                ->join('products_detail pd', 'pd.product_detail_id=pp.products_fkid', 'left')
                ->where('pc.purchase_confrim_id in (' . $id . ')')
                ->get()->result_array();

            // print_r($data);die;

            $data_insert = [
                'trans_type' => "5",
                'trans_id' => '',
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => $data[0]['invoice'],
                'outlet_fkid' => $data[0]['outlet_fkid'],
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => '',
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $milis = current_millis();

            $param = [];
            $total = 0;
            foreach ($data as $key => $value) {
                if ($value['qty_arive'] > 0) {
                    if ($value['stock_management'] == '0') {
                        $data_insert['trans_id'] = $milis . "." . $value['purchase_confrim_id'];
                        $data_insert['account_name'] = $this->account('purchase_cogs')->account_name;
                        $data_insert['account_code'] = $this->account('purchase_cogs')->account_code;
                        $data_insert['type'] = 'D';
                        $data_insert['nominal'] = $value['qty_arive'] * $value['hpp'];
                        $data_insert['account_category_fkid'] = $this->account('purchase_cogs')->category_id;
                        $data_insert['description'] = $value['product_name'];
                        $data_insert['trans_created'] = $value['date_confirm'];
                        array_push($param, $data_insert);
                    } else {
                        // $data_insert['trans_id'] =  $data['purchase_id'].'.'.$id_confirm->total;
                        $data_insert['trans_id'] = $milis . "." . $value['purchase_confrim_id'];
                        $data_insert['account_code'] = $this->get_accunt_product($value['product_detail_id'])['account_code'];
                        $data_insert['account_name'] = $this->get_accunt_product($value['product_detail_id'])['account_name'];
                        $data_insert['type'] = "D";
                        $data_insert['nominal'] = $value['qty_arive'] * $value['hpp'];
                        $data_insert['account_category_fkid'] = $this->get_accunt_product($value['product_detail_id'])['category_id'];
                        $data_insert['description'] = $value['product_name'];
                        $data_insert['trans_created'] = $value['date_confirm'];
                        array_push($param, $data_insert);
                    }
                    // $total += $value['qty_arive']*$value['hpp'];   
                    $data_insert['trans_id'] = $milis . "." . $value['purchase_confrim_id'];
                    $data_insert['account_name'] = $this->account('purchase_payment')->account_name; //"1-06100";
                    $data_insert['account_code'] = $this->account('purchase_payment')->account_code; //"Uang Muka Pembelian";
                    $data_insert['type'] = 'K';
                    $data_insert['account_category_fkid'] = $this->account('purchase_payment')->category_id;
                    $data_insert['nominal'] = $value['qty_arive'] * $value['hpp'];
                    $data_insert['description'] = $data[0]['invoice'];
                    $data_insert['trans_created'] = $value['date_confirm'];
                    array_push($param, $data_insert);
                }
            }


            // print_r($param);die;
            // $grouping = $this->group_account($param);
            $this->db->insert_batch('finance_jurnal_umum', $param);
        }

    }

    public function jurnal_purchase_retur($param)
    {
        if ($this->cek_subcribe()) {
            # code...
            $data_insert = [];
            $data_jurnal = [
                'trans_type' => "2",
                'trans_id' => current_millis() . '.' . $param['purchase_id'],
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => $param['invoice'],
                'outlet_fkid' => $param['outlet_id'],
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => current_millis(),
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $id = implode("','", $param['detail']);
            // print_r($id);die;
            $data = $this->db->select('
                rp.retur_product_id,
                rp.qty_retur,
                (select price_stok from purchase_products where purchase_products_id=rp.purchase_product_fkid) as hpp,
                (select gratuity_fkid from purchase_products where purchase_products_id=rp.purchase_product_fkid) as tax_id,
                (select tax_type from purchase_products where purchase_products_id=rp.purchase_product_fkid) as tax_type,
                (select tax from purchase_products where purchase_products_id=rp.purchase_product_fkid) as tax,
                (select products_fkid from purchase_products where purchase_products_id=rp.purchase_product_fkid) as product_detail_fkid,
                (select qty_nota from purchase_products where purchase_products_id=rp.purchase_product_fkid) as qty_nota,
                (select stock_management from products where product_id = pd.product_fkid) as stock_management
            ')
                ->from("retur_products rp")
                ->join('purchase_products pp', 'pp.purchase_products_id=rp.purchase_product_fkid', 'left')
                ->join('products_detail pd', 'pd.product_detail_id=pp.products_fkid', 'left')
                ->where("retur_product_id in ('" . $id . "')")
                ->get()->result_array();
            // print_r($data);die;
            $hargaRetur = 0;
            $total_pajak = 0;
            foreach ($data as $key) {
                $pajak = $key['tax'];
                if ($key['tax'] > 0) {
                    if ($key['tax_type'] == 'percentage') {
                        $pajak = ($key['hpp'] * $key['tax'] / 100) * $key['qty_retur'];
                    }
                    // $pajak = $pajak/$key['qty_nota']*$key['qty_retur'];
                    $total_pajak += $pajak;
                    // jurnal tax
                    // cari apakah ada cutom pajak
                    // echo $pajak;
                    $account_pajak = $this->get_account_gratuity($key['tax_id'], 'tax');
                    if ($account_pajak) {
                        $data_jurnal['trans_type'] = '3';
                        $data_jurnal['account_code'] = $account_pajak->account_code;
                        $data_jurnal['account_name'] = $account_pajak->account_name;
                        $data_jurnal['type'] = "K";
                        $data_jurnal['nominal'] = $pajak;
                        $data_jurnal['account_category_fkid'] = $account_pajak->category_id;
                        array_push($data_insert, $data_jurnal);
                    } else {
                        // echo $pajak;die;
                        $data_jurnal['trans_type'] = '3';
                        $data_jurnal['account_code'] = $this->account('purchase_tax')->account_code; //"1-06201";
                        $data_jurnal['account_name'] = $this->account('purchase_tax')->account_name; //'PPN Masukan';
                        $data_jurnal['type'] = "K";
                        $data_jurnal['nominal'] = $pajak;
                        $data_jurnal['account_category_fkid'] = $this->account('purchase_tax')->category_id;
                        array_push($data_insert, $data_jurnal);
                    }

                }

                // persediaan
                $hargaRetur += $key['hpp'] * $key['qty_retur'];
                // jika persediaan no
                if ($key['stock_management'] == 0) {
                    $data_jurnal['trans_type'] = '3';
                    $data_jurnal['account_code'] = $this->account_purchase($key['product_detail_fkid'])['account_code'];
                    $data_jurnal['account_name'] = $this->account_purchase($key['product_detail_fkid'])['account_name'];
                    $data_jurnal['type'] = "K";
                    $data_jurnal['nominal'] = $key['hpp'] * $key['qty_retur'];
                    $data_jurnal['account_category_fkid'] = $this->account_purchase($key['product_detail_fkid'])['category_id'];
                    array_push($data_insert, $data_jurnal);
                } else {
                    $data_jurnal['trans_type'] = '3';
                    $data_jurnal['account_code'] = $this->get_accunt_product($key['product_detail_fkid'])['account_code'];
                    $data_jurnal['account_name'] = $this->get_accunt_product($key['product_detail_fkid'])['account_name'];
                    $data_jurnal['type'] = "K";
                    $data_jurnal['nominal'] = $key['hpp'] * $key['qty_retur'];
                    $data_jurnal['account_category_fkid'] = $this->get_accunt_product($key['product_detail_fkid'])['category_id'];
                    array_push($data_insert, $data_jurnal);
                }

            }

            // hutang usaha
            $nominal = $hargaRetur + $total_pajak;
            // echo $nominal;die;
            $data_jurnal['trans_type'] = '3';
            $data_jurnal['account_code'] = $this->account('hutang_usaha')->account_code; //"3-01000";
            $data_jurnal['account_name'] = $this->account('hutang_usaha')->account_name; //"Utang Usaha";
            $data_jurnal['type'] = "D";
            $data_jurnal['nominal'] = $nominal ? $nominal : 0;
            $data_jurnal['account_category_fkid'] = $this->account('hutang_usaha')->category_id;
            array_push($data_insert, $data_jurnal);

            // kreditt memo
            // print_r($data);die;


            // $grouping = $this->group_account($data_insert);           
            $this->db->insert_batch('finance_jurnal_umum', $data_insert);

            // print_r($nominal);
            // print_r($param['sisaTagihan']);die;
            // debit memo
            $dataDebitMemo = [];
            if ($param['kreditMemo'] > 0) {
                $data_jurnal['trans_type'] = "6";
                $data_jurnal['account_code'] = $this->account('purchase_debitmemo')->account_code; //"1-02001";
                $data_jurnal['account_name'] = $this->account('purchase_debitmemo')->account_name; //"Piutang Usaha";
                $data_jurnal['type'] = "D";
                $data_jurnal['nominal'] = $param['kreditMemo'];
                $data_jurnal['account_category_fkid'] = $this->account('purchase_debitmemo')->category_id;
                array_push($dataDebitMemo, $data_jurnal);

                $data_jurnal['trans_type'] = "6";
                $data_jurnal['account_code'] = $this->account('hutang_usaha')->account_code; //"3-01000";
                $data_jurnal['account_name'] = $this->account('hutang_usaha')->account_name; //"Utang Usaha";
                $data_jurnal['type'] = "K";
                $data_jurnal['nominal'] = $param['kreditMemo'];
                $data_jurnal['account_category_fkid'] = $this->account('hutang_usaha')->category_id;
                array_push($dataDebitMemo, $data_jurnal);

                // print_r($dataDebitMemo);die;
                // $this->db->insert_batch('finance_jurnal_umum', array_values($dataDebitMemo));
                // $groupingDebitMemo = $this->group_account($dataDebitMemo);    
                // $this->db->insert_batch('finance_jurnal_umum', array_values($groupingDebitMemo));  
                $this->db->insert_batch('finance_jurnal_umum', $dataDebitMemo);
            }



            if ($param['pengembalian'] != "") {
                $param_refund = [
                    'purchase_fkid' => $param['purchase_id'],
                    'nominal' => $nominal - $param['sisaTagihan'],
                    'payment_type' => $param['pengembalian'] != 'cash' ? 'card' : 'cash',
                    'bank_fkid' => $param['pengembalian'] != 'cash' ? $param['pengembalian'] : '',
                ];
                $this->jurnal_purchase_refund($param_refund);
            }

        }
    }

    public function data_retur($purchase_id)
    {
        $pram = [
            "subRetur" => 0, //sub total retur
            "sisaTagihan" => 0, //sisa hutang - subtotal retur
            "kerditMemo" => 0, //sisa lebih dari jumlah yg dibayar - sub retur
            "purchase_id" => 0,
            "invoice" => '',
            "outlet_id" => 0,
            "pengembalian" => "",
            "detail" => [] //id tabel retur product
        ];
        $data_purchase = $this->db->select('
            p.invoice,
            p.purchase_id,
            p.outlet_fkid,
            pp.purchase_product_id
        ')
            ->form("purchase p")
            ->join("purchase_products pp", "pp.purchase_fkid=p.purchase_id", "left")
            ->where('purchase_id', $purchase_id)->get()->result_array();
        foreach ($data_purchase as $prc) {
            $prc_detail[] = $prc['purchase_product_id'];
        }
        $param = [
            "invoice" => $data_purchase[0]['invoice'],
            "outlet_id" => $data_purchase[0]['outlet_fkid'],
            "purchase_id" => $data_purchase[0]['purchase_id']
        ];
        $data_retur = $this->db->select('*')
            ->from("retur_products")
            ->where_in('purchase_product_fkid', $prc_detail)
            ->group_by('');


    }

    public function update_retur($purchase_id)
    {
        // delete jurnal all retur and debt memoby purchase  id 
        $this->db->where("SUBSTRING_INDEX(trans_id,'.',-1)", $purchase_id);
        $this->db->where_in('trans_type', [3, 6]);
        $this->db->delete('finance_jurnal_umum');        

        $data_retur = $this->db->select('
                rp.retur_product_id,
                rp.qty_retur,
                rp.data_created,
                pp.purchase_fkid,
                pp.price_stok as hpp,
                pp.gratuity_fkid as tax_id,
                pp.tax_type as tax_type,
                pp.tax as tax,
                pp.products_fkid as product_detail_fkid,
                pp.qty_nota as qty_nota,
                (select stock_management from products where product_id = pd.product_fkid) as stock_management,
                (select name from products where product_id = pd.product_fkid) as pd_name,
                p.outlet_fkid as outlet_id,
                p.invoice,
                p.grand_total
            ')
            ->from("retur_products rp")
            ->join('purchase_products pp', 'pp.purchase_products_id=rp.purchase_product_fkid', 'left')
            ->join('purchase p', 'p.purchase_id=pp.purchase_fkid', 'left')
            ->join('products_detail pd', 'pd.product_detail_id=pp.products_fkid', 'left')
            ->where("pp.purchase_fkid", $purchase_id)
            ->get()->result_array();          

        // Process array
        $groupedData = [];
        foreach ($data_retur as $item) {
            // Round data_created to the nearest minute in milliseconds
            $minuteKey = floor($item['data_created'] / 60000) * 60000;
            $item['data_created'] = $minuteKey; // Update data_created to minute precision

            // Group by minuteKey
            if (!isset($groupedData[$minuteKey])) {
                $groupedData[$minuteKey] = [];
            }
            $groupedData[$minuteKey][] = $item;
        }

        
        $data_jurnal = [
            'trans_type' => "2",
            'trans_id' => current_millis() . "." . $data_retur[0]['purchase_fkid'],
            'account_code' => '',
            'account_name' => '',
            'type' => '',
            'nominal' => '',
            'description' => $data_retur[0]['invoice'],
            'outlet_fkid' => $data_retur[0]['outlet_id'],
            'admin_fkid' => $this->session->userdata('admin_id'),
            'account_category_fkid' => '',
            'trans_created' => "",
            'created_at' => current_millis(),
            'updated_at' => current_millis(),
            'data_status' => '1',
            'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
        ];

        if ($data_retur) {
            foreach ($groupedData as $a) {
                $data_insert = [];
                $total_pajak = 0;
                $hargaRetur = 0;
                $data_jurnal['trans_id'] = current_millis() . "." . $data_retur[0]['purchase_fkid'];
                foreach ($a as $key) {
                    $pajak = $key['tax'];
                    $data_jurnal['trans_created'] = $key['data_created'];
                    if ($key['tax'] > 0) {
                        if ($key['tax_type'] == 'percentage') {
                            $pajak = ($key['hpp'] * $key['tax'] / 100) * $key['qty_retur'];
                        }
                        // $pajak = $pajak/$key['qty_nota']*$key['qty_retur'];
                        $total_pajak += $pajak;
                        // jurnal tax
                        // cari apakah ada cutom pajak
                        $account_pajak = $this->get_account_gratuity($key['tax_id'], 'tax');
                        if ($account_pajak) {
                            $data_jurnal['trans_type'] = '3';
                            $data_jurnal['account_code'] = $account_pajak->account_code;
                            $data_jurnal['account_name'] = $account_pajak->account_name;
                            $data_jurnal['type'] = "K";
                            $data_jurnal['nominal'] = $pajak;
                            $data_jurnal['account_category_fkid'] = $account_pajak->category_id;
                            array_push($data_insert, $data_jurnal);
                        } else {
                            // echo $pajak;die;
                            $data_jurnal['trans_type'] = '3';
                            $data_jurnal['account_code'] = $this->account('purchase_tax')->account_code; //"1-06201";
                            $data_jurnal['account_name'] = $this->account('purchase_tax')->account_name; //'PPN Masukan';
                            $data_jurnal['type'] = "K";
                            $data_jurnal['nominal'] = $pajak;
                            $data_jurnal['account_category_fkid'] = $this->account('purchase_tax')->category_id;
                            array_push($data_insert, $data_jurnal);
                        }
                    }
    
                    // persediaan
                    $hargaRetur += $key['hpp'] * $key['qty_retur'];
                    // jika persediaan no
                    if ($key['stock_management'] == 0) {
                        $data_jurnal['trans_type'] = '3';
                        $data_jurnal['account_code'] = $this->account_purchase($key['product_detail_fkid'])['account_code'];
                        $data_jurnal['account_name'] = $this->account_purchase($key['product_detail_fkid'])['account_name'];
                        $data_jurnal['type'] = "K";
                        $data_jurnal['nominal'] = $key['hpp'] * $key['qty_retur'];
                        $data_jurnal['account_category_fkid'] = $this->account_purchase($key['product_detail_fkid'])['category_id'];
                        $data_jurnal['description'] = $key['pd_name'];
                        array_push($data_insert, $data_jurnal);
                    } else {
                        $data_jurnal['trans_type'] = '3';
                        $data_jurnal['account_code'] = $this->get_accunt_product($key['product_detail_fkid'])['account_code'];
                        $data_jurnal['account_name'] = $this->get_accunt_product($key['product_detail_fkid'])['account_name'];
                        $data_jurnal['type'] = "K";
                        $data_jurnal['nominal'] = $key['hpp'] * $key['qty_retur'];
                        $data_jurnal['account_category_fkid'] = $this->get_accunt_product($key['product_detail_fkid'])['category_id'];
                        $data_jurnal['description'] = $key['pd_name'];
                        array_push($data_insert, $data_jurnal);
                    }                   
                }
                // hutang usaha
                $nominal = $hargaRetur + $total_pajak;
                // echo $nominal;die;
                $data_jurnal['description'] = $data_retur[0]['invoice'];
                $data_jurnal['trans_type'] = '3';
                $data_jurnal['account_code'] = $this->account('hutang_usaha')->account_code; //"3-01000";
                $data_jurnal['account_name'] = $this->account('hutang_usaha')->account_name; //"Utang Usaha";
                $data_jurnal['type'] = "D";
                $data_jurnal['nominal'] = $nominal ? $nominal : 0;
                $data_jurnal['account_category_fkid'] = $this->account('hutang_usaha')->category_id;
                array_push($data_insert, $data_jurnal);
                $this->db->insert_batch('finance_jurnal_umum', $data_insert);
                // print_r($data_insert);
            }

            // debit memo
            // mencari debit memo dari setiap transaksi retur
            // debit memo =  selisih antara total bayar pada periode retur - total retur
            //mencari tgl retur paling awal by product fkiid

            //purchase_payment
            $purchase_payment = $this->db->select('bayar')
                ->from("purchase")  
                ->where('purchase_id', $purchase_id)
                ->get()->row();

            $date_retur = $this->db->select("FLOOR(data_created / (1000 * 60)) * (1000 * 60) as data_created,sum(tot_dis) as total_retur")
                ->from("retur_products")
                ->where_in("purchase_product_fkid","(select purchase_products_id from purchase_products where purchase_fkid=".$purchase_id.")",false)
                ->group_by("FLOOR(data_created / (1000 * 60)) * (1000 * 60)")
                ->order_by("data_created", "asc")
                ->get()->result_array();
            
            
            $total_retur = 0;
            $dataDebitMemo = [];
            foreach ($date_retur as $retur_data) {
                $data_jurnal['trans_id'] = current_millis() . "." . $data_retur[0]['purchase_fkid'];
                // mencari total pembayaran dibawah tgl retur sebagai total pembayaran pada setiap tahapan retur
                $data_debt = $this->db->select('sum(nominal) as total')
                    ->from('debt_payment')
                    ->where("time_created<=", $retur_data['data_created'])
                    ->where('purchase_fkid',$purchase_id)
                    ->get()->row();

                $total_retur += $retur_data['total_retur'];
                $hutang = $data_retur[0]['grand_total']-($purchase_payment->bayar + $data_debt->total);
                    $total_payment = $hutang - $total_retur;
                    // print_r($total_payment);
                if ($total_payment < 0) { //total pengembalian lebih besar dari pembayaran
                    $data_jurnal['trans_type'] = "6";
                    $data_jurnal['account_code'] = $this->account('purchase_debitmemo')->account_code; //"1-02001";
                    $data_jurnal['account_name'] = $this->account('purchase_debitmemo')->account_name; //"Piutang Usaha";
                    $data_jurnal['type'] = "D";
                    $data_jurnal['nominal'] = $total_payment * -1;
                    $data_jurnal['trans_created'] =  $retur_data['data_created'];
                    $data_jurnal['account_category_fkid'] = $this->account('purchase_debitmemo')->category_id;
                    array_push($dataDebitMemo, $data_jurnal);

                    $data_jurnal['trans_type'] = "6";
                    $data_jurnal['account_code'] = $this->account('hutang_usaha')->account_code; //"3-01000";
                    $data_jurnal['account_name'] = $this->account('hutang_usaha')->account_name; //"Utang Usaha";
                    $data_jurnal['type'] = "K";
                    $data_jurnal['nominal'] = $total_payment * -1;
                    $data_jurnal['trans_created'] = $retur_data['data_created'];
                    $data_jurnal['account_category_fkid'] = $this->account('hutang_usaha')->category_id;
                    array_push($dataDebitMemo, $data_jurnal);

                    // print_r($dataDebitMemo);die;
                    // $this->db->insert_batch('finance_jurnal_umum', array_values($dataDebitMemo));
                    // $groupingDebitMemo = $this->group_account($dataDebitMemo);    
                    // $this->db->insert_batch('finance_jurnal_umum', array_values($groupingDebitMemo));  
                }
            }
            if ($dataDebitMemo) {
                $this->db->insert_batch('finance_jurnal_umum', $dataDebitMemo);
            }

        }

    }

    public function jurnal_purchase_refund($param)
    {
        $data_purchase = $this->db->get_where('purchase', ['purchase_id' => $param['purchase_fkid']])->row();
        $data_insert = [];
        $data_jurnal = [
            'trans_type' => "15",
            'trans_id' => current_millis() . '.' . $param['purchase_fkid'],
            'account_code' => '',
            'account_name' => '',
            'type' => '',
            'nominal' => '',
            'description' => $data_purchase->invoice,
            'outlet_fkid' => $data_purchase->outlet_fkid,
            'admin_fkid' => $this->session->userdata('admin_id'),
            'account_category_fkid' => '',
            'trans_created' => current_millis(),
            'created_at' => current_millis(),
            'updated_at' => current_millis(),
            'data_status' => '1',
            'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
        ];

        $data_jurnal['account_code'] = $this->get_payment($param['payment_type'], $param['bank_fkid'])['code']; //kas/bank
        $data_jurnal['account_name'] = $this->get_payment($param['payment_type'], $param['bank_fkid'])['name'];
        $data_jurnal['type'] = "D";
        $data_jurnal['nominal'] = $param['nominal'];
        $data_jurnal['account_category_fkid'] = $this->get_payment($param['payment_type'], $param['bank_fkid'])['category_id'];
        array_push($data_insert, $data_jurnal);

        $data_jurnal['account_code'] = $this->account('purchase_debitmemo')->account_code; //piutang usaha
        $data_jurnal['account_name'] = $this->account('purchase_debitmemo')->account_name;
        $data_jurnal['type'] = "K";
        $data_jurnal['nominal'] = $param['nominal'];
        $data_jurnal['account_category_fkid'] = $this->account('purchase_debitmemo')->category_id;
        array_push($data_insert, $data_jurnal);
        $grouping = $this->group_account($data_insert);
        $this->db->insert_batch('finance_jurnal_umum', array_values($grouping));


    }

    // JURNAL OPNAME
    public function jurnal_opname($data)
    {
        if ($this->cek_subcribe()) {
            // print_r($data);die;
            $param = implode("','", $data);
            $dataOpname = $this->db->select('
                opname_id,
                product_detail_fkid as product_detail_id,
                closing,
                (opname-closing) as balance,
                (select outlet_fkid from products_detail where product_detail_id = op.product_detail_fkid) as outlet_id,
            ')
                ->from('stock_opname op')
                ->where("opname_id in ('" . $param . "')")
                ->get()->result_array();
            // echo $this->db->last_query();die;
            // print_r($dataOpname);die;


            $data_jurnal = [
                'trans_type' => "7", //opname
                'trans_id' => "",
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => "",
                'outlet_fkid' => "",
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => current_millis(),
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $dataInsert = array();

            foreach ($dataOpname as $key => $value) {
                $is_expenses = $this->is_expenses($value['product_detail_id']);
                // print_r($is_expenses);die;
                if (!$is_expenses) {
                    // echo "bukan expense";
                    // echo $value['balance'];
                    $data_jurnal['trans_id'] = current_millis() . '.' . $value['opname_id'];
                    $data_jurnal['description'] = $this->get_product_name($value['product_detail_id']);
                    $data_jurnal['outlet_fkid'] = $value['outlet_id'];


                    // $last_trans = $this->get_last($value['product_detail_id']);
                    // $hpp=0;
                    $hpp = $this->get_hpp_opname($value['product_detail_id'], $value['balance']); //cek apakah sudah ada transaksi
                    $tras_cek = $this->trans_cek($value['product_detail_id']);
                    // $price = $this->get_hpp_product($value['product_detail_id']) * $value['balance'];
                    // print_r($price."<br>");
                    // print_r($value['balance']."<br>");
                    // print_r($this->get_hpp_product($value['product_detail_id'])."<br>");
                    // $hppProduct?$hpp=$hppProduct:$hpp=$price; //mencari hpp
                    if ($value['balance'] > 0) { //jika ada sisa

                        $customProduct = $this->custom_accunt($value['product_detail_id']);
                        $data_jurnal['account_code'] = $customProduct['code'];
                        $data_jurnal['account_name'] = $customProduct['name'];
                        $data_jurnal['type'] = "D";
                        $data_jurnal['nominal'] = $hpp;
                        $data_jurnal['account_category_fkid'] = $customProduct['category_id'];
                        array_push($dataInsert, $data_jurnal);

                        if ($tras_cek) { //jika belum ada stok yang masuk
                            $data_jurnal['account_code'] = $this->account('persediaan_umum')->account_code; //"8-00000";
                            $data_jurnal['account_name'] = $this->account('persediaan_umum')->account_name; //"Pendapatan Lain-Lain";
                            $data_jurnal['type'] = "K";
                            $data_jurnal['nominal'] = $hpp;
                            $data_jurnal['account_category_fkid'] = $this->account('persediaan_umum')->category_id;
                            array_push($dataInsert, $data_jurnal);
                        } else {
                            $data_jurnal['account_code'] = $this->account('modal_saham')->account_code; //"7-01000";
                            $data_jurnal['account_name'] = $this->account('modal_saham')->account_name; //"Modal Saham";
                            $data_jurnal['type'] = "K";
                            $data_jurnal['nominal'] = $hpp;
                            $data_jurnal['account_category_fkid'] = $this->account('modal_saham')->category_id;
                            array_push($dataInsert, $data_jurnal);
                        }
                    } else if ($value['balance'] < 0) {// jika ada stok hilang
                        $data_jurnal['account_code'] = $this->get_accunt_product($value['product_detail_id'])['account_code'];
                        $data_jurnal['account_name'] = $this->get_accunt_product($value['product_detail_id'])['account_name'];
                        $data_jurnal['type'] = "K";
                        $data_jurnal['nominal'] = $hpp;
                        $data_jurnal['account_category_fkid'] = $this->get_accunt_product($value['product_detail_id'])['category_id'];
                        array_push($dataInsert, $data_jurnal);

                        $data_jurnal['account_code'] = $this->account('persediaan_umum')->account_code;//"5-00000";
                        $data_jurnal['account_name'] = $this->account('persediaan_umum')->account_name; //"Beban";
                        $data_jurnal['type'] = "D";
                        $data_jurnal['nominal'] = $hpp;
                        $data_jurnal['account_category_fkid'] = $this->account('persediaan_umum')->category_id;
                        array_push($dataInsert, $data_jurnal);
                    }
                }
            }
            // print_r($dataInsert);die;

            if (count($dataInsert) != 0) {
                // $grouping = $this->group_account($dataInsert);
                // print_r($dataInsert);die;
                $this->db->insert_batch('finance_jurnal_umum', $dataInsert);
            }
        }
    }

    //cek product expense atau bukan
    function is_expenses($pd_id)
    {
        $p_id = $this->db->select('product_fkid')
            ->from("products_detail")
            ->where("product_detail_id", $pd_id)->get()->row();

        $data = $this->db->select('*')
            ->from("finance_account_setting fas")
            ->where('product_fkid', $p_id->product_fkid)
            ->get()->row();
        if ($data) {
            return $data->expenses == 1 ? true : false;
        } else {
            return false;
        }

    }

    public function get_product_name($product_detail_id)
    {
        $data = $this->db->select("
            concat(p.name,' ',ifnull(pdv.variant_name,'')) as product_name
        ")
            ->from("products_detail pd")
            ->join("products p", "p.product_id=pd.product_fkid", "left")
            ->join("products_detail_variant pdv", "pd.variant_fkid=pdv.variant_id", "left")
            ->where("pd.product_detail_id", $product_detail_id)
            ->get()->row();
        return $data->product_name;
    }

    // cek apakah sudah ada transaksi
    function trans_cek($product_detail_id)
    {
        $data = $this->db->select('price')
            ->from("stock_price")
            ->where('product_detail_fkid', $product_detail_id)
            ->order_by('stock_price_id', 'desc')
            ->limit(1)
            ->get()->row();
        return $data;
    }

    public function get_hpp_opname($product_detail_id, $balance)
    {
        // echo "balance".$balance."<br>";
        $total_price = 0;
        if ($balance > 0) {
            $data = $this->db->select('price')
                ->from("stock_price")
                ->where('product_detail_fkid', $product_detail_id)
                // ->where("stock_in_source != 'stock_opname'")
                ->order_by('stock_price_id', 'desc')
                ->limit(1)
                ->get()->row();
            if ($data) {
                $price = $data->price;
            } else {
                $price = $this->get_hpp_product($product_detail_id); //jika belum ada transaksi
            }
            $total_price = $price * $balance;
            return $total_price;

        } else {
            $data = $this->db->select("
                product_detail_fkid,
                stock_in,
                stock_out,
                price
            ")
                ->from('stock_price')
                ->where('product_detail_fkid', $product_detail_id)
                ->where('stock_out < stock_in')
                ->order_by('stock_price_id', 'asc')
                // ->where("stock_in_source != 'stock_opname'")
                ->get()->result_array();
            // print_r($data);die;



            // echo "balance=".$balance;
            $balance = $balance * -1; //2
            // print_r("balance=".$balance);
            $total_price = 0;
            if ($data) {
                foreach ($data as $key => $value) {
                    $sisa_stock = $value["stock_in"] - $value["stock_out"];
                    $hpp = 0;
                    switch (true) {
                        case ($balance > $sisa_stock): //jika opnem diatas 0// balance masih sisa
                            $hpp = $sisa_stock * $value['price'];
                            $balance = $balance - $sisa_stock;
                            $sisa_stock -= $sisa_stock;
                            break;
                        case ($balance < $sisa_stock): //jika opnem diatas 0// balance masih sisa
                            $hpp = $balance * $value['price'];
                            $balance -= $balance;
                            $sisa_stock = $sisa_stock - $balance;
                            break;
                    }

                    // print_r("balance=".$balance);
                    // print_r("hpp=".$hpp);

                    $total_price += $hpp;
                }
                return $total_price;
            } else {
                $price = $this->get_hpp_product($product_detail_id);//jika blm ada  transaksi
                $total_price = $price * ($balance * -1);
                return $total_price;
            }
        }
    }

    public function get_hpp_product($product_detail_id)
    {
        $price = $this->db->select('
           price_buy as price,
           ')
            ->from("products_detail")
            ->where('product_detail_id', $product_detail_id)
            ->get()->row();
        return $price->price;
    }

    public function get_last($product_detail_id) //cek apakah pernah opname atau purchase
    {
        // purchase
        $result = false;
        $purchase = $this->db->select('*')
            ->from('purchase_products')
            ->where('products_fkid', $product_detail_id)
            ->get()->row();

        $opname = $this->db->select('*')
            ->from('stock_opname')
            ->where('product_detail_fkid', $product_detail_id)
            ->get()->result_array();

        if (!$purchase && count($opname) > 1) {
            $result = false;
        } else {
            $result = true;
        }
        print_r($result);
        return $result;
    }


    public function custom_accunt($product_detail_id)
    {
        $priduct = $this->db->select('product_fkid')
            ->from("products_detail")
            ->where("product_detail_id", $product_detail_id)
            ->get()->row();
        $data = $this->db->select('
            code,
            name,
            account_category_fkid as category
        ')
            ->from('finance_accounts fa')
            ->join('finance_account_setting fas', 'fas.account_persediaan=fa.account_id')
            ->where('fas.product_fkid', $priduct->product_fkid)
            ->get()->row();
        // print_r($data);die;
        $result = [
            'code' => $data ? $data->code : $this->account('persediaan_persediaan')->account_code,//'1-03000',
            'name' => $data ? $data->name : $this->account('persediaan_persediaan')->account_name,//'Persediaan',
            'category_id' => $data ? $data->category : $this->account('persediaan_persediaan')->category_id//'1',
        ];
        return $result;

    }

    public function account($key)
    {
        $account = $this->db->select('
            (select account_id from finance_accounts where account_id = fg.account_fkid) as account_id,
            (select name from finance_accounts where account_id = fg.account_fkid) as account_name,
            (select code from finance_accounts where account_id = fg.account_fkid) as account_code,
            (select account_category_fkid from finance_accounts where account_id = fg.account_fkid) as category_id
        ')
            ->from('finance_account_general fg')
            ->join("finance_default_key fk", "fk.id=fg.default_key_fkid")
            ->where([
                'fg.admin_fkid' => $this->session->userdata('admin_id'),
                'fg.type' => 'general',
                'fk.idx_key' => $key
            ])
            ->get()->row();
        return $account;

    }

    public function jurnal_oprasional($id, $timzone = null)
    {
        if ($this->cek_subcribe()) {
            $timzone ?: $timzone = -25200;

            $data_operasional = $this->db->get_where('operationalcost', ['operationalcost_id' => $id])->row();
            $data_jurnal = [
                'trans_type' => "13",
                'trans_id' => current_millis() . '.' . $data_operasional->operationalcost_id,
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => 0,
                'description' => $data_operasional->opcost_name,
                'outlet_fkid' => $data_operasional->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => date_to_milis($data_operasional->data_created, $timzone),
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $data_insert = [];
            // get custom accunt prc category
            $data_jurnal['account_code'] = $this->account_prc_category($data_operasional->prc_category_fkid)->account_code;
            $data_jurnal['account_name'] = $this->account_prc_category($data_operasional->prc_category_fkid)->account_name;
            $data_jurnal['type'] = "D";
            $data_jurnal['nominal'] = $data_operasional->total;
            $data_jurnal['account_category_fkid'] = $this->account_prc_category($data_operasional->prc_category_fkid)->category_id;
            array_push($data_insert, $data_jurnal);

            // pembayaran
            $payment_amount = $data_operasional->total - $data_operasional->payment_amount;
            // jika hutang
            if ($data_operasional->total > $data_operasional->payment_amount) {
                $data_jurnal['account_code'] = $this->account('hutang_biaya')->account_code; //"3-01000";
                $data_jurnal['account_name'] = $this->account('hutang_biaya')->account_name; //"Utang Usaha";
                $data_jurnal['type'] = "K";
                $data_jurnal['nominal'] = $payment_amount;
                $data_jurnal['account_category_fkid'] = $this->account('hutang_biaya')->category_id;
                array_push($data_insert, $data_jurnal);
            }
            $data_jurnal['account_code'] = $this->get_payment($data_operasional->payment, $data_operasional->payment_bank_fkid)['code'];
            $data_jurnal['account_name'] = $this->get_payment($data_operasional->payment, $data_operasional->payment_bank_fkid)['name'];
            $data_jurnal['type'] = "K";
            $data_jurnal['nominal'] = $data_operasional->payment_amount;
            $data_jurnal['account_category_fkid'] = $this->get_payment($data_operasional->payment, $data_operasional->payment_bank_fkid)['category_id'];
            array_push($data_insert, $data_jurnal);

            // insert jurnal operasional
            $this->db->insert_batch('finance_jurnal_umum', $data_insert);
        }
    }

    public function account_prc_category($id)
    {
        $data = $this->db->select('
            (select name from finance_accounts where account_id = fg.account_fkid) as account_name,
            (select code from finance_accounts where account_id = fg.account_fkid) as account_code,
            (select account_category_fkid from finance_accounts where account_id = fg.account_fkid) as category_id,
        ')
            ->from('finance_account_general fg')
            ->where('fg.type_fkid', $id)
            ->where('fg.type', 'prc_category')
            ->get()->row();

        if (empty($data)) {
            $data = $this->account('purchase_operasional');
        }

        return $data;

    }

    public function account_by_id($id)
    {
        return $this->db->select('
            fa.account_category_fkid as category_id,
            fa.code,
            fa.name
        ')
            ->from("finance_accounts fa")
            ->where("account_id", $id)
            ->get()->row();

    }

    // jurnal debt payment
    public function jurnal_debit_pay($id)
    {
        if ($this->cek_subcribe()) {
            $data = $this->db->select('
                (select invoice from purchase where purchase_id = dp.purchase_fkid) as invoice,
                (select outlet_fkid from purchase where purchase_id = dp.purchase_fkid) as outlet_id,
                dp.*
            ')
                ->from('debt_payment dp')
                ->where('dp.debt_payment_id', $id)
                ->get()->row();
            // print_r($data);die;

            $outlet_op = $this->db->select('outlet_fkid,operationalcost_id')
                ->from('operationalcost o')
                ->where("o.operationalcost_id = (select operational_fkid from debt_payment where debt_payment_id=" . $id . ")")->get()->row();



            $data_jurnal = [
                'trans_type' => "14",
                'trans_id' => current_millis() . "." . $data->debt_payment_id,
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => $data->invoice,
                'outlet_fkid' => $data->outlet_id,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => $data->time_created,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $data_insert = [];

            if ($data->trans_type == 'purchase') {
                $data_jurnal['account_code'] = $this->get_payment($data->payment, $data->payment_bank_fkid)['code'];
                $data_jurnal['account_name'] = $this->get_payment($data->payment, $data->payment_bank_fkid)['name']; // kas or bank
                $data_jurnal['type'] = 'K';
                $data_jurnal['nominal'] = $data->nominal;
                $data_jurnal['account_category_fkid'] = $this->get_payment($data->payment, $data->payment_bank_fkid)['category_id'];
                array_push($data_insert, $data_jurnal);

                $data_jurnal['account_code'] = $this->account('purchase_hutang')->account_code;
                $data_jurnal['account_name'] = $this->account('purchase_hutang')->account_name; // hutang usaha
                $data_jurnal['type'] = 'D';
                $data_jurnal['nominal'] = $data->nominal;
                $data_jurnal['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
                array_push($data_insert, $data_jurnal);
            } else {
                // payment operational
                $data_jurnal['account_code'] = $this->account('hutang_biaya')->account_code;
                ;
                $data_jurnal['account_name'] = $this->account('hutang_biaya')->account_name;
                $data_jurnal['type'] = 'D';
                $data_jurnal['outlet_fkid'] = $outlet_op->outlet_fkid;
                $data_jurnal['description'] = 'OP-' . $outlet_op->operationalcost_id;
                $data_jurnal['nominal'] = $data->nominal;
                $data_jurnal['account_category_fkid'] = $this->account('hutang_biaya')->category_id;
                array_push($data_insert, $data_jurnal);

                $data_jurnal['account_code'] = $this->get_payment($data->payment, $data->payment_bank_fkid)['code'];
                $data_jurnal['account_name'] = $this->get_payment($data->payment, $data->payment_bank_fkid)['name']; // kas or bank
                $data_jurnal['type'] = 'K';
                $data_jurnal['outlet_fkid'] = $outlet_op->outlet_fkid;
                $data_jurnal['description'] = 'OP-' . $outlet_op->operationalcost_id;
                $data_jurnal['nominal'] = $data->nominal;
                $data_jurnal['account_category_fkid'] = $this->get_payment($data->payment, $data->payment_bank_fkid)['category_id'];
                array_push($data_insert, $data_jurnal);

            }


            $this->db->insert_batch('finance_jurnal_umum', $data_insert);
        }
    }

    public function jurnal_asset($aset_id, $accout_old = null)
    {
        $data = $this->db->select('
            a.asset_id,
            a.akumulasi_susut,
            a.account_aset_tetap_fkid as accout_aset_tetap,
            a.account_credit_fkid as account_credit,
            a.biaya_akuisisi,
            a.depreciation,
            a.tgl_susut,
            a.tgl_akuisisi,
            a.purchase_fkid,
            (select name from products where product_id = pd.product_fkid) as asset_name,
            pd.outlet_fkid,
            (select name from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_name,
            (select code from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_category,

            (select name from finance_accounts where account_id = a.account_credit_fkid) as account_credit_name,
            (select code from finance_accounts where account_id = a.account_credit_fkid) as account_credit_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_credit_fkid) as account_credit_category,

            (select name from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_name,
            (select code from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_category,
            
        ')
            ->from('assets a')
            ->join('products_detail pd', 'pd.product_detail_id = a.product_detail_fkid', 'left')
            ->where('a.asset_id', $aset_id)
            ->get()->row();

        $data_jurnal = [
            'trans_id' => current_millis() . "." . $data->asset_id,
            'type' => '',
            'nominal' => "",
            'description' => $data->asset_name,
            'outlet_fkid' => $data->outlet_fkid,
            'admin_fkid' => $this->session->userdata('admin_id'),
            'account_category_fkid' => $data->account_aset_category,
            'trans_created' => $data->tgl_akuisisi,
            'created_at' => current_millis(),
            'updated_at' => current_millis(),
            'data_status' => '1',
            'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
        ];

        $data_save = [];
        // jika data berasalal dari purchasing
        $data_jurnal['account_code'] = $data->account_aset_code;
        $data_jurnal['account_name'] = $data->account_aset_name;
        $data_jurnal['nominal'] = $data->biaya_akuisisi;
        $data_jurnal['account_category_fkid'] = $data->account_aset_category;
        $data_jurnal['type'] = "D";
        array_push($data_save, $data_jurnal);

        if (!empty($data->purchase_fkid)) {
            $data_jurnal['account_code'] = $this->account_by_id($accout_old)->code;
            $data_jurnal['account_name'] = $this->account_by_id($accout_old)->name;
            $data_jurnal['nominal'] = $data->biaya_akuisisi;
            $data_jurnal['account_category_fkid'] = $this->account_by_id($accout_old)->category_id;
            $data_jurnal['type'] = "k";
        } else {
            $data_jurnal['account_code'] = $data->account_credit_code;
            $data_jurnal['account_name'] = $data->account_credit_name;
            $data_jurnal['nominal'] = $data->biaya_akuisisi;
            $data_jurnal['account_category_fkid'] = $data->account_credit_category;
            $data_jurnal['type'] = "K";
        }
        array_push($data_save, $data_jurnal);
        // print_r($data_save);die;
        $this->db->insert_batch('finance_jurnal_umum', $data_save);

        // insert penyusutan
        if ($data->akumulasi_susut > 0 && $data->depreciation == 1) {
            $data_susut = [];
            $tmp_susut = [
                'trans_type' => "17",
                'trans_id' => current_millis() . "." . $data->asset_id,
                'account_code' => $data->account_credit_code, // ekuitas saldo awal,
                'account_name' => $data->account_credit_name,
                'type' => 'D',
                'nominal' => $data->akumulasi_susut,
                'description' => $data->asset_name,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => $data->account_credit_category,
                'trans_created' => $data->tgl_susut,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1'
            ];
            array_push($data_susut, $tmp_susut);
            $tmp_susut = [
                'trans_type' => "17",
                'trans_id' => current_millis() . "." . $data->asset_id,
                'account_code' => $data->account_total_code, // ekuitas saldo awal,
                'account_name' => $data->account_total_name,
                'type' => 'K',
                'nominal' => $data->akumulasi_susut,
                'description' => $data->asset_name,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => $data->account_total_category,
                'trans_created' => $data->tgl_susut,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1'
            ];
            array_push($data_susut, $tmp_susut);
            $this->db->insert_batch('finance_jurnal_umum', $data_susut);
        }

    }

    public function add_to_jurnal_purchase_assets($purchase_id)
    {
        if ($this->cek_subcribe()) {
            $data = $this->data_purchase($purchase_id);
            $data_jurnal = [];
            $data_insert = [
                'trans_type' => "18",
                'trans_id' => $purchase_id,
                'account_code' => '',
                'account_name' => '',
                'type' => '',
                'nominal' => '',
                'description' => $data->invoice,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => '',
                'trans_created' => current_millis(),
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            $sisa = $data->grand_total - $data->bayar;
            $payment = $data->bayar;
            if ($sisa < $data->grand_total) {
                $payment = $data->grand_total;
            }
            // PAYMENT
            if ($data->hutang != 0 && $data->bayar == 0) { //full hutang
                $data_insert['account_code'] = $this->account('purchase_hutang')->account_code;
                $data_insert['account_name'] = $this->account('purchase_hutang')->account_name;
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $data->hutang;
                $data_insert['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
                $data_insert['description'] = 'Hutang Invoice ' . $data->invoice;
                array_push($data_jurnal, $data_insert);
            }

            if ($data->hutang == 0 && $data->bayar != 0) { //lunas
                $data_insert['account_code'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['code'];
                $data_insert['account_name'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['name'];
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $payment;
                $data_insert['account_category_fkid'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['category_id'];
                $data_insert['description'] = 'Payment Invoice ' . $data->invoice;
                array_push($data_jurnal, $data_insert);
            }

            if ($data->hutang != 0 && $data->bayar != 0) { //lunas dibayar sebagian
                $data_insert['account_code'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['code'];
                $data_insert['account_name'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['name'];
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $data->bayar;
                $data_insert['account_category_fkid'] = $this->get_payment($data->pay_type, $data->payment_media_bank_fkid)['category_id'];
                $data_insert['description'] = 'Payment Invoice ' . $data->invoice;
                array_push($data_jurnal, $data_insert);

                $data_insert['account_code'] = $this->account('purchase_hutang')->account_code;
                $data_insert['account_name'] = $this->account('purchase_hutang')->account_name;
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $data->hutang;
                $data_insert['account_category_fkid'] = $this->account('purchase_hutang')->category_id;
                $data_insert['description'] = 'Hutang Invoice ' . $data->invoice;
                array_push($data_jurnal, $data_insert);
            }



            // PERSEDIAAN
            $total_beban = 0;
            $total_pajak = 0;
            $total_discount = 0;
            $saldoProduct = 0;
            $discDiterima = 0;
            $data_assets = [];
            $this->db->trans_start();
            foreach ($data->detail as $a) {
                // PAJAK
                $tmptotal_pajak = 0;
                if ($a['tax'] > 0) {
                    if ($a['tax_type'] == "percentage") {
                        $pajak_persen = $a['total'] / 100 * $a['tax'];
                        $tmptotal_pajak = (int) $pajak_persen;
                    } else {
                        $tmptotal_pajak = $a['tax'];
                    }
                    // cek apakah ada custom pajak
                    $account_pajak = $this->get_account_gratuity($a['gratuity_fkid'], 'tax');
                    if ($account_pajak) {
                        $data_insert['account_code'] = $account_pajak->account_code;
                        $data_insert['account_name'] = $account_pajak->account_name;
                        $data_insert['type'] = "K";
                        $data_insert['nominal'] = $tmptotal_pajak;
                        $data_insert['account_category_fkid'] = $account_pajak->category_id;
                        $data_insert['description'] = $account_pajak->tax_name;
                        array_push($data_jurnal, $data_insert);
                    } else {
                        $total_pajak += $tmptotal_pajak;
                    }
                }

                $data_insert['account_code'] = $this->account('purchase_asset')->account_code;
                $data_insert['account_name'] = $this->account('purchase_asset')->account_name;
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $a['diterima'] * $a["price_stok"];
                $data_insert['account_category_fkid'] = $this->account('purchase_asset')->category_id;
                $data_insert['description'] = $a['product_name'];
                array_push($data_jurnal, $data_insert);

                // insert data aset
                $tmp = [
                    "product_detail_fkid" => $a['products_fkid'],
                    "tgl_akuisisi" => $data->data_created,
                    "biaya_akuisisi" => $a['tot_dis'],
                    "account_aset_tetap_fkid" => $this->account('purchase_asset')->account_id,
                    "purchase_fkid" => $purchase_id,
                    "qty" => $a['qty_nota'],
                    "asset_code" => current_millis(),
                    "created_at" => current_millis(),
                ];
                array_push($data_assets, $tmp);
            }

            $this->db->insert_batch('assets', $data_assets);


            if ($total_pajak > 0) {
                $data_insert['account_code'] = $this->account('purchase_tax')->account_code;
                $data_insert['account_name'] = $this->account('purchase_tax')->account_name;
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $total_pajak;
                $data_insert['account_category_fkid'] = $this->account('purchase_tax')->category_id;
                $data_insert['description'] = "Total Tax";
                array_push($data_jurnal, $data_insert);
                // $this->db->insert('finance_jurnal_umum', $data_insert);
            }

            $grouping = $this->group_account($data_jurnal);
            $this->db->insert_batch('finance_jurnal_umum', $data_jurnal);

            // DEBIT MEMO JIKA PEMBAYARAN LEBIH
            $data_insert['trans_type'] = "6";
            $data_insert['trans_id'] = current_millis() . "." . $purchase_id;
            $hutang = $data->grand_total - $data->bayar;
            $dataDebitMemo = [];
            if ($hutang < 0) {
                $data_insert['account_code'] = $this->account('purchase_debitmemo')->account_code;  //"1-02001";
                $data_insert['account_name'] = $this->account('purchase_debitmemo')->account_name; //"Piutang Usaha";
                $data_insert['type'] = "D";
                $data_insert['nominal'] = $hutang * -1;
                $data_insert['account_category_fkid'] = $this->account('purchase_debitmemo')->category_id;
                array_push($dataDebitMemo, $data_insert);

                $data_insert['account_code'] = $this->account('hutang_usaha')->account_code; //"3-01000";
                $data_insert['account_name'] = $this->account('hutang_usaha')->account_name; //"Utang Usaha";
                $data_insert['type'] = "K";
                $data_insert['nominal'] = $hutang * -1;
                $data_insert['account_category_fkid'] = $this->account('hutang_usaha')->category_id;
                array_push($dataDebitMemo, $data_insert);
                // $this->db->insert('finance_jurnal_umum', $dataDebitMemo);    
                // $dataInserDebtMemo = $this->group_account($dataDebitMemo);           
                $this->db->insert_batch('finance_jurnal_umum', array_values($dataDebitMemo));
            }
            $this->db->trans_complete();
        }
    }

    public function catat_jurnal_susut($param)
    {
        $data = $this->db->select('
            a.asset_id,
            a.account_credit_fkid as account_credit,
            a.akumulasi_susut,
            a.account_aset_tetap_fkid as accout_aset_tetap,
            a.biaya_akuisisi,
            a.depreciation,
            a.tgl_susut,
            a.tgl_akuisisi,
            a.purchase_fkid,
            pd.outlet_fkid,
            (select name from products where product_id = pd.product_fkid) as asset_name,
            (select name from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_name,
            (select code from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_category,

            (select name from finance_accounts where account_id = a.account_credit_fkid) as account_credit_name,
            (select code from finance_accounts where account_id = a.account_credit_fkid) as account_credit_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_credit_fkid) as account_credit_category,

            (select name from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_name,
            (select code from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_category,
            
        ')
            ->from('assets a')
            ->join('products_detail pd', 'pd.product_detail_id = a.product_detail_fkid', 'left')
            ->where('a.asset_id', $param['asset_id'])
            ->get()->row();

        // echo $this->db->last_query();die;
        $date = current_millis();
        $data_jurnal = [
            'trans_type' => "16",
            'trans_id' => $date . "." . $data->asset_id,
            'account_code' => "",
            'account_name' => "",
            'type' => 'K',
            'nominal' => "",
            'description' => $data->asset_name,
            'outlet_fkid' => $data->outlet_fkid,
            'admin_fkid' => $this->session->userdata('admin_id'),
            'account_category_fkid' => "",
            'trans_created' => current_millis(),
            'created_at' => $date,
            'updated_at' => $date,
            'data_status' => '1',
            'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
        ];

        // penyusutan
        // $nominal = 

        //jika ada purchase
        if (empty($data->purchase_fkid)) {
            $data_insert = [];
            $data_jurnal['account_code'] = $this->account_by_id($param['akun_asset'])->code;
            $data_jurnal['account_name'] = $this->account_by_id($param['akun_asset'])->name;
            $data_jurnal['account_category_fkid'] = $this->account_by_id($param['akun_asset'])->category_id;
            $data_jurnal['nominal'] = $data->biaya_akuisisi;
            $data_jurnal['description'] = $data->asset_name;
            array_push($data_insert, $data_jurnal);

            $data_jurnal['account_code'] = $this->account_by_id($data->accout_aset_tetap)->code;
            $data_jurnal['account_name'] = $this->account_by_id($data->accout_aset_tetap)->name;
            $data_jurnal['account_category_fkid'] = $this->account_by_id($data->accout_aset_tetap)->category_id;
            $data_jurnal['nominal'] = $data->biaya_akuisisi;
            $data_jurnal['description'] = $data->asset_name;
            array_push($data_insert, $data_jurnal);
            $this->db->insert_batch('finance_jurnal_umum', $data_insert);
        }

        // insert penyusutan
        if ($data->akumulasi_susut > 0 && $data->depreciation == 1) {
            $data_susut = [];
            $tmp_susut = [
                'trans_type' => "17",
                'trans_id' => current_millis() . "." . $data->asset_id,
                'account_code' => $data->account_credit_code, // ekuitas saldo awal,
                'account_name' => $data->account_credit_name,
                'type' => 'D',
                'nominal' => $data->akumulasi_susut,
                'description' => $data->asset_name,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => $data->account_credit_category,
                'trans_created' => $data->tgl_susut,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1'
            ];
            array_push($data_susut, $tmp_susut);
            $tmp_susut = [
                'trans_type' => "17",
                'trans_id' => current_millis() . "." . $data->asset_id,
                'account_code' => $data->account_total_code, // ekuitas saldo awal,
                'account_name' => $data->account_total_name,
                'type' => 'K',
                'nominal' => $data->akumulasi_susut,
                'description' => $data->asset_name,
                'outlet_fkid' => $data->outlet_fkid,
                'admin_fkid' => $this->session->userdata('admin_id'),
                'account_category_fkid' => $data->account_total_category,
                'trans_created' => $data->tgl_susut,
                'created_at' => current_millis(),
                'updated_at' => current_millis(),
                'data_status' => '1'
            ];
            array_push($data_susut, $tmp_susut);
            $this->db->insert_batch('finance_jurnal_umum', $data_susut);
        }
    }

    public function jurnal_sales_asset($sales_id)
    {
        $data_sales = $this->db->select('
            ast.asset_fkid,
            (select name from products where product_id = pd.product_fkid) as asset_name,
            pd.outlet_fkid,
            ast.payment,
            ast.bank_fkid,
            ast.price,
            ast.hpp,
            ast.perolehan,
            ast.qty,
            ast.nilai_buku,
            ast.akumulasi,
            (select name from finance_accounts where account_id = a.account_total_susut_fkid) as name_akumulasi,
            (select code from finance_accounts where account_id = a.account_total_susut_fkid) as code_akumulasi,
            (select account_category_fkid from finance_accounts where account_id = a.account_total_susut_fkid) as category_akumulasi,
            
            (select name from finance_accounts where account_id = a.account_aset_tetap_fkid) as name_asset,
            (select code from finance_accounts where account_id = a.account_aset_tetap_fkid) as code_asset,
            (select account_category_fkid from finance_accounts where account_id = a.account_aset_tetap_fkid) as category_asset,

        ')
            ->from("assets_sales ast")
            ->join("assets a", "ast.asset_fkid=a.asset_id", "left")
            ->join("products_detail pd", "pd.product_detail_id=a.product_detail_fkid", "left")
            ->where("ast.asset_sales_id", $sales_id)
            ->get()->row();

        $date = current_millis();
        $data_jurnal = [];
        $data_insert = [
            'trans_type' => "19", //sales assets
            'trans_id' => $date . "." . $data_sales->asset_fkid,
            'account_code' => "",
            'account_name' => "",
            'type' => 'K',
            'nominal' => "",
            'description' => $data_sales->asset_name,
            'outlet_fkid' => $data_sales->outlet_fkid,
            'admin_fkid' => $this->session->userdata('admin_id'),
            'account_category_fkid' => "",
            'trans_created' => current_millis(),
            'created_at' => $date,
            'updated_at' => $date,
            'data_status' => '1',
            'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
        ];
        // payment kas/bank
        $data_insert['account_code'] = $this->get_payment($data_sales->payment, $data_sales->bank_fkid)['code'];
        $data_insert['account_name'] = $this->get_payment($data_sales->payment, $data_sales->bank_fkid)['name'];
        $data_insert['type'] = "D";
        $data_insert['nominal'] = $data_sales->price * $data_sales->qty;
        $data_insert['account_category_fkid'] = $this->get_payment($data_sales->payment, $data_sales->bank_fkid)['category_id'];
        $data_insert['description'] = 'Payment';
        array_push($data_jurnal, $data_insert);

        // akumulasi penyusutan
        $data_insert['account_code'] = $data_sales->code_akumulasi;
        $data_insert['account_name'] = $data_sales->name_akumulasi;
        $data_insert['type'] = "D";
        $data_insert['nominal'] = $data_sales->hpp;
        $data_insert['account_category_fkid'] = $data_sales->category_akumulasi;
        $data_insert['description'] = 'akumulasi penyusutan';
        array_push($data_jurnal, $data_insert);

        // account asset
        $data_insert['account_code'] = $data_sales->code_asset;
        $data_insert['account_name'] = $data_sales->name_asset;
        $data_insert['type'] = "K";
        $data_insert['nominal'] = $data_sales->perolehan;
        $data_insert['account_category_fkid'] = $data_sales->category_asset;
        $data_insert['description'] = 'penjualan ' . $data_sales->asset_name;
        array_push($data_jurnal, $data_insert);

        // laba atau rugi
        // pendapan or beban lain2
        $harga_jual = $data_sales->price * $data_sales->qty;
        $total = $data_sales->perolehan - $harga_jual - $data_sales->akumulasi;
        if ($harga_jual < $data_sales->nilai_buku) { //rugi
            $data_insert['account_code'] = $this->account('defult_beban')->account_code;
            $data_insert['account_name'] = $this->account('defult_beban')->account_name;
            $data_insert['type'] = "D";
            $data_insert['nominal'] = $total > 0 ? $total : $total * -1;
            $data_insert['account_category_fkid'] = $this->account('defult_beban')->category_id;
            array_push($data_jurnal, $data_insert);
        } else {//untung
            $data_insert['account_code'] = $this->account('dafault_pendapatan')->account_code;
            $data_insert['account_name'] = $this->account('dafault_pendapatan')->account_name;
            $data_insert['type'] = "K";
            $data_insert['nominal'] = $total > 0 ? $total : $total * -1;
            $data_insert['account_category_fkid'] = $this->account('dafault_pendapatan')->category_id;
            array_push($data_jurnal, $data_insert);
        }
        // print_r($data_jurnal);die;
        $grouping = $this->group_account($data_jurnal);
        $this->db->insert_batch('finance_jurnal_umum', $data_jurnal);

    }

    public function posting_penyusutan($penyusutan_id)
    {
        $data = $this->db->select('
            a.asset_id,
            a.account_credit_fkid as account_credit,
            a.akumulasi_susut,
            a.account_aset_tetap_fkid as accout_aset_tetap,
            pd.outlet_fkid,
            ap.nominal,
            ap.created_at,
            (select name from products where product_id = pd.product_fkid) as asset_name,
            (select name from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_name,
            (select code from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_aset_tetap_fkid) as account_aset_category,

            (select name from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_name,
            (select code from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_total_susut_fkid) as account_total_category,

            (select name from finance_accounts where account_id = a.account_susut_fkid) as account_susut_name,
            (select code from finance_accounts where account_id = a.account_susut_fkid) as account_susut_code,
            (select account_category_fkid from finance_accounts where account_id = a.account_susut_fkid) as account_susut_category,
            
        ')
            ->from('assets a')
            ->join('products_detail pd', 'pd.product_detail_id = a.product_detail_fkid', 'left')
            ->join("assets_penyusutan ap", "ap.asset_fkid=a.asset_id", "left")
            ->where('ap.penyusutan_id', $penyusutan_id)
            ->get()->row();



        $date = current_millis();
        $data_jurnal = [
            'trans_type' => "17",
            'trans_id' => $date . "." . $data->asset_id,
            'account_code' => "",
            'account_name' => "",
            'type' => '',
            'nominal' => "",
            'description' => "",
            'outlet_fkid' => $data->outlet_fkid,
            'admin_fkid' => $this->session->userdata('admin_id'),
            'account_category_fkid' => "",
            'trans_created' => $data->created_at,
            'created_at' => $date,
            'updated_at' => $date,
            'data_status' => '1',
            'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
        ];

        $data_insert = [];

        // biaya penyusutan
        $data_jurnal["account_code"] = $data->account_susut_code;
        $data_jurnal["account_name"] = $data->account_susut_name;
        $data_jurnal["type"] = "D";
        $data_jurnal["nominal"] = $data->nominal;
        $data_jurnal["description"] = $data->asset_name;
        $data_jurnal["account_category_fkid"] = $data->account_susut_category;
        array_push($data_insert, $data_jurnal);
        // akumulasi penyusutan
        $data_jurnal["account_code"] = $data->account_total_code;
        $data_jurnal["account_name"] = $data->account_total_name;
        $data_jurnal["type"] = "K";
        $data_jurnal["nominal"] = $data->nominal;
        $data_jurnal["description"] = "akumulasi " . $data->asset_name;
        $data_jurnal["account_category_fkid"] = $data->account_total_category;
        array_push($data_insert, $data_jurnal);
        $this->db->insert_batch('finance_jurnal_umum', $data_insert);

    }

    function set_jurnal_transfer_out($transfer_id)
    {
        if ($this->cek_subcribe()) {
            $data_transfer = $this->db->select('
                tp.date_created,
                tp.product_detail_des_fkid as pd_id,
                tc.qty_confirm as qty,
                tp.transfer_fkid as transfer_id,
                (select name from products where product_id = pd.product_fkid) as product_name,
                t.outlet_origin_fkid as outlet_id,
                pd.price_buy as hpp,
                t.grand_total,
                t.bank_origin,
                t.payment,
                ifnull((select name from payment_media_bank where bank_id=t.bank_origin),"") as bank_name
        
            ')
                ->from("transfer_products tp")
                ->join("transfer t", "t.transfer_id=tp.transfer_fkid", "left")
                ->join("transfer_confirm tc", "tc.transfer_product_fkid=tp.transfer_product_id", "left")
                ->join("products_detail pd", "pd.product_detail_id=tp.product_detail_fkid", "left")
                ->where("tp.transfer_fkid", $transfer_id)
                ->get()->result_array();
            // print_r($data_transfer);die;
            $date = current_millis();
            $tmp = [
                'trans_type' => "20", //sales assets
                'trans_id' => $data_transfer[0]['transfer_id'],
                'account_code' => "",
                'account_name' => "",
                'type' => '',
                'nominal' => "",
                'description' => "",
                'account_category_fkid' => "",
                'outlet_fkid' => $data_transfer[0]['outlet_id'],
                'admin_fkid' => $this->session->userdata('admin_id'),
                'trans_created' => current_millis(),
                'created_at' => $date,
                'updated_at' => $date,
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];


            // data jurnal
            $data_insert = [];
            $grand_total = 0;
            // persediaan bahan jadi
            foreach ($data_transfer as $key => $value) {
                $tmp["account_code"] = $this->get_accunt_product($value['pd_id'])['account_code'];
                $tmp["account_name"] = $this->get_accunt_product($value['pd_id'])['account_name'];
                $tmp["type"] = "K";
                $tmp["account_category_fkid"] = $this->get_accunt_product($value['pd_id'])['category_id'];
                $tmp["nominal"] = $value["qty"] * $value["hpp"];
                $tmp["description"] = $value["product_name"];
                array_push($data_insert, $tmp);
                $total = $value["qty"] * $value["hpp"];
                $grand_total += $total;
            }

            // pembayaran
            $tmp["account_code"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_origin"])['code'];
            $tmp["account_name"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_origin"])['name'];
            $tmp["type"] = "D";
            $tmp["account_category_fkid"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_origin"])['category_id'];
            $tmp["nominal"] = $total;
            $tmp["description"] = "Pembayaran " . $data_transfer["0"]["payment"] . " " . $data_transfer["0"]["bank_name"];
            array_push($data_insert, $tmp);
            // $grouping = $this->group_account($data_insert);           
            $this->db->insert_batch('finance_jurnal_umum', $data_insert);
        }

    }

    function set_jurnal_transfer_in($transfer_id)
    {
        if ($this->cek_subcribe()) {
            // $this->set_jurnal_transfer_out($transfer_id);
            $data_transfer = $this->db->select('
                tc.data_created,
                tp.product_detail_des_fkid as pd_id_in,
                tp.product_detail_fkid as pd_id_out,
                tc.qty_confirm as qty,
                tc.transfer_confirm_id as transfer_confirm_id,
                tc.price_transfer,
                tc.total,
                (select name from products where product_id = pd.product_fkid) as product_name,
                t.outlet_destination_fkid as outlet_des_id,
                t.outlet_origin_fkid as outlet_origin_id,
                pd.price_buy as hpp,
                t.grand_total,
                t.bank_destination,
                t.payment,
                tp.markup,
                tp.price,
                ifnull((select name from payment_media_bank where bank_id=t.bank_destination),"") as bank_name
        
            ')
                ->from("transfer_products tp")
                ->join("transfer_confirm tc", "tc.transfer_product_fkid=tp.transfer_product_id", "left")
                ->join("transfer t", "t.transfer_id=tp.transfer_fkid", "left")
                ->join("products_detail pd", "pd.product_detail_id=tp.product_detail_des_fkid", "left")
                ->where("tp.transfer_fkid", $transfer_id)
                ->get()->result_array();
            // print_r($data_transfer);die;
            $date = current_millis();
            $tmp = [
                'trans_type' => "", //transfer in
                'trans_id' => $data_transfer[0]['transfer_confirm_id'],
                'account_code' => "",
                'account_name' => "",
                'type' => '',
                'nominal' => "",
                'description' => "",
                'account_category_fkid' => "",
                'outlet_fkid' => "",
                'admin_fkid' => $this->session->userdata('admin_id'),
                'trans_created' => $data_transfer[0]['data_created'],
                'created_at' => $date,
                // 'updated_at' => $date,
                'data_status' => '1',
                'info' => json_encode(["user_input" => $this->session->userdata("user_name")])
            ];

            // data jurnal
            $data_in = [];
            $data_out = [];
            $grand_total = 0;
            $total_sub_total = 0;
            // persediaan bahan jadi
            // print_r($data_transfer);die();
            foreach ($data_transfer as $key => $value) {
                $tmp["trans_type"] = "8";
                $tmp["account_code"] = $this->get_accunt_product($value['pd_id_in'])['account_code'];
                $tmp["account_name"] = $this->get_accunt_product($value['pd_id_in'])['account_name'];
                $tmp["type"] = "D";
                $tmp["account_category_fkid"] = $this->get_accunt_product($value['pd_id_in'])['category_id'];
                $tmp["nominal"] = $value["qty"] * $value["price"];
                $tmp["description"] = $value["product_name"];
                $tmp["outlet_fkid"] = $value["outlet_des_id"];
                array_push($data_in, $tmp);

                $tmp["trans_type"] = "20";
                $tmp["account_code"] = $this->get_accunt_product($value['pd_id_out'])['account_code'];
                $tmp["account_name"] = $this->get_accunt_product($value['pd_id_out'])['account_name'];
                $tmp["type"] = "K";
                $tmp["account_category_fkid"] = $this->get_accunt_product($value['pd_id_out'])['category_id'];
                $tmp["nominal"] = $value["qty"] * $value["price"];
                $tmp["description"] = $value["product_name"];
                $tmp["outlet_fkid"] = $value["outlet_origin_id"];
                array_push($data_out, $tmp);

                $total = $value["qty"] * $value["price_transfer"];
                $sub_total = $value["qty"] * $value["price"];
                $total_sub_total += $sub_total;
                $grand_total += $total;
            }

            if ($total_sub_total != $grand_total) {
                $selisih = $grand_total - $total_sub_total;
                // selisih kredit
                $tmp["trans_type"] = "8";
                $tmp["account_code"] = $this->account('selisih_persediaan')->account_code;
                $tmp["account_name"] = $this->account('selisih_persediaan')->account_name;
                $tmp["type"] = ($selisih < 0) ? "K" : "D";
                $tmp["account_category_fkid"] = $this->account('selisih_persediaan')->category_id;
                $tmp["nominal"] = ($selisih < 0) ? $selisih * -1 : $selisih;
                $tmp["description"] = "Total selisih";
                $tmp["outlet_fkid"] = $data_transfer[0]["outlet_des_id"];
                array_push($data_in, $tmp);

                // selisih debit
                $tmp["trans_type"] = "20";
                $tmp["account_code"] = $this->account('selisih_persediaan')->account_code;
                $tmp["account_name"] = $this->account('selisih_persediaan')->account_name;
                $tmp["type"] = ($selisih < 0) ? "D" : "K";
                $tmp["account_category_fkid"] = $this->account('selisih_persediaan')->category_id;
                $tmp["nominal"] = ($selisih < 0) ? $selisih * -1 : $selisih;
                $tmp["description"] = "Total selisih";
                $tmp["outlet_fkid"] = $data_transfer[0]["outlet_origin_id"];
                array_push($data_out, $tmp);
            }

            // pembayaran
            $tmp["trans_type"] = "8";
            $tmp["account_code"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_destination"])['code'];
            $tmp["account_name"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_destination"])['name'];
            $tmp["type"] = "K";
            $tmp["account_category_fkid"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_destination"])['category_id'];
            $tmp["nominal"] = $grand_total;
            $tmp["description"] = "Pembayaran " . $data_transfer[0]["payment"] . " " . $data_transfer[0]["bank_name"];
            $tmp["outlet_fkid"] = $data_transfer[0]["outlet_des_id"];
            array_push($data_in, $tmp);

            $tmp["trans_type"] = "20";
            $tmp["account_code"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_destination"])['code'];
            $tmp["account_name"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_destination"])['name'];
            $tmp["type"] = "D";
            $tmp["account_category_fkid"] = $this->get_payment($data_transfer[0]["payment"], $data_transfer[0]["bank_destination"])['category_id'];
            $tmp["nominal"] = $grand_total;
            $tmp["description"] = "Pembayaran " . $data_transfer[0]["payment"] . " " . $data_transfer[0]["bank_name"];
            $tmp["outlet_fkid"] = $data_transfer[0]["outlet_origin_id"];
            array_push($data_out, $tmp);

            $grouping_in = $this->group_account($data_in);
            $grouping_out = $this->group_account($data_out);
            $this->db->insert_batch('finance_jurnal_umum', $data_in);
            $this->db->insert_batch('finance_jurnal_umum', $data_out);
        }

    }

    function jurnal_purchase_update($purchase_id)
    {
        if ($this->cek_subcribe()) {
            $this->db->trans_start();
            // del old purchase
            $this->db->where(['trans_type' => 2, 'trans_id' => $purchase_id]);
            $this->db->delete('finance_jurnal_umum');

            $this->db->where(['trans_type' => 14, 'trans_id' => $purchase_id]);
            $this->db->delete('finance_jurnal_umum');

            // insert new purchase
            $this->jurnal_purchase_v2($purchase_id);

            //data confirm
            // data transfer confirm
            $data_confirm = $this->db->select('pc.purchase_confrim_id')
                ->from('purchase_confrim pc')
                ->join('purchase_products pp', 'pp.purchase_products_id=pc.purchase_product_fkid')
                ->where('pp.purchase_fkid', $purchase_id)
                ->get()->result_array();
            $arr_id_confirm = [];
            // print_r($data_confirm);die;
            foreach ($data_confirm as $confirm) {
                // del all purchas confirm history jurnal
                $this->db->where('trans_type', 5);
                $this->db->where("SUBSTRING_INDEX(trans_id,'.',-1)", $confirm['purchase_confrim_id']);
                $this->db->delete('finance_jurnal_umum');

                array_push($arr_id_confirm, $confirm['purchase_confrim_id']);
            }
            // print_r($arr_id_confirm);die;
            $this->jurnal_purchase_confirm($arr_id_confirm);

            // update debt payment
            $data_debt = $this->db->select('debt_payment_id')
                ->from('debt_payment')
                ->where('purchase_fkid', $purchase_id)
                ->get()->result_array();

            foreach ($data_debt as $debt) {
                // delete jurnal debt
                $this->db->where("SUBSTRING_INDEX(trans_id,'.',-1)", $debt['debt_payment_id']);
                $this->db->where('trans_type', 14);
                $this->db->delete('finance_jurnal_umum');

                $this->jurnal_debit_pay($debt['debt_payment_id']);
            }

            // PURCHASE RETUR
            $this->update_retur($purchase_id);

            $res = $this->db->trans_complete();
        }
    }

    function jurnal_transfer_update($transfer_id)
    {
        if ($this->cek_subcribe()) {
            $this->db->trans_start();

            $data_transfer_confirm = $this->db->select('
                tf.transfer_confirm_id
            ')
                ->from('transfer_confirm tf')
                ->join('transfer_products tp', 'tp.transfer_product_id=tf.transfer_product_fkid', 'left')
                ->where('tp.transfer_fkid', $transfer_id)
                ->get()->result_array();


            // delete jurnal transfer old
            foreach ($data_transfer_confirm as $key) {
                $this->db->where("trans_type in ('8','20')");
                $this->db->where("trans_id", $key['transfer_confirm_id']);
                $this->db->delete('finance_jurnal_umum');
            }
            // insert new jurnal
            $this->set_jurnal_transfer_in($transfer_id);

            $res = $this->db->trans_complete();
        }
    }

    function jurnal_oprasional_update($op_id)
    {
        if ($this->cek_subcribe()) {
            // hapus transaksi jurnal op
            $this->db->trans_start();
            $this->db->where('"SUBSTRING_INDEX(trans_id,' . ',-1)"', $op_id);
            $this->db->where('trans_type', 13);
            $this->db->delete('finance_jurnal_umum');

            // insert new op
            $this->jurnal_oprasional($op_id);
            $res = $this->db->trans_complete();
        }
    }


    function jurnal_oprasional_del($op_id)
    {
        if ($this->cek_subcribe()) {
            // hapus transaksi jurnal op
            $this->db->trans_start();
            $this->db->where('"SUBSTRING_INDEX(trans_id,' . ',-1)"', $op_id);
            $this->db->where('trans_type', 13);
            $this->db->delete('finance_jurnal_umum');
            $res = $this->db->trans_complete();
        }
    }


}

/* End of file M_jurnal_umum.php */
